"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uploadthing";
exports.ids = ["vendor-chunks/uploadthing"];
exports.modules = {

/***/ "(ssr)/./node_modules/uploadthing/client/index.js":
/*!**************************************************!*\
  !*** ./node_modules/uploadthing/client/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadAbortedError: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError),\n/* harmony export */   genUploader: () => (/* binding */ genUploader),\n/* harmony export */   generateClientDropzoneAccept: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateClientDropzoneAccept),\n/* harmony export */   generateMimeTypes: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateMimeTypes),\n/* harmony export */   generatePermittedFileTypes: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generatePermittedFileTypes),\n/* harmony export */   isValidFileSize: () => (/* binding */ isValidFileSize),\n/* harmony export */   isValidFileType: () => (/* binding */ isValidFileType),\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/* harmony import */ var effect_Array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! effect/Array */ \"(ssr)/./node_modules/effect/dist/esm/Array.js\");\n/* harmony import */ var effect_Function__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! effect/Function */ \"(ssr)/./node_modules/effect/dist/esm/Function.js\");\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Micro */ \"(ssr)/./node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var effect_Option__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! effect/Option */ \"(ssr)/./node_modules/effect/dist/esm/Option.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uploadthing/shared */ \"(ssr)/./node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var std_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! std-env */ \"(ssr)/./node_modules/std-env/dist/index.mjs\");\n\n\n\n\n\n\n\n\nvar version$1 = \"6.13.2\";\n\nconst uploadMultipartWithProgress = (file, presigned, opts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        let uploadedBytes = 0;\n        const etags = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.forEach(presigned.urls, (url, index)=>{\n            const offset = presigned.chunkSize * index;\n            const end = Math.min(offset + presigned.chunkSize, file.size);\n            const chunk = file.slice(offset, end);\n            return uploadPart({\n                url,\n                chunk: chunk,\n                contentDisposition: presigned.contentDisposition,\n                fileType: file.type,\n                fileName: file.name,\n                onProgress: (delta)=>{\n                    uploadedBytes += delta;\n                    const percent = uploadedBytes / file.size * 100;\n                    opts.onUploadProgress?.({\n                        file: file.name,\n                        progress: percent\n                    });\n                }\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.andThen((tag)=>({\n                    tag,\n                    partNumber: index + 1\n                })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.retry({\n                while: (error)=>error instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError,\n                times: std_env__WEBPACK_IMPORTED_MODULE_2__.isTest ? 3 : 10,\n                delay: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.exponentialDelay)()\n            }));\n        }, {\n            concurrency: \"inherit\"\n        }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tapExpected((error)=>opts.reportEventToUT(\"failure\", {\n                fileKey: presigned.key,\n                uploadId: presigned.uploadId,\n                fileName: file.name,\n                storageProviderError: String(error)\n            })));\n        // Tell the server that the upload is complete\n        yield* opts.reportEventToUT(\"multipart-complete\", {\n            uploadId: presigned.uploadId,\n            fileKey: presigned.key,\n            etags\n        });\n    });\nconst uploadPart = (opts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.async((resume)=>{\n        const xhr = new XMLHttpRequest();\n        xhr.open(\"PUT\", opts.url, true);\n        xhr.setRequestHeader(\"Content-Type\", opts.fileType);\n        xhr.setRequestHeader(\"Content-Disposition\", (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.contentDisposition)(opts.contentDisposition, opts.fileName));\n        xhr.addEventListener(\"load\", ()=>{\n            const etag = xhr.getResponseHeader(\"Etag\");\n            if (xhr.status >= 200 && xhr.status <= 299 && etag) {\n                return resume(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(etag));\n            }\n            return resume(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError()));\n        });\n        xhr.addEventListener(\"error\", ()=>resume(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError())));\n        let lastProgress = 0;\n        xhr.upload.addEventListener(\"progress\", (e)=>{\n            const delta = e.loaded - lastProgress;\n            lastProgress += delta;\n            opts.onProgress(delta);\n        });\n        xhr.send(opts.chunk);\n        // Cleanup function that runs on interruption\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>xhr.abort());\n    });\n\nconst uploadPresignedPostWithProgress = (file, presigned, opts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.async((resume)=>{\n        const xhr = new XMLHttpRequest();\n        xhr.open(\"POST\", presigned.url);\n        xhr.setRequestHeader(\"Accept\", \"application/xml\");\n        xhr.upload.addEventListener(\"progress\", ({ loaded, total })=>{\n            opts.onUploadProgress?.({\n                file: file.name,\n                progress: loaded / total * 100\n            });\n        });\n        xhr.addEventListener(\"load\", ()=>resume(xhr.status >= 200 && xhr.status < 300 ? effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(null) : opts.reportEventToUT(\"failure\", {\n                fileKey: presigned.key,\n                uploadId: null,\n                fileName: file.name,\n                storageProviderError: xhr.responseText\n            })));\n        xhr.addEventListener(\"error\", ()=>resume(opts.reportEventToUT(\"failure\", {\n                fileKey: presigned.key,\n                uploadId: null,\n                fileName: file.name\n            })));\n        const formData = new FormData();\n        Object.entries(presigned.fields).forEach(([k, v])=>formData.append(k, v));\n        formData.append(\"file\", file); // File data **MUST GO LAST**\n        xhr.send(formData);\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>{\n            xhr.abort();\n        });\n    });\n\nconst maybeParseResponseXML = (maybeXml)=>{\n    const codeMatch = maybeXml.match(/<Code>(.*?)<\\/Code>/s);\n    const messageMatch = maybeXml.match(/<Message>(.*?)<\\/Message>/s);\n    const code = codeMatch?.[1];\n    const message = messageMatch?.[1];\n    if (!code || !message) return null;\n    return {\n        code: s3CodeToUploadThingCode[code] ?? DEFAULT_ERROR_CODE,\n        message\n    };\n};\n/**\n * Map S3 error codes to UploadThing error codes\n *\n * This is a subset of the S3 error codes, based on what seemed most likely to\n * occur in uploadthing. For a full list of S3 error codes, see:\n * https://docs.aws.amazon.com/AmazonS3/latest/API/ErrorResponses.html\n */ const DEFAULT_ERROR_CODE = \"UPLOAD_FAILED\";\nconst s3CodeToUploadThingCode = {\n    AccessDenied: \"FORBIDDEN\",\n    EntityTooSmall: \"TOO_SMALL\",\n    EntityTooLarge: \"TOO_LARGE\",\n    ExpiredToken: \"FORBIDDEN\",\n    IncorrectNumberOfFilesInPostRequest: \"TOO_MANY_FILES\",\n    InternalError: \"INTERNAL_SERVER_ERROR\",\n    KeyTooLongError: \"KEY_TOO_LONG\",\n    MaxMessageLengthExceeded: \"TOO_LARGE\"\n};\n\nconst createAPIRequestUrl = (config)=>{\n    const url = new URL(config.url);\n    const queryParams = new URLSearchParams(url.search);\n    queryParams.set(\"actionType\", config.actionType);\n    queryParams.set(\"slug\", config.slug);\n    url.search = queryParams.toString();\n    return url;\n};\n/**\n * Creates a \"client\" for reporting events to the UploadThing server via the user's API endpoint.\n * Events are handled in \"./handler.ts starting at L112\"\n */ const createUTReporter = (cfg)=>(type, payload)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n            const url = createAPIRequestUrl({\n                url: cfg.url,\n                slug: cfg.endpoint,\n                actionType: type\n            });\n            let headers = typeof cfg.headers === \"function\" ? cfg.headers() : cfg.headers;\n            if (headers instanceof Promise) {\n                headers = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.promise(()=>headers);\n            }\n            const response = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)(url, {\n                method: \"POST\",\n                body: JSON.stringify(payload),\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"x-uploadthing-package\": cfg.package,\n                    \"x-uploadthing-version\": version$1,\n                    ...headers\n                }\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), /**\n         * We don't _need_ to validate the response here, just cast it for now.\n         * As of now, @effect/schema includes quite a few bytes we cut out by this...\n         * We have \"strong typing\" on the backend that ensures the shape should match.\n         */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(effect_Function__WEBPACK_IMPORTED_MODULE_3__.unsafeCoerce), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.catchTag(\"FetchError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_CLIENT_ERROR\",\n                    message: `Failed to report event \"${type}\" to UploadThing server`,\n                    cause: e\n                }))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.catchTag(\"BadRequestError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getErrorTypeFromStatusCode)(e.status),\n                    message: e.getMessage(),\n                    cause: e.json\n                }))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.catchTag(\"InvalidJson\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_CLIENT_ERROR\",\n                    message: \"Failed to parse response from UploadThing server\",\n                    cause: e\n                }))));\n            switch(type){\n                case \"failure\":\n                    {\n                        // why isn't this narrowed automatically?\n                        const p = payload;\n                        const parsed = maybeParseResponseXML(p.storageProviderError ?? \"\");\n                        if (parsed?.message) {\n                            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                                code: parsed.code,\n                                message: parsed.message\n                            });\n                        } else {\n                            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                                code: \"UPLOAD_FAILED\",\n                                message: `Failed to upload file ${p.fileName} to S3`,\n                                cause: p.storageProviderError\n                            });\n                        }\n                    }\n            }\n            return response;\n        });\n\nconst version = version$1;\n/**\n * Validate that a file is of a valid type given a route config\n * @public\n */ const isValidFileType = (file, routeConfig)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getTypeFromFileName)(file.name, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(routeConfig)).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((type)=>file.type.includes(type)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false)));\n/**\n * Validate that a file is of a valid size given a route config\n * @public\n */ const isValidFileSize = (file, routeConfig)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getTypeFromFileName)(file.name, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(routeConfig)).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap((type)=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fileSizeToBytes)(routeConfig[type].maxFileSize)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((maxFileSize)=>file.size <= maxFileSize), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false)));\n/**\n * Generate a typed uploader for a given FileRouter\n * @public\n */ const genUploader = (initOpts)=>{\n    return (endpoint, opts)=>uploadFilesInternal(endpoint, {\n            ...opts,\n            url: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.resolveMaybeUrlArg)(initOpts?.url),\n            package: initOpts.package,\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n            input: opts.input\n        }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, {\n            fetch: globalThis.fetch.bind(globalThis),\n            baseHeaders: {\n                \"x-uploadthing-version\": version$1,\n                \"x-uploadthing-api-key\": undefined,\n                \"x-uploadthing-fe-package\": initOpts.package,\n                \"x-uploadthing-be-adapter\": undefined\n            }\n        }), (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromiseResult(e, opts.signal ? {\n                signal: opts.signal\n            } : {})).then((result)=>{\n            if (result._tag === \"Right\") {\n                return result.right;\n            } else if (result.left._tag === \"Aborted\") {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError();\n            }\n            throw effect_Micro__WEBPACK_IMPORTED_MODULE_1__.failureSquash(result.left);\n        });\n};\nconst uploadFilesInternal = (endpoint, opts)=>{\n    // classic service right here\n    const reportEventToUT = createUTReporter({\n        endpoint: String(endpoint),\n        package: opts.package,\n        url: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.resolveMaybeUrlArg)(opts.url),\n        headers: opts.headers\n    });\n    return reportEventToUT(\"upload\", {\n        input: \"input\" in opts ? opts.input : null,\n        files: opts.files.map((f)=>({\n                name: f.name,\n                size: f.size,\n                type: f.type\n            }))\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap((responses)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.forEach(responses, (presigned)=>uploadFile(String(endpoint), {\n                ...opts,\n                reportEventToUT\n            }, presigned).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.onAbort(reportEventToUT(\"failure\", {\n                fileKey: presigned.key,\n                uploadId: \"uploadId\" in presigned ? presigned.uploadId : null,\n                fileName: presigned.fileName\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.ignore))), {\n            concurrency: 6\n        })));\n};\nconst isPollingResponse = (input)=>{\n    if (!(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.isObject)(input)) return false;\n    if (input.status === \"done\") return \"callbackData\" in input;\n    return input.status === \"still waiting\";\n};\nconst isPollingDone = (input)=>{\n    return input.status === \"done\";\n};\nconst uploadFile = (slug, opts, presigned)=>effect_Array__WEBPACK_IMPORTED_MODULE_4__.findFirst(opts.files, (file)=>file.name === presigned.fileName).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fromOption, effect_Micro__WEBPACK_IMPORTED_MODULE_1__.mapError(()=>{\n        // eslint-disable-next-line no-console\n        console.error(\"No file found for presigned URL\", presigned);\n        return new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: \"NOT_FOUND\",\n            message: \"No file found for presigned URL\",\n            cause: `Expected file with name ${presigned.fileName} but got '${opts.files.join(\",\")}'`\n        });\n    }), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tap((file)=>opts.onUploadBegin?.({\n            file: file.name\n        })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tap((file)=>\"urls\" in presigned ? uploadMultipartWithProgress(file, presigned, opts) : uploadPresignedPostWithProgress(file, presigned, opts)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.zip((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)(presigned.pollingUrl, {\n        headers: {\n            authorization: presigned.pollingJwt\n        }\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.catchTag(\"BadRequestError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getErrorTypeFromStatusCode)(e.status),\n            message: e.message,\n            cause: e\n        }))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.filterOrFailWith(isPollingResponse, (_)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.FailureUnexpected(\"received a non PollingResponse from the polling endpoint\")), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.filterOrFail(isPollingDone, ()=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError()), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(({ callbackData })=>callbackData), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.retry({\n        while: (res)=>{\n            return res instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError;\n        },\n        delay: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.exponentialDelay)()\n    }), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.when(()=>!opts.skipPolling), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(effect_Option__WEBPACK_IMPORTED_MODULE_5__.getOrNull), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(effect_Function__WEBPACK_IMPORTED_MODULE_3__.unsafeCoerce))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(([file, serverData])=>({\n            name: file.name,\n            size: file.size,\n            key: presigned.key,\n            serverData,\n            url: \"https://utfs.io/f/\" + presigned.key,\n            customId: presigned.customId,\n            type: file.type\n        })));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uploadthing/client/index.js\n");

/***/ })

};
;