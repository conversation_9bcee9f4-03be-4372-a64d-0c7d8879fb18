"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uploadthing";
exports.ids = ["vendor-chunks/uploadthing"];
exports.modules = {

/***/ "(ssr)/./node_modules/uploadthing/client/index.js":
/*!**************************************************!*\
  !*** ./node_modules/uploadthing/client/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadAbortedError: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError),\n/* harmony export */   genUploader: () => (/* binding */ genUploader),\n/* harmony export */   generateClientDropzoneAccept: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateClientDropzoneAccept),\n/* harmony export */   generateMimeTypes: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateMimeTypes),\n/* harmony export */   generatePermittedFileTypes: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generatePermittedFileTypes),\n/* harmony export */   isValidFileSize: () => (/* binding */ isValidFileSize),\n/* harmony export */   isValidFileType: () => (/* binding */ isValidFileType),\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/* harmony import */ var effect_Array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! effect/Array */ \"(ssr)/./node_modules/effect/dist/esm/Array.js\");\n/* harmony import */ var effect_Function__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! effect/Function */ \"(ssr)/./node_modules/effect/dist/esm/Function.js\");\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Micro */ \"(ssr)/./node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var effect_Option__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! effect/Option */ \"(ssr)/./node_modules/effect/dist/esm/Option.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uploadthing/shared */ \"(ssr)/./node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var std_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! std-env */ \"(ssr)/./node_modules/std-env/dist/index.mjs\");\n\n\n\n\n\n\n\n\nvar version$1 = \"6.13.2\";\n\nconst uploadMultipartWithProgress = (file, presigned, opts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        let uploadedBytes = 0;\n        const etags = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.forEach(presigned.urls, (url, index)=>{\n            const offset = presigned.chunkSize * index;\n            const end = Math.min(offset + presigned.chunkSize, file.size);\n            const chunk = file.slice(offset, end);\n            return uploadPart({\n                url,\n                chunk: chunk,\n                contentDisposition: presigned.contentDisposition,\n                fileType: file.type,\n                fileName: file.name,\n                onProgress: (delta)=>{\n                    uploadedBytes += delta;\n                    const percent = uploadedBytes / file.size * 100;\n                    opts.onUploadProgress?.({\n                        file: file.name,\n                        progress: percent\n                    });\n                }\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.andThen((tag)=>({\n                    tag,\n                    partNumber: index + 1\n                })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.retry({\n                while: (error)=>error instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError,\n                times: std_env__WEBPACK_IMPORTED_MODULE_2__.isTest ? 3 : 10,\n                delay: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.exponentialDelay)()\n            }));\n        }, {\n            concurrency: \"inherit\"\n        }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tapExpected((error)=>opts.reportEventToUT(\"failure\", {\n                fileKey: presigned.key,\n                uploadId: presigned.uploadId,\n                fileName: file.name,\n                storageProviderError: String(error)\n            })));\n        // Tell the server that the upload is complete\n        yield* opts.reportEventToUT(\"multipart-complete\", {\n            uploadId: presigned.uploadId,\n            fileKey: presigned.key,\n            etags\n        });\n    });\nconst uploadPart = (opts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.async((resume)=>{\n        const xhr = new XMLHttpRequest();\n        xhr.open(\"PUT\", opts.url, true);\n        xhr.setRequestHeader(\"Content-Type\", opts.fileType);\n        xhr.setRequestHeader(\"Content-Disposition\", (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.contentDisposition)(opts.contentDisposition, opts.fileName));\n        xhr.addEventListener(\"load\", ()=>{\n            const etag = xhr.getResponseHeader(\"Etag\");\n            if (xhr.status >= 200 && xhr.status <= 299 && etag) {\n                return resume(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(etag));\n            }\n            return resume(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError()));\n        });\n        xhr.addEventListener(\"error\", ()=>resume(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError())));\n        let lastProgress = 0;\n        xhr.upload.addEventListener(\"progress\", (e)=>{\n            const delta = e.loaded - lastProgress;\n            lastProgress += delta;\n            opts.onProgress(delta);\n        });\n        xhr.send(opts.chunk);\n        // Cleanup function that runs on interruption\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>xhr.abort());\n    });\n\nconst uploadPresignedPostWithProgress = (file, presigned, opts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.async((resume)=>{\n        const xhr = new XMLHttpRequest();\n        xhr.open(\"POST\", presigned.url);\n        xhr.setRequestHeader(\"Accept\", \"application/xml\");\n        xhr.upload.addEventListener(\"progress\", ({ loaded, total })=>{\n            opts.onUploadProgress?.({\n                file: file.name,\n                progress: loaded / total * 100\n            });\n        });\n        xhr.addEventListener(\"load\", ()=>resume(xhr.status >= 200 && xhr.status < 300 ? effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(null) : opts.reportEventToUT(\"failure\", {\n                fileKey: presigned.key,\n                uploadId: null,\n                fileName: file.name,\n                storageProviderError: xhr.responseText\n            })));\n        xhr.addEventListener(\"error\", ()=>resume(opts.reportEventToUT(\"failure\", {\n                fileKey: presigned.key,\n                uploadId: null,\n                fileName: file.name\n            })));\n        const formData = new FormData();\n        Object.entries(presigned.fields).forEach(([k, v])=>formData.append(k, v));\n        formData.append(\"file\", file); // File data **MUST GO LAST**\n        xhr.send(formData);\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>{\n            xhr.abort();\n        });\n    });\n\nconst maybeParseResponseXML = (maybeXml)=>{\n    const codeMatch = maybeXml.match(/<Code>(.*?)<\\/Code>/s);\n    const messageMatch = maybeXml.match(/<Message>(.*?)<\\/Message>/s);\n    const code = codeMatch?.[1];\n    const message = messageMatch?.[1];\n    if (!code || !message) return null;\n    return {\n        code: s3CodeToUploadThingCode[code] ?? DEFAULT_ERROR_CODE,\n        message\n    };\n};\n/**\n * Map S3 error codes to UploadThing error codes\n *\n * This is a subset of the S3 error codes, based on what seemed most likely to\n * occur in uploadthing. For a full list of S3 error codes, see:\n * https://docs.aws.amazon.com/AmazonS3/latest/API/ErrorResponses.html\n */ const DEFAULT_ERROR_CODE = \"UPLOAD_FAILED\";\nconst s3CodeToUploadThingCode = {\n    AccessDenied: \"FORBIDDEN\",\n    EntityTooSmall: \"TOO_SMALL\",\n    EntityTooLarge: \"TOO_LARGE\",\n    ExpiredToken: \"FORBIDDEN\",\n    IncorrectNumberOfFilesInPostRequest: \"TOO_MANY_FILES\",\n    InternalError: \"INTERNAL_SERVER_ERROR\",\n    KeyTooLongError: \"KEY_TOO_LONG\",\n    MaxMessageLengthExceeded: \"TOO_LARGE\"\n};\n\nconst createAPIRequestUrl = (config)=>{\n    const url = new URL(config.url);\n    const queryParams = new URLSearchParams(url.search);\n    queryParams.set(\"actionType\", config.actionType);\n    queryParams.set(\"slug\", config.slug);\n    url.search = queryParams.toString();\n    return url;\n};\n/**\n * Creates a \"client\" for reporting events to the UploadThing server via the user's API endpoint.\n * Events are handled in \"./handler.ts starting at L112\"\n */ const createUTReporter = (cfg)=>(type, payload)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n            const url = createAPIRequestUrl({\n                url: cfg.url,\n                slug: cfg.endpoint,\n                actionType: type\n            });\n            let headers = typeof cfg.headers === \"function\" ? cfg.headers() : cfg.headers;\n            if (headers instanceof Promise) {\n                headers = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.promise(()=>headers);\n            }\n            const response = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)(url, {\n                method: \"POST\",\n                body: JSON.stringify(payload),\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"x-uploadthing-package\": cfg.package,\n                    \"x-uploadthing-version\": version$1,\n                    ...headers\n                }\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), /**\n         * We don't _need_ to validate the response here, just cast it for now.\n         * As of now, @effect/schema includes quite a few bytes we cut out by this...\n         * We have \"strong typing\" on the backend that ensures the shape should match.\n         */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(effect_Function__WEBPACK_IMPORTED_MODULE_3__.unsafeCoerce), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.catchTag(\"FetchError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_CLIENT_ERROR\",\n                    message: `Failed to report event \"${type}\" to UploadThing server`,\n                    cause: e\n                }))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.catchTag(\"BadRequestError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getErrorTypeFromStatusCode)(e.status),\n                    message: e.getMessage(),\n                    cause: e.json\n                }))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.catchTag(\"InvalidJson\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_CLIENT_ERROR\",\n                    message: \"Failed to parse response from UploadThing server\",\n                    cause: e\n                }))));\n            switch(type){\n                case \"failure\":\n                    {\n                        // why isn't this narrowed automatically?\n                        const p = payload;\n                        const parsed = maybeParseResponseXML(p.storageProviderError ?? \"\");\n                        if (parsed?.message) {\n                            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                                code: parsed.code,\n                                message: parsed.message\n                            });\n                        } else {\n                            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                                code: \"UPLOAD_FAILED\",\n                                message: `Failed to upload file ${p.fileName} to S3`,\n                                cause: p.storageProviderError\n                            });\n                        }\n                    }\n            }\n            return response;\n        });\n\nconst version = version$1;\n/**\n * Validate that a file is of a valid type given a route config\n * @public\n */ const isValidFileType = (file, routeConfig)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getTypeFromFileName)(file.name, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(routeConfig)).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((type)=>file.type.includes(type)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false)));\n/**\n * Validate that a file is of a valid size given a route config\n * @public\n */ const isValidFileSize = (file, routeConfig)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getTypeFromFileName)(file.name, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(routeConfig)).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap((type)=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fileSizeToBytes)(routeConfig[type].maxFileSize)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((maxFileSize)=>file.size <= maxFileSize), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false)));\n/**\n * Generate a typed uploader for a given FileRouter\n * @public\n */ const genUploader = (initOpts)=>{\n    return (endpoint, opts)=>uploadFilesInternal(endpoint, {\n            ...opts,\n            url: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.resolveMaybeUrlArg)(initOpts?.url),\n            package: initOpts.package,\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n            input: opts.input\n        }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, {\n            fetch: globalThis.fetch.bind(globalThis),\n            baseHeaders: {\n                \"x-uploadthing-version\": version$1,\n                \"x-uploadthing-api-key\": undefined,\n                \"x-uploadthing-fe-package\": initOpts.package,\n                \"x-uploadthing-be-adapter\": undefined\n            }\n        }), (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromiseResult(e, opts.signal ? {\n                signal: opts.signal\n            } : {})).then((result)=>{\n            if (result._tag === \"Right\") {\n                return result.right;\n            } else if (result.left._tag === \"Aborted\") {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError();\n            }\n            throw effect_Micro__WEBPACK_IMPORTED_MODULE_1__.failureSquash(result.left);\n        });\n};\nconst uploadFilesInternal = (endpoint, opts)=>{\n    // classic service right here\n    const reportEventToUT = createUTReporter({\n        endpoint: String(endpoint),\n        package: opts.package,\n        url: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.resolveMaybeUrlArg)(opts.url),\n        headers: opts.headers\n    });\n    return reportEventToUT(\"upload\", {\n        input: \"input\" in opts ? opts.input : null,\n        files: opts.files.map((f)=>({\n                name: f.name,\n                size: f.size,\n                type: f.type\n            }))\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap((responses)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.forEach(responses, (presigned)=>uploadFile(String(endpoint), {\n                ...opts,\n                reportEventToUT\n            }, presigned).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.onAbort(reportEventToUT(\"failure\", {\n                fileKey: presigned.key,\n                uploadId: \"uploadId\" in presigned ? presigned.uploadId : null,\n                fileName: presigned.fileName\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.ignore))), {\n            concurrency: 6\n        })));\n};\nconst isPollingResponse = (input)=>{\n    if (!(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.isObject)(input)) return false;\n    if (input.status === \"done\") return \"callbackData\" in input;\n    return input.status === \"still waiting\";\n};\nconst isPollingDone = (input)=>{\n    return input.status === \"done\";\n};\nconst uploadFile = (slug, opts, presigned)=>effect_Array__WEBPACK_IMPORTED_MODULE_4__.findFirst(opts.files, (file)=>file.name === presigned.fileName).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fromOption, effect_Micro__WEBPACK_IMPORTED_MODULE_1__.mapError(()=>{\n        // eslint-disable-next-line no-console\n        console.error(\"No file found for presigned URL\", presigned);\n        return new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: \"NOT_FOUND\",\n            message: \"No file found for presigned URL\",\n            cause: `Expected file with name ${presigned.fileName} but got '${opts.files.join(\",\")}'`\n        });\n    }), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tap((file)=>opts.onUploadBegin?.({\n            file: file.name\n        })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tap((file)=>\"urls\" in presigned ? uploadMultipartWithProgress(file, presigned, opts) : uploadPresignedPostWithProgress(file, presigned, opts)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.zip((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)(presigned.pollingUrl, {\n        headers: {\n            authorization: presigned.pollingJwt\n        }\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.catchTag(\"BadRequestError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getErrorTypeFromStatusCode)(e.status),\n            message: e.message,\n            cause: e\n        }))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.filterOrFailWith(isPollingResponse, (_)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.FailureUnexpected(\"received a non PollingResponse from the polling endpoint\")), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.filterOrFail(isPollingDone, ()=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError()), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(({ callbackData })=>callbackData), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.retry({\n        while: (res)=>{\n            return res instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError;\n        },\n        delay: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.exponentialDelay)()\n    }), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.when(()=>!opts.skipPolling), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(effect_Option__WEBPACK_IMPORTED_MODULE_5__.getOrNull), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(effect_Function__WEBPACK_IMPORTED_MODULE_3__.unsafeCoerce))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(([file, serverData])=>({\n            name: file.name,\n            size: file.size,\n            key: presigned.key,\n            serverData,\n            url: \"https://utfs.io/f/\" + presigned.key,\n            customId: presigned.customId,\n            type: file.type\n        })));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uploadthing/client/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/uploadthing/internal/types.js":
/*!****************************************************!*\
  !*** ./node_modules/uploadthing/internal/types.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UTFiles: () => (/* binding */ UTFiles),\n/* harmony export */   VALID_ACTION_TYPES: () => (/* binding */ VALID_ACTION_TYPES),\n/* harmony export */   VALID_UT_HOOKS: () => (/* binding */ VALID_UT_HOOKS),\n/* harmony export */   isActionType: () => (/* binding */ isActionType),\n/* harmony export */   isUploadThingHook: () => (/* binding */ isUploadThingHook)\n/* harmony export */ });\n/**\n * Marker used to append a `customId` to the incoming file data in `.middleware()`\n * @example\n * ```ts\n * .middleware((opts) => {\n *   return {\n *     [UTFiles]: opts.files.map((file) => ({\n *       ...file,\n *       customId: generateId(),\n *     }))\n *   };\n * })\n * ```\n */ const UTFiles = Symbol(\"uploadthing-custom-id-symbol\");\n/**\n * Valid options for the `?actionType` query param\n */ const VALID_ACTION_TYPES = [\n    \"upload\",\n    \"failure\",\n    \"multipart-complete\"\n];\nconst isActionType = (input)=>typeof input === \"string\" && VALID_ACTION_TYPES.includes(input);\n/**\n * Valid options for the `uploadthing-hook` header\n * for requests coming from UT server\n */ const VALID_UT_HOOKS = [\n    \"callback\"\n];\nconst isUploadThingHook = (input)=>typeof input === \"string\" && VALID_UT_HOOKS.includes(input);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdXBsb2FkdGhpbmcvaW50ZXJuYWwvdHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFd0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL2ludGVybmFsL3R5cGVzLmpzPzdiMWQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNYXJrZXIgdXNlZCB0byBhcHBlbmQgYSBgY3VzdG9tSWRgIHRvIHRoZSBpbmNvbWluZyBmaWxlIGRhdGEgaW4gYC5taWRkbGV3YXJlKClgXG4gKiBAZXhhbXBsZVxuICogYGBgdHNcbiAqIC5taWRkbGV3YXJlKChvcHRzKSA9PiB7XG4gKiAgIHJldHVybiB7XG4gKiAgICAgW1VURmlsZXNdOiBvcHRzLmZpbGVzLm1hcCgoZmlsZSkgPT4gKHtcbiAqICAgICAgIC4uLmZpbGUsXG4gKiAgICAgICBjdXN0b21JZDogZ2VuZXJhdGVJZCgpLFxuICogICAgIH0pKVxuICogICB9O1xuICogfSlcbiAqIGBgYFxuICovIGNvbnN0IFVURmlsZXMgPSBTeW1ib2woXCJ1cGxvYWR0aGluZy1jdXN0b20taWQtc3ltYm9sXCIpO1xuLyoqXG4gKiBWYWxpZCBvcHRpb25zIGZvciB0aGUgYD9hY3Rpb25UeXBlYCBxdWVyeSBwYXJhbVxuICovIGNvbnN0IFZBTElEX0FDVElPTl9UWVBFUyA9IFtcbiAgICBcInVwbG9hZFwiLFxuICAgIFwiZmFpbHVyZVwiLFxuICAgIFwibXVsdGlwYXJ0LWNvbXBsZXRlXCJcbl07XG5jb25zdCBpc0FjdGlvblR5cGUgPSAoaW5wdXQpPT50eXBlb2YgaW5wdXQgPT09IFwic3RyaW5nXCIgJiYgVkFMSURfQUNUSU9OX1RZUEVTLmluY2x1ZGVzKGlucHV0KTtcbi8qKlxuICogVmFsaWQgb3B0aW9ucyBmb3IgdGhlIGB1cGxvYWR0aGluZy1ob29rYCBoZWFkZXJcbiAqIGZvciByZXF1ZXN0cyBjb21pbmcgZnJvbSBVVCBzZXJ2ZXJcbiAqLyBjb25zdCBWQUxJRF9VVF9IT09LUyA9IFtcbiAgICBcImNhbGxiYWNrXCJcbl07XG5jb25zdCBpc1VwbG9hZFRoaW5nSG9vayA9IChpbnB1dCk9PnR5cGVvZiBpbnB1dCA9PT0gXCJzdHJpbmdcIiAmJiBWQUxJRF9VVF9IT09LUy5pbmNsdWRlcyhpbnB1dCk7XG5cbmV4cG9ydCB7IFVURmlsZXMsIFZBTElEX0FDVElPTl9UWVBFUywgVkFMSURfVVRfSE9PS1MsIGlzQWN0aW9uVHlwZSwgaXNVcGxvYWRUaGluZ0hvb2sgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/uploadthing/internal/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/uploadthing/next/index.js":
/*!************************************************!*\
  !*** ./node_modules/uploadthing/next/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UTFiles: () => (/* reexport safe */ _internal_types_js__WEBPACK_IMPORTED_MODULE_0__.UTFiles),\n/* harmony export */   createNextRouteHandler: () => (/* binding */ createNextRouteHandler),\n/* harmony export */   createRouteHandler: () => (/* binding */ createRouteHandler),\n/* harmony export */   createUploadthing: () => (/* binding */ createUploadthing)\n/* harmony export */ });\n/* harmony import */ var _server_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/index.js */ \"(rsc)/./node_modules/uploadthing/server/index.js\");\n/* harmony import */ var _internal_types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/types.js */ \"(rsc)/./node_modules/uploadthing/internal/types.js\");\n\n\n\nfunction defaultErrorFormatter(error) {\n    return {\n        message: error.message\n    };\n}\n\nfunction internalCreateBuilder(initDef = {}) {\n    const _def = {\n        // Default router config\n        routerConfig: {\n            image: {\n                maxFileSize: \"4MB\"\n            }\n        },\n        inputParser: {\n            parse: ()=>undefined,\n            _input: undefined,\n            _output: undefined\n        },\n        middleware: ()=>({}),\n        onUploadError: ()=>({}),\n        errorFormatter: initDef.errorFormatter ?? defaultErrorFormatter,\n        // Overload with properties passed in\n        ...initDef\n    };\n    return {\n        input (userParser) {\n            return internalCreateBuilder({\n                ..._def,\n                inputParser: userParser\n            });\n        },\n        middleware (userMiddleware) {\n            return internalCreateBuilder({\n                ..._def,\n                middleware: userMiddleware\n            });\n        },\n        onUploadComplete (userUploadComplete) {\n            return {\n                _def,\n                resolver: userUploadComplete\n            };\n        },\n        onUploadError (userOnUploadError) {\n            return internalCreateBuilder({\n                ..._def,\n                onUploadError: userOnUploadError\n            });\n        }\n    };\n}\nfunction createBuilder(opts) {\n    return (input)=>{\n        return internalCreateBuilder({\n            routerConfig: input,\n            ...opts\n        });\n    };\n}\n\nconst createUploadthing = (opts)=>createBuilder(opts);\nconst createRouteHandler = (opts)=>{\n    const handlers = (0,_server_index_js__WEBPACK_IMPORTED_MODULE_1__.INTERNAL_DO_NOT_USE_createRouteHandlerCore)(opts, \"nextjs-app\");\n    return {\n        POST: (req)=>handlers.POST(req),\n        GET: (req)=>handlers.GET(req)\n    };\n};\n/**\n * @deprecated Use {@link createRouteHandler} instead\n */ const createNextRouteHandler = createRouteHandler;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/uploadthing/next/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/uploadthing/server/index.js":
/*!**************************************************!*\
  !*** ./node_modules/uploadthing/server/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INTERNAL_DO_NOT_USE_createRouteHandlerCore: () => (/* binding */ INTERNAL_DO_NOT_USE_createRouteHandlerCore),\n/* harmony export */   UTApi: () => (/* binding */ UTApi),\n/* harmony export */   UTFile: () => (/* binding */ UTFile),\n/* harmony export */   UTFiles: () => (/* reexport safe */ _internal_types_js__WEBPACK_IMPORTED_MODULE_2__.UTFiles),\n/* harmony export */   UploadThingError: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError),\n/* harmony export */   createRouteHandler: () => (/* binding */ createRouteHandler),\n/* harmony export */   createServerHandler: () => (/* binding */ createServerHandler),\n/* harmony export */   createUploadthing: () => (/* binding */ createUploadthing),\n/* harmony export */   extractRouterConfig: () => (/* binding */ extractRouterConfig)\n/* harmony export */ });\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uploadthing/shared */ \"(rsc)/./node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @effect/schema/Schema */ \"(rsc)/./node_modules/@effect/schema/dist/esm/Schema.js\");\n/* harmony import */ var effect_Effect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! effect/Effect */ \"(rsc)/./node_modules/effect/dist/esm/Effect.js\");\n/* harmony import */ var effect_Duration__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! effect/Duration */ \"(rsc)/./node_modules/effect/dist/esm/Duration.js\");\n/* harmony import */ var effect_Schedule__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! effect/Schedule */ \"(rsc)/./node_modules/effect/dist/esm/Schedule.js\");\n/* harmony import */ var consola_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! consola/core */ \"(rsc)/./node_modules/consola/dist/core.mjs\");\n/* harmony import */ var effect_Logger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! effect/Logger */ \"(rsc)/./node_modules/effect/dist/esm/Logger.js\");\n/* harmony import */ var effect_LogLevel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! effect/LogLevel */ \"(rsc)/./node_modules/effect/dist/esm/LogLevel.js\");\n/* harmony import */ var std_env__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! std-env */ \"(rsc)/./node_modules/std-env/dist/index.mjs\");\n/* harmony import */ var effect_Context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! effect/Context */ \"(rsc)/./node_modules/effect/dist/esm/Context.js\");\n/* harmony import */ var effect_Data__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! effect/Data */ \"(rsc)/./node_modules/effect/dist/esm/Data.js\");\n/* harmony import */ var _internal_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../internal/types.js */ \"(rsc)/./node_modules/uploadthing/internal/types.js\");\n/* harmony import */ var _uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @uploadthing/mime-types */ \"(rsc)/./node_modules/@uploadthing/mime-types/dist/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar version = \"6.13.2\";\n\nfunction defaultErrorFormatter(error) {\n    return {\n        message: error.message\n    };\n}\nfunction formatError(error, router) {\n    const errorFormatter = router[Object.keys(router)[0]]?._def.errorFormatter ?? defaultErrorFormatter;\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n    return errorFormatter(error);\n}\n\nconst ContentDispositionSchema = _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Literal(..._uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.ValidContentDispositions);\n_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Literal(..._uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.ValidACLs);\n/**\n * =============================================================================\n * ======================== File Type Hierarchy ===============================\n * =============================================================================\n */ /**\n * Properties from the web File object, this is what the client sends when initiating an upload\n */ class FileUploadData extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"FileUploadData\")({\n    name: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    size: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number,\n    type: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String\n}) {\n}\n/**\n * `.middleware()` can add a customId to the incoming file data\n */ class FileUploadDataWithCustomId extends FileUploadData.extend(\"FileUploadDataWithCustomId\")({\n    customId: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.NullOr(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String)\n}) {\n}\n/**\n * When files are uploaded, we get back a key and a URL for the file\n */ class UploadedFileData extends FileUploadDataWithCustomId.extend(\"UploadedFileData\")({\n    key: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    url: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String\n}) {\n}\n/**\n * =============================================================================\n * ======================== Server Response Schemas ============================\n * =============================================================================\n */ class PresignedBase extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"PresignedBaseSchema\")({\n    key: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    fileName: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    fileType: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    fileUrl: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    pollingJwt: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    pollingUrl: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    contentDisposition: ContentDispositionSchema,\n    customId: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.NullOr(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String)\n}) {\n}\nclass MPUResponse extends PresignedBase.extend(\"MPUResponseSchema\")({\n    urls: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Array(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String),\n    uploadId: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    chunkSize: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number,\n    chunkCount: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number\n}) {\n}\nclass PSPResponse extends PresignedBase.extend(\"PSPResponseSchema\")({\n    url: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    fields: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Record(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String, _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String)\n}) {\n}\nconst PresignedURLResponse = _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Array(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Union(PSPResponse, MPUResponse));\nclass PollUploadResponse extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"PollUploadResponse\")({\n    status: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    fileData: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.optional(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Struct({\n        fileKey: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.NullOr(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String),\n        fileName: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n        fileSize: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number,\n        fileType: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n        metadata: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.NullOr(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String),\n        customId: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.NullOr(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String),\n        callbackUrl: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.optional(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String),\n        callbackSlug: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.optional(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String)\n    }))\n}) {\n}\nclass FailureCallbackResponse extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"FailureCallbackResponse\")({\n    success: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Boolean,\n    message: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.optional(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String)\n}) {\n}\nclass ServerCallbackPostResponse extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"ServerCallbackPostResponse\")({\n    status: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String\n}) {\n}\n/**\n * =============================================================================\n * ======================== Client Action Payloads ============================\n * =============================================================================\n */ class UploadActionPayload extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"UploadActionPayload\")({\n    files: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Array(FileUploadData),\n    input: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Unknown\n}) {\n}\nclass FailureActionPayload extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"FailureActionPayload\")({\n    fileKey: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    uploadId: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.NullOr(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String),\n    storageProviderError: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.optional(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String),\n    fileName: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String\n}) {\n}\nclass MultipartCompleteActionPayload extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"MultipartCompleteActionPayload\")({\n    fileKey: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    uploadId: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n    etags: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Array(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Struct({\n        tag: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n        partNumber: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number\n    }))\n}) {\n}\n\nconst isValidResponse = (response)=>{\n    if (!response.ok) return false;\n    if (response.status >= 400) return false;\n    if (!response.headers.has(\"x-uploadthing-version\")) return false;\n    return true;\n};\nconst conditionalDevServer = (fileKey, apiKey)=>{\n    return effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n        const file = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateUploadThingURL)(`/api/pollUpload/${fileKey}`)).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(PollUploadResponse)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((res)=>res.status === \"done\" && res.fileData ? effect_Effect__WEBPACK_IMPORTED_MODULE_4__.succeed(res.fileData) : effect_Effect__WEBPACK_IMPORTED_MODULE_4__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError())), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.retry({\n            while: (err)=>err instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError,\n            schedule: effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.exponential(10, 4).pipe(// 10ms, 40ms, 160ms, 640ms...\n            effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.union(effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.spaced(1000)), effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.compose(effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.elapsed), effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.whileOutput(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.lessThanOrEqualTo(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.minutes(1))))\n        }), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"RetryError\", ()=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"UPLOAD_FAILED\",\n                message: \"File took too long to upload\"\n            })));\n        let callbackUrl = file.callbackUrl + `?slug=${file.callbackSlug}`;\n        if (!callbackUrl.startsWith(\"http\")) callbackUrl = \"http://\" + callbackUrl;\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logInfo(`SIMULATING FILE UPLOAD WEBHOOK CALLBACK`, callbackUrl);\n        const payload = JSON.stringify({\n            status: \"uploaded\",\n            metadata: JSON.parse(file.metadata ?? \"{}\"),\n            file: new UploadedFileData({\n                url: `https://utfs.io/f/${encodeURIComponent(fileKey)}`,\n                key: fileKey,\n                name: file.fileName,\n                size: file.fileSize,\n                customId: file.customId,\n                type: file.fileType\n            })\n        });\n        const signature = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tryPromise({\n            try: ()=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.signPayload)(payload, apiKey),\n            catch: (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: \"Failed to sign payload\",\n                    cause: e\n                })\n        });\n        const callbackResponse = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)(callbackUrl, {\n            method: \"POST\",\n            body: payload,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"uploadthing-hook\": \"callback\",\n                \"x-uploadthing-signature\": signature\n            }\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"FetchError\", ()=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.succeed(new Response(null, {\n                status: 500\n            }))));\n        if (isValidResponse(callbackResponse)) {\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logInfo(\"Successfully simulated callback for file\", fileKey);\n        } else {\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(`\nFailed to simulate callback for file '${file.fileKey}'. Is your webhook configured correctly?\n  - Make sure the URL '${callbackUrl}' is accessible without any authentication. You can verify this by running 'curl -X POST ${callbackUrl}' in your terminal\n  - Still facing issues? Read https://docs.uploadthing.com/faq for common issues\n`.trim());\n        }\n        return file;\n    });\n};\n\nconst colorize = (str, level)=>{\n    // TODO: Maybe check is shell supports colors\n    switch(level){\n        case \"error\":\n        case \"fatal\":\n            return `\\x1b[41m\\x1b[30m${str}\\x1b[0m`;\n        case \"warn\":\n            return `\\x1b[43m\\x1b[30m${str}\\x1b[0m`;\n        case \"info\":\n        case \"log\":\n            return `\\x1b[44m\\x1b[30m${str}\\x1b[0m`;\n        case \"debug\":\n            return `\\x1b[47m\\x1b[30m${str}\\x1b[0m`;\n        case \"trace\":\n            return `\\x1b[47m\\x1b[30m${str}\\x1b[0m`;\n        case \"success\":\n            return `\\x1b[42m\\x1b[30m${str}\\x1b[0m`;\n        default:\n            return str;\n    }\n};\nconst icons = {\n    fatal: \"⨯\",\n    error: \"⨯\",\n    warn: \"⚠️\",\n    info: \"ℹ\",\n    log: \"ℹ\",\n    debug: \"⚙\",\n    trace: \"→\",\n    success: \"✓\"\n};\nfunction formatStack(stack) {\n    const cwd = \"cwd\" in std_env__WEBPACK_IMPORTED_MODULE_7__.process && typeof std_env__WEBPACK_IMPORTED_MODULE_7__.process.cwd === \"function\" ? std_env__WEBPACK_IMPORTED_MODULE_7__.process.cwd() : \"__UnknownCWD__\";\n    return \"  \" + stack.split(\"\\n\").splice(1).map((l)=>l.trim().replace(\"file://\", \"\").replace(cwd + \"/\", \"\")).join(\"\\n  \");\n}\nfunction formatArgs(args) {\n    const fmtArgs = args.map((arg)=>{\n        if ((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.isObject)(arg) && typeof arg.stack === \"string\") {\n            return arg.message + \"\\n\" + formatStack(arg.stack);\n        }\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return arg;\n    });\n    return fmtArgs.map((arg)=>{\n        if (typeof arg === \"string\") {\n            return arg;\n        }\n        return JSON.stringify(arg, null, 4);\n    });\n}\nconst logger = (0,consola_core__WEBPACK_IMPORTED_MODULE_1__.createConsola)({\n    reporters: [\n        {\n            log: (logObj)=>{\n                const { type, tag, date, args } = logObj;\n                const icon = icons[type];\n                const logPrefix = colorize(` ${icon} ${tag} ${date.toLocaleTimeString()} `, type);\n                const lines = formatArgs(args).join(\" \") // concat all arguments to one space-separated string (like console does)\n                .split(\"\\n\") // split all the newlines (e.g. from logged JSON.stringified objects)\n                .map((l)=>logPrefix + \" \" + l) // prepend the log prefix to each line\n                .join(\"\\n\"); // join all the lines back together\n                // eslint-disable-next-line no-console\n                console.log(lines);\n            }\n        }\n    ],\n    defaults: {\n        tag: \"UPLOADTHING\"\n    }\n});\nconst effectLoggerLevelToConsolaLevel = {\n    All: \"verbose\",\n    Fatal: \"error\",\n    Error: \"error\",\n    Info: \"info\",\n    Debug: \"debug\",\n    Trace: \"trace\",\n    Warning: \"warn\",\n    None: \"silent\"\n};\nconst withMinimalLogLevel = (level = \"info\")=>{\n    logger.level = consola_core__WEBPACK_IMPORTED_MODULE_1__.LogLevels[level];\n    return effect_Logger__WEBPACK_IMPORTED_MODULE_8__.withMinimumLogLevel({\n        silent: effect_LogLevel__WEBPACK_IMPORTED_MODULE_9__.None,\n        error: effect_LogLevel__WEBPACK_IMPORTED_MODULE_9__.Error,\n        warn: effect_LogLevel__WEBPACK_IMPORTED_MODULE_9__.Warning,\n        info: effect_LogLevel__WEBPACK_IMPORTED_MODULE_9__.Info,\n        debug: effect_LogLevel__WEBPACK_IMPORTED_MODULE_9__.Debug,\n        trace: effect_LogLevel__WEBPACK_IMPORTED_MODULE_9__.Trace,\n        verbose: effect_LogLevel__WEBPACK_IMPORTED_MODULE_9__.All\n    }[level]);\n};\nconst ConsolaLogger = effect_Logger__WEBPACK_IMPORTED_MODULE_8__.replace(effect_Logger__WEBPACK_IMPORTED_MODULE_8__.defaultLogger, effect_Logger__WEBPACK_IMPORTED_MODULE_8__.make(({ logLevel, message })=>{\n    // FIXME: Probably log other stuff than just message?\n    logger[effectLoggerLevelToConsolaLevel[logLevel._tag]](message);\n}));\n\nconst uploadMultipart = (file, presigned)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(`Uploading file ${file.name} with ${presigned.urls.length} chunks of size ${presigned.chunkSize} bytes each`);\n        const etags = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.forEach(presigned.urls, (url, index)=>{\n            const offset = presigned.chunkSize * index;\n            const end = Math.min(offset + presigned.chunkSize, file.size);\n            const chunk = file.slice(offset, end);\n            return uploadPart({\n                url,\n                chunk: chunk,\n                contentDisposition: presigned.contentDisposition,\n                contentType: file.type,\n                fileName: file.name,\n                maxRetries: 10,\n                key: presigned.key,\n                uploadId: presigned.uploadId\n            }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((etag)=>({\n                    tag: etag,\n                    partNumber: index + 1\n                })), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"RetryError\", (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.die(e)));\n        }, {\n            concurrency: \"inherit\"\n        });\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"File\", file.name, \"uploaded successfully.\");\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Completing multipart upload...\");\n        yield* completeMultipartUpload(presigned, etags);\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Multipart upload complete.\");\n    });\n/**\n * Used by server uploads where progress is not needed.\n * Uses normal fetch API.\n */ const uploadPart = (opts)=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)(opts.url, {\n        method: \"PUT\",\n        body: opts.chunk,\n        headers: {\n            \"Content-Type\": opts.contentType,\n            \"Content-Disposition\": (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.contentDisposition)(opts.contentDisposition, opts.fileName)\n        }\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((res)=>res.ok && res.headers.get(\"Etag\") ? effect_Effect__WEBPACK_IMPORTED_MODULE_4__.succeed(res.headers.get(\"Etag\")) : effect_Effect__WEBPACK_IMPORTED_MODULE_4__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError())), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.retry({\n        while: (res)=>res instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError,\n        schedule: effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.exponential(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.millis(10), 4).pipe(// 10ms, 40ms, 160ms, 640ms...\n        effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.andThenEither(effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.spaced(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.seconds(1))), effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.compose(effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.elapsed), effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.whileOutput(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.lessThanOrEqualTo(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.minutes(1)))),\n        times: opts.maxRetries\n    }), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapErrorTag(\"RetryError\", ()=>// Max retries exceeded, tell UT server that upload failed\n        abortMultipartUpload({\n            key: opts.key,\n            uploadId: opts.uploadId\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((res)=>{\n            new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"UPLOAD_FAILED\",\n                message: `Failed to upload file ${opts.fileName} to S3`,\n                cause: res\n            });\n        }))));\nconst completeMultipartUpload = (presigned, etags)=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateUploadThingURL)(\"/api/completeMultipart\"), {\n        method: \"POST\",\n        body: JSON.stringify({\n            fileKey: presigned.key,\n            uploadId: presigned.uploadId,\n            etags\n        }),\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Struct({\n        success: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Boolean,\n        message: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.optional(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String)\n    }))), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.withSpan(\"completeMultipartUpload\", {\n        attributes: {\n            etags,\n            presigned\n        }\n    }));\nconst abortMultipartUpload = (presigned)=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateUploadThingURL)(\"/api/failureCallback\"), {\n        method: \"POST\",\n        body: JSON.stringify({\n            fileKey: presigned.key,\n            uploadId: presigned.uploadId\n        }),\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(FailureCallbackResponse)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.withSpan(\"abortMultipartUpload\", {\n        attributes: {\n            presigned\n        }\n    }));\n\nfunction getParseFn(parser) {\n    if (typeof parser.parse === \"function\") {\n        return parser.parse;\n    }\n    throw new Error(\"Invalid parser\");\n}\n\nconst getApiKey = (apiKey)=>{\n    if (apiKey) return apiKey;\n    if (std_env__WEBPACK_IMPORTED_MODULE_7__.process.env.UPLOADTHING_SECRET) return std_env__WEBPACK_IMPORTED_MODULE_7__.process.env.UPLOADTHING_SECRET;\n    return undefined;\n};\nconst getApiKeyOrThrow = (apiKey)=>{\n    const key = getApiKey(apiKey);\n    if (!key?.startsWith(\"sk_\")) {\n        throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: \"MISSING_ENV\",\n            message: \"Missing or invalid API key. API keys must start with `sk_`.\"\n        });\n    }\n    return key;\n};\n\nclass FileSizeMismatch extends effect_Data__WEBPACK_IMPORTED_MODULE_10__.Error {\n    constructor(type, max, actual){\n        const reason = `You uploaded a ${type} file that was ${(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.bytesToFileSize)(actual)}, but the limit for that type is ${max}`;\n        super({\n            reason\n        });\n        this._tag = \"FileSizeMismatch\";\n        this.name = \"FileSizeMismatchError\";\n    }\n}\nclass FileCountMismatch extends effect_Data__WEBPACK_IMPORTED_MODULE_10__.Error {\n    constructor(type, boundtype, bound, actual){\n        const reason = `You uploaded ${actual} file(s) of type '${type}', but the ${boundtype} for that type is ${bound}`;\n        super({\n            reason\n        });\n        this._tag = \"FileCountMismatch\";\n        this.name = \"FileCountMismatchError\";\n    }\n}\n// Verify that the uploaded files doesn't violate the route config,\n// e.g. uploading more videos than allowed, or a file that is larger than allowed.\n// This is double-checked on infra side, but we want to fail early to avoid network latency.\nconst assertFilesMeetConfig = (files, routeConfig)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n        const counts = {};\n        for (const file of files){\n            const type = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getTypeFromFileName)(file.name, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(routeConfig));\n            counts[type] = (counts[type] ?? 0) + 1;\n            const sizeLimit = routeConfig[type]?.maxFileSize;\n            if (!sizeLimit) {\n                return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.InvalidRouteConfigError(type, \"maxFileSize\");\n            }\n            const sizeLimitBytes = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fileSizeToBytes)(sizeLimit);\n            if (file.size > sizeLimitBytes) {\n                return yield* new FileSizeMismatch(type, sizeLimit, file.size);\n            }\n        }\n        for(const _key in counts){\n            const key = _key;\n            const config = routeConfig[key];\n            if (!config) return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.InvalidRouteConfigError(key);\n            const count = counts[key];\n            const min = config.minFileCount;\n            const max = config.maxFileCount;\n            if (min > max) {\n                return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid config during file count - minFileCount > maxFileCount\",\n                    cause: `minFileCount must be less than maxFileCount for key ${key}. got: ${min} > ${max}`\n                });\n            }\n            if (count < min) {\n                return yield* new FileCountMismatch(key, \"minimum\", min, count);\n            }\n            if (count > max) {\n                return yield* new FileCountMismatch(key, \"maximum\", max, count);\n            }\n        }\n        return null;\n    });\nclass RequestInput extends /** #__PURE__ */ effect_Context__WEBPACK_IMPORTED_MODULE_11__.Tag(\"uploadthing/RequestInput\")() {\n}\nconst parseAndValidateRequest = (input, opts, adapter)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n        const req = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.isEffect(input.req) ? input.req : effect_Effect__WEBPACK_IMPORTED_MODULE_4__.succeed(input.req);\n        // Get inputs from query and params\n        const url = new URL(req.url);\n        const headers = req.headers;\n        const params = url.searchParams;\n        const action = params.get(\"actionType\");\n        const slug = params.get(\"slug\");\n        const hook = headers.get(\"uploadthing-hook\");\n        const utFrontendPackage = headers.get(\"x-uploadthing-package\") ?? \"unknown\";\n        const clientVersion = headers.get(\"x-uploadthing-version\");\n        const apiKey = getApiKey(opts.config?.uploadthingSecret);\n        if (clientVersion != null && clientVersion !== version) {\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(`Client version mismatch. Server version: ${version}, Client version: ${clientVersion}`);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Client version mismatch\",\n                cause: `Server version: ${version}, Client version: ${clientVersion}`\n            });\n        }\n        if (!slug) {\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"No slug provided in params:\", params);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"No slug provided in params\"\n            });\n        }\n        if (slug && typeof slug !== \"string\") {\n            const msg = `Expected slug to be of type 'string', got '${typeof slug}'`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"`slug` must be a string\",\n                cause: msg\n            });\n        }\n        if (!apiKey) {\n            const msg = `No secret provided, please set UPLOADTHING_SECRET in your env file or in the config`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"MISSING_ENV\",\n                message: `No secret provided`,\n                cause: msg\n            });\n        }\n        if (!apiKey.startsWith(\"sk_\")) {\n            const msg = `Invalid secret provided, UPLOADTHING_SECRET must start with 'sk_'`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"MISSING_ENV\",\n                message: \"Invalid API key. API keys must start with 'sk_'.\",\n                cause: msg\n            });\n        }\n        if (utFrontendPackage && typeof utFrontendPackage !== \"string\") {\n            const msg = `Expected x-uploadthing-package to be of type 'string', got '${typeof utFrontendPackage}'`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"`x-uploadthing-package` must be a string. eg. '@uploadthing/react'\",\n                cause: msg\n            });\n        }\n        const uploadable = opts.router[slug];\n        if (!uploadable) {\n            const msg = `No file route found for slug ${slug}`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"NOT_FOUND\",\n                message: msg\n            });\n        }\n        if (action && !(0,_internal_types_js__WEBPACK_IMPORTED_MODULE_2__.isActionType)(action)) {\n            const msg = `Expected ${_internal_types_js__WEBPACK_IMPORTED_MODULE_2__.VALID_ACTION_TYPES.map((x)=>`\"${x}\"`).join(\", \").replace(/,(?!.*,)/, \" or\")} but got \"${action}\"`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Invalid action type\", msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                cause: `Invalid action type ${action}`,\n                message: msg\n            });\n        }\n        if (hook && !(0,_internal_types_js__WEBPACK_IMPORTED_MODULE_2__.isUploadThingHook)(hook)) {\n            const msg = `Expected ${_internal_types_js__WEBPACK_IMPORTED_MODULE_2__.VALID_UT_HOOKS.map((x)=>`\"${x}\"`).join(\", \").replace(/,(?!.*,)/, \" or\")} but got \"${hook}\"`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Invalid uploadthing hook\", msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                cause: `Invalid uploadthing hook ${hook}`,\n                message: msg\n            });\n        }\n        if (!action && !hook || action && hook) {\n            const msg = `Exactly one of 'actionType' or 'uploadthing-hook' must be provided`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: msg\n            });\n        }\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"✔︎ All request input is valid\");\n        // FIXME: This should probably provide the full context at once instead of\n        // partially in the `runRequestHandlerAsync` and partially in here...\n        // Ref: https://discord.com/channels/@me/1201977154577891369/1207441839972548669\n        const contextValue = yield* _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext;\n        contextValue.baseHeaders[\"x-uploadthing-api-key\"] = apiKey;\n        contextValue.baseHeaders[\"x-uploadthing-fe-package\"] = utFrontendPackage;\n        contextValue.baseHeaders[\"x-uploadthing-be-adapter\"] = adapter;\n        const { isDev = std_env__WEBPACK_IMPORTED_MODULE_7__.isDevelopment } = opts.config ?? {};\n        if (isDev) yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logInfo(\"UploadThing dev server is now running!\");\n        const base = {\n            req,\n            config: opts.config ?? {},\n            middlewareArgs: input.middlewareArgs,\n            isDev,\n            apiKey,\n            slug,\n            uploadable,\n            hook: null,\n            action: null\n        };\n        return action ? {\n            ...base,\n            action: action\n        } : {\n            ...base,\n            hook: hook\n        };\n    });\n\nconst resolveCallbackUrl = effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n    const { config, req, isDev } = yield* RequestInput;\n    let callbackUrl = new URL(req.url);\n    if (config?.callbackUrl) {\n        callbackUrl = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getFullApiUrl)(config.callbackUrl);\n    } else if (std_env__WEBPACK_IMPORTED_MODULE_7__.process.env.UPLOADTHING_URL) {\n        callbackUrl = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getFullApiUrl)(std_env__WEBPACK_IMPORTED_MODULE_7__.process.env.UPLOADTHING_URL);\n    }\n    if (isDev || !callbackUrl.host.includes(\"localhost\")) {\n        return callbackUrl;\n    }\n    // Production builds have to have a public URL so UT can send webhook\n    // Parse the URL from the headers\n    let parsedFromHeaders = req.headers.get(\"origin\") ?? req.headers.get(\"referer\") ?? req.headers.get(\"host\") ?? req.headers.get(\"x-forwarded-host\");\n    if (parsedFromHeaders && !parsedFromHeaders.includes(\"http\")) {\n        parsedFromHeaders = (req.headers.get(\"x-forwarded-proto\") ?? \"https\") + \"://\" + parsedFromHeaders;\n    }\n    if (!parsedFromHeaders || parsedFromHeaders.includes(\"localhost\")) {\n        // Didn't find a valid URL in the headers, log a warning and use the original url anyway\n        effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logWarning(\"You are using a localhost callback url in production which is not supported.\", \"Read more and learn how to fix it here: https://docs.uploadthing.com/faq#my-callback-runs-in-development-but-not-in-production\");\n        return callbackUrl;\n    }\n    return yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getFullApiUrl)(parsedFromHeaders);\n});\n\n/**\n * Allows adapters to be fully async/await instead of providing services and running Effect programs\n */ const runRequestHandlerAsync = (handler, args, config)=>handler(args).pipe(withMinimalLogLevel(config?.logLevel), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.provide(ConsolaLogger), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, {\n        fetch: config?.fetch ?? globalThis.fetch,\n        baseHeaders: {\n            \"x-uploadthing-version\": version,\n            // These are filled in later in `parseAndValidateRequest`\n            \"x-uploadthing-api-key\": undefined,\n            \"x-uploadthing-be-adapter\": undefined,\n            \"x-uploadthing-fe-package\": undefined\n        }\n    }), asHandlerOutput, effect_Effect__WEBPACK_IMPORTED_MODULE_4__.runPromise);\nconst asHandlerOutput = (effect)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchAll(effect, (error)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.succeed({\n            success: false,\n            error\n        }));\nconst handleRequest = RequestInput.pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(({ action, hook })=>{\n    if (hook === \"callback\") return handleCallbackRequest;\n    switch(action){\n        case \"upload\":\n            return handleUploadAction;\n        case \"multipart-complete\":\n            return handleMultipartCompleteAction;\n        case \"failure\":\n            return handleMultipartFailureAction;\n    }\n}), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.map((output)=>({\n        success: true,\n        ...output\n    })));\nconst buildRequestHandler = (opts, adapter)=>(input)=>handleRequest.pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.provideServiceEffect(RequestInput, parseAndValidateRequest(input, opts, adapter)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTags({\n            InvalidJson: (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: \"An error occured while parsing input/output\",\n                    cause: e\n                }),\n            BadRequestError: (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: e.getMessage(),\n                    cause: e,\n                    data: e.json\n                }),\n            FetchError: (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: typeof e.error === \"string\" ? e.error : e.message,\n                    cause: e,\n                    data: e.error\n                }),\n            ParseError: (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: \"An error occured while parsing input/output\",\n                    cause: e\n                })\n        }), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapError((e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(e.message)));\nconst handleCallbackRequest = effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n    const { req, uploadable, apiKey } = yield* RequestInput;\n    const verified = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tryPromise({\n        try: async ()=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.verifySignature)(await req.clone().text(), req.headers.get(\"x-uploadthing-signature\"), apiKey),\n        catch: ()=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Invalid signature\"\n            })\n    });\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Signature verified:\", verified);\n    if (!verified) {\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Invalid signature\");\n        return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: \"BAD_REQUEST\",\n            message: \"Invalid signature\"\n        });\n    }\n    const requestInput = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.flatMap((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseRequestJson)(req), _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Struct({\n        status: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n        file: UploadedFileData,\n        metadata: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Record(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String, _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Unknown)\n    })));\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Handling callback request with input:\", requestInput);\n    const serverData = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tryPromise({\n        try: async ()=>uploadable.resolver({\n                file: requestInput.file,\n                metadata: requestInput.metadata\n            }),\n        catch: (error)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"INTERNAL_SERVER_ERROR\",\n                message: \"Failed to run onUploadComplete\",\n                cause: error\n            })\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapError((error)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Failed to run onUploadComplete. You probably shouldn't be throwing errors here.\", error)));\n    const payload = {\n        fileKey: requestInput.file.key,\n        callbackData: serverData ?? null\n    };\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"'onUploadComplete' callback finished. Sending response to UploadThing:\", payload);\n    yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateUploadThingURL)(\"/api/serverCallback\"), {\n        method: \"POST\",\n        body: JSON.stringify(payload),\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(ServerCallbackPostResponse)));\n    return {\n        body: null\n    };\n});\nconst runRouteMiddleware = (opts)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n        const { uploadable, middlewareArgs } = yield* RequestInput;\n        const { files, input } = opts;\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Running middleware\");\n        const metadata = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tryPromise({\n            try: async ()=>uploadable._def.middleware({\n                    ...middlewareArgs,\n                    input,\n                    files\n                }),\n            catch: (error)=>error instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError ? error : new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: \"Failed to run middleware\",\n                    cause: error\n                })\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapError((error)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"An error occured in your middleware function\", error)));\n        if (metadata[_internal_types_js__WEBPACK_IMPORTED_MODULE_2__.UTFiles] && metadata[_internal_types_js__WEBPACK_IMPORTED_MODULE_2__.UTFiles].length !== files.length) {\n            const msg = `Expected files override to have the same length as original files, got ${metadata[_internal_types_js__WEBPACK_IMPORTED_MODULE_2__.UTFiles].length} but expected ${files.length}`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Files override must have the same length as files\",\n                cause: msg\n            });\n        }\n        // Attach customIds from middleware to the files\n        const filesWithCustomIds = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.forEach(files, (file, idx)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n                const theirs = metadata[_internal_types_js__WEBPACK_IMPORTED_MODULE_2__.UTFiles]?.[idx];\n                if (theirs && theirs.size !== file.size) {\n                    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logWarning(\"File size mismatch. Reverting to original size\");\n                }\n                return {\n                    name: theirs?.name ?? file.name,\n                    size: file.size,\n                    customId: theirs?.customId\n                };\n            }));\n        return {\n            metadata,\n            filesWithCustomIds\n        };\n    });\nconst handleUploadAction = effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n    const opts = yield* RequestInput;\n    const { files, input } = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.flatMap((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseRequestJson)(opts.req), _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(UploadActionPayload));\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Handling upload request with input:\", {\n        files,\n        input\n    });\n    // validate the input\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Parsing user input\");\n    const inputParser = opts.uploadable._def.inputParser;\n    const parsedInput = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tryPromise({\n        try: async ()=>getParseFn(inputParser)(input),\n        catch: (error)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Invalid input\",\n                cause: error\n            })\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapError((error)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"An error occured trying to parse input\", error)));\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Input parsed successfully\", parsedInput);\n    const { metadata, filesWithCustomIds } = yield* runRouteMiddleware({\n        input: parsedInput,\n        files\n    });\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Parsing route config\", opts.uploadable._def.routerConfig);\n    const parsedConfig = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fillInputRouteConfig)(opts.uploadable._def.routerConfig).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"InvalidRouteConfig\", (err)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: \"BAD_REQUEST\",\n            message: \"Invalid config\",\n            cause: err\n        })));\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Route config parsed successfully\", parsedConfig);\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Validating files meet the config requirements\", files);\n    yield* assertFilesMeetConfig(files, parsedConfig).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.mapError((e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: \"BAD_REQUEST\",\n            message: `Invalid config: ${e._tag}`,\n            cause: \"reason\" in e ? e.reason : e.message\n        })));\n    const callbackUrl = yield* resolveCallbackUrl.pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapError((error)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Failed to resolve callback URL\", error)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"InvalidURL\", (err)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: \"INTERNAL_SERVER_ERROR\",\n            message: err.message\n        })));\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Retrieving presigned URLs from UploadThing. Callback URL is:\", callbackUrl.href);\n    const presignedUrls = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateUploadThingURL)(\"/api/prepareUpload\"), {\n        method: \"POST\",\n        body: JSON.stringify({\n            files: filesWithCustomIds,\n            routeConfig: parsedConfig,\n            metadata,\n            callbackUrl: callbackUrl.origin + callbackUrl.pathname,\n            callbackSlug: opts.slug\n        }),\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(PresignedURLResponse)));\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"UploadThing responded with:\", presignedUrls);\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Sending presigned URLs to client\");\n    let promise = undefined;\n    if (opts.isDev) {\n        const fetchContext = yield* _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext;\n        promise = effect_Effect__WEBPACK_IMPORTED_MODULE_4__.forEach(presignedUrls, (file)=>conditionalDevServer(file.key, opts.apiKey).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.either), {\n            concurrency: 10\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.provide(ConsolaLogger), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, fetchContext), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.runPromise);\n    }\n    return {\n        body: presignedUrls,\n        cleanup: promise\n    };\n});\nconst handleMultipartCompleteAction = effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n    const opts = yield* RequestInput;\n    const requestInput = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.flatMap((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseRequestJson)(opts.req), _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(MultipartCompleteActionPayload));\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Handling multipart-complete request with input:\", requestInput);\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Notifying UploadThing that multipart upload is complete\");\n    const completionResponse = yield* completeMultipartUpload({\n        key: requestInput.fileKey,\n        uploadId: requestInput.uploadId\n    }, requestInput.etags);\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"UploadThing responded with:\", completionResponse);\n    return {\n        body: null\n    };\n});\nconst handleMultipartFailureAction = effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n    const { req, uploadable } = yield* RequestInput;\n    const { fileKey, uploadId } = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.flatMap((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseRequestJson)(req), _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(FailureActionPayload));\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Handling failure request with input:\", {\n        fileKey,\n        uploadId\n    });\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Notifying UploadThing that upload failed\");\n    const failureResponse = yield* abortMultipartUpload({\n        key: fileKey,\n        uploadId\n    });\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"UploadThing responded with:\", failureResponse);\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Running 'onUploadError' callback\");\n    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__[\"try\"]({\n        try: ()=>{\n            uploadable._def.onUploadError({\n                error: new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"UPLOAD_FAILED\",\n                    message: `Upload failed for ${fileKey}`\n                }),\n                fileKey\n            });\n        },\n        catch: (error)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"INTERNAL_SERVER_ERROR\",\n                message: \"Failed to run onUploadError\",\n                cause: error\n            })\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapError((error)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Failed to run onUploadError. You probably shouldn't be throwing errors here.\", error)));\n    return {\n        body: null\n    };\n});\nconst buildPermissionsInfoHandler = (opts)=>{\n    return ()=>{\n        const permissions = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(opts.router).map((slug)=>{\n            const route = opts.router[slug];\n            const config = effect_Effect__WEBPACK_IMPORTED_MODULE_4__.runSync((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fillInputRouteConfig)(route._def.routerConfig));\n            return {\n                slug,\n                config\n            };\n        });\n        return permissions;\n    };\n};\n\nfunction incompatibleNodeGuard() {\n    if (typeof std_env__WEBPACK_IMPORTED_MODULE_7__.process === \"undefined\") return;\n    let major;\n    let minor;\n    const maybeNodeVersion = std_env__WEBPACK_IMPORTED_MODULE_7__.process.versions?.node?.split(\".\");\n    if (maybeNodeVersion) {\n        [major, minor] = maybeNodeVersion.map((v)=>parseInt(v, 10));\n    }\n    const maybeNodePath = std_env__WEBPACK_IMPORTED_MODULE_7__.process.env?.NODE;\n    if (!major && maybeNodePath) {\n        const nodeVersion = /v(\\d+)\\.(\\d+)\\.(\\d+)/.exec(maybeNodePath)?.[0];\n        if (nodeVersion) {\n            [major, minor] = nodeVersion.substring(1).split(\".\").map((v)=>parseInt(v, 10));\n        }\n    }\n    if (!major || !minor) return;\n    // Require ^18.13.0\n    if (major > 18) return;\n    if (major === 18 && minor >= 13) return;\n    effect_Effect__WEBPACK_IMPORTED_MODULE_4__.runSync(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(`YOU ARE USING A LEGACY (${major}.${minor}) NODE VERSION WHICH ISN'T OFFICIALLY SUPPORTED. PLEASE UPGRADE TO NODE ^18.13.`));\n    // Kill the process if it isn't going to work correctly anyway\n    // If we've gotten this far we know we have a Node.js runtime so exit is defined. Override std-env type.\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call\n    std_env__WEBPACK_IMPORTED_MODULE_7__.process.exit?.(1);\n}\n\nfunction internalCreateBuilder(initDef = {}) {\n    const _def = {\n        // Default router config\n        routerConfig: {\n            image: {\n                maxFileSize: \"4MB\"\n            }\n        },\n        inputParser: {\n            parse: ()=>undefined,\n            _input: undefined,\n            _output: undefined\n        },\n        middleware: ()=>({}),\n        onUploadError: ()=>({}),\n        errorFormatter: initDef.errorFormatter ?? defaultErrorFormatter,\n        // Overload with properties passed in\n        ...initDef\n    };\n    return {\n        input (userParser) {\n            return internalCreateBuilder({\n                ..._def,\n                inputParser: userParser\n            });\n        },\n        middleware (userMiddleware) {\n            return internalCreateBuilder({\n                ..._def,\n                middleware: userMiddleware\n            });\n        },\n        onUploadComplete (userUploadComplete) {\n            return {\n                _def,\n                resolver: userUploadComplete\n            };\n        },\n        onUploadError (userOnUploadError) {\n            return internalCreateBuilder({\n                ..._def,\n                onUploadError: userOnUploadError\n            });\n        }\n    };\n}\nfunction createBuilder(opts) {\n    return (input)=>{\n        return internalCreateBuilder({\n            routerConfig: input,\n            ...opts\n        });\n    };\n}\n\n/**\n * Extension of the Blob class that simplifies setting the `name` and `customId` properties,\n * similar to the built-in File class from Node > 20.\n */ class UTFile extends Blob {\n    constructor(parts, name, options){\n        const optionsWithDefaults = {\n            ...options,\n            type: options?.type ?? ((0,_uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_12__.lookup)(name) || \"application/octet-stream\"),\n            lastModified: options?.lastModified ?? Date.now()\n        };\n        super(parts, optionsWithDefaults);\n        this.name = name;\n        this.customId = optionsWithDefaults.customId;\n        this.lastModified = optionsWithDefaults.lastModified;\n    }\n}\n\nconst uploadPresignedPost = (file, presigned)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(`Uploading file ${file.name} using presigned POST URL`);\n        const formData = new FormData();\n        Object.entries(presigned.fields).forEach(([k, v])=>formData.append(k, v));\n        formData.append(\"file\", file); // File data **MUST GO LAST**\n        const res = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)(presigned.url, {\n            method: \"POST\",\n            body: formData,\n            headers: new Headers({\n                Accept: \"application/xml\"\n            })\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapErrorCause(()=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateUploadThingURL)(\"/api/failureCallback\"), {\n                method: \"POST\",\n                body: JSON.stringify({\n                    fileKey: presigned.key,\n                    uploadId: null\n                }),\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(FailureCallbackResponse)))));\n        if (!res.ok) {\n            const text = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.promise(res.text);\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(`Failed to upload file ${file.name} to presigned POST URL. Response: ${text}`);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                code: \"UPLOAD_FAILED\",\n                message: \"Failed to upload file\",\n                cause: text\n            });\n        }\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"File\", file.name, \"uploaded successfully\");\n    });\n\nfunction guardServerOnly() {\n    if (typeof window !== \"undefined\") {\n        throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: \"INTERNAL_SERVER_ERROR\",\n            message: \"The `utapi` can only be used on the server.\"\n        });\n    }\n}\nconst uploadFilesInternal = (input)=>getPresignedUrls(input).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((presigneds)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.forEach(presigneds, (file)=>uploadFile(file).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapError((error)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Upload failed:\", error)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.match({\n                onFailure: (error)=>({\n                        data: null,\n                        error: _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError.toObject(error instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError ? error : new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                            message: \"Failed to upload file.\",\n                            code: \"BAD_REQUEST\",\n                            cause: error\n                        }))\n                    }),\n                onSuccess: (data)=>({\n                        data,\n                        error: null\n                    })\n            })), {\n            concurrency: 10\n        })));\n/**\n * FIXME: downloading everything into memory and then upload\n * isn't the best. We should support streams so we can download\n * just as much as we need at any time.\n */ const downloadFiles = (urls, downloadErrors)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.forEach(urls, (_url, idx)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n            let url = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.isObject)(_url) ? _url.url : _url;\n            if (typeof url === \"string\") {\n                // since dataurls will result in name being too long, tell the user\n                // to use uploadFiles instead.\n                if (url.startsWith(\"data:\")) {\n                    downloadErrors[idx] = _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError.toObject(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                        code: \"BAD_REQUEST\",\n                        message: \"Please use uploadFiles() for data URLs. uploadFilesFromUrl() is intended for use with remote URLs only.\"\n                    }));\n                    return null;\n                }\n            }\n            url = new URL(url);\n            const { name = url.pathname.split(\"/\").pop() ?? \"unknown-filename\", customId = undefined } = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.isObject)(_url) ? _url : {};\n            const response = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)(url);\n            if (!response.ok) {\n                downloadErrors[idx] = _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError.toObject(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Failed to download requested file.\",\n                    cause: response\n                }));\n                return undefined;\n            }\n            return yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.promise(()=>response.blob()).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((blob)=>new UTFile([\n                    blob\n                ], name, {\n                    customId\n                })));\n        }), {\n        concurrency: 10\n    });\nconst getPresignedUrls = (input)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n        const { files, metadata, contentDisposition, acl } = input;\n        const fileData = files.map((file)=>({\n                name: file.name ?? \"unnamed-blob\",\n                type: file.type,\n                size: file.size,\n                ...\"customId\" in file ? {\n                    customId: file.customId\n                } : {}\n            }));\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Getting presigned URLs for files\", fileData);\n        const responseSchema = _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Struct({\n            data: PresignedURLResponse\n        });\n        const presigneds = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateUploadThingURL)(\"/api/uploadFiles\"), {\n            method: \"POST\",\n            cache: \"no-store\",\n            body: JSON.stringify({\n                files: fileData,\n                metadata,\n                contentDisposition,\n                acl\n            }),\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(responseSchema)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"ParseError\", (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.die(e)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"FetchError\", (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.die(e)));\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Got presigned URLs:\", presigneds.data);\n        return files.map((file, i)=>({\n                file,\n                presigned: presigneds.data[i]\n            }));\n    });\nconst uploadFile = (input)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n        const { file, presigned } = input;\n        if (\"urls\" in presigned) {\n            yield* uploadMultipart(file, presigned);\n        } else {\n            yield* uploadPresignedPost(file, presigned);\n        }\n        yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateUploadThingURL)(`/api/pollUpload/${presigned.key}`)).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(PollUploadResponse)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tap(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Polled upload\", presigned.key)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((res)=>res.status === \"done\" ? effect_Effect__WEBPACK_IMPORTED_MODULE_4__.succeed(undefined) : effect_Effect__WEBPACK_IMPORTED_MODULE_4__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError())), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.retry({\n            while: (err)=>err instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.RetryError,\n            schedule: effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.exponential(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.millis(10), 4).pipe(// 10ms, 40ms, 160ms, 640ms...\n            effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.andThenEither(effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.spaced(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.seconds(1))), effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.compose(effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.elapsed), effect_Schedule__WEBPACK_IMPORTED_MODULE_5__.whileOutput(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.lessThanOrEqualTo(effect_Duration__WEBPACK_IMPORTED_MODULE_6__.minutes(1))))\n        }), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"RetryError\", (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.die(e)));\n        return {\n            key: presigned.key,\n            url: presigned.fileUrl,\n            name: file.name,\n            size: file.size,\n            type: file.type,\n            customId: \"customId\" in file ? file.customId ?? null : null\n        };\n    });\nfunction parseTimeToSeconds(time) {\n    const match = time.toString().split(/(\\d+)/).filter(Boolean);\n    const num = Number(match[0]);\n    const unit = (match[1] ?? \"s\").trim().slice(0, 1);\n    const multiplier = {\n        s: 1,\n        m: 60,\n        h: 3600,\n        d: 86400\n    }[unit];\n    return num * multiplier;\n}\n\nclass UTApi {\n    constructor(opts){\n        this.requestUploadThing = (pathname, body, responseSchema)=>{\n            const url = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateUploadThingURL)(pathname);\n            effect_Effect__WEBPACK_IMPORTED_MODULE_4__.runSync(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Requesting UploadThing:\", {\n                url,\n                body,\n                headers: this.defaultHeaders\n            }));\n            const headers = new Headers([\n                [\n                    \"Content-Type\",\n                    \"application/json\"\n                ]\n            ]);\n            for (const [key, value] of Object.entries(this.defaultHeaders)){\n                if (typeof value === \"string\") headers.set(key, value);\n            }\n            return (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fetchEff)(url, {\n                method: \"POST\",\n                cache: \"no-store\",\n                body: JSON.stringify(body),\n                headers\n            }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseResponseJson), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.decodeUnknown(responseSchema)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"FetchError\", (err)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Request failed:\", err).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(()=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.die(err)))), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"ParseError\", (err)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Response parsing failed:\", err).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen(()=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.die(err)))), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tap((res)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"UploadThing response:\", res)));\n        };\n        this.executeAsync = (program, signal)=>program.pipe(withMinimalLogLevel(this.logLevel), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.provide(ConsolaLogger), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, {\n                fetch: this.fetch,\n                baseHeaders: this.defaultHeaders\n            }), (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.runPromise(e, signal ? {\n                    signal\n                } : undefined));\n        /**\n   * Request to delete files from UploadThing storage.\n   * @param {string | string[]} fileKeys\n   *\n   * @example\n   * await deleteFiles(\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\");\n   *\n   * @example\n   * await deleteFiles([\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\",\"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\"])\n   *\n   * @example\n   * await deleteFiles(\"myCustomIdentifier\", { keyType: \"customId\" })\n   */ this.deleteFiles = async (keys, opts)=>{\n            guardServerOnly();\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            class DeleteFileResponse extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"DeleteFileResponse\")({\n                success: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Boolean,\n                deletedCount: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/api/deleteFiles\", keyType === \"fileKey\" ? {\n                fileKeys: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.asArray)(keys)\n            } : {\n                customIds: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.asArray)(keys)\n            }, DeleteFileResponse));\n        };\n        /**\n   * Request file URLs from UploadThing storage.\n   * @param {string | string[]} fileKeys\n   *\n   * @example\n   * const data = await getFileUrls(\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\");\n   * console.log(data); // [{key: \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", url: \"https://uploadthing.com/f/2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\"}]\n   *\n   * @example\n   * const data = await getFileUrls([\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\",\"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\"])\n   * console.log(data) // [{key: \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", url: \"https://uploadthing.com/f/2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\" },{key: \"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\", url: \"https://uploadthing.com/f/1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\"}]\n   */ this.getFileUrls = async (keys, opts)=>{\n            guardServerOnly();\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            class GetFileUrlResponse extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"GetFileUrlResponse\")({\n                data: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Array(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Struct({\n                    key: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n                    url: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String\n                }))\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/api/getFileUrl\", keyType === \"fileKey\" ? {\n                fileKeys: keys\n            } : {\n                customIds: keys\n            }, GetFileUrlResponse));\n        };\n        /**\n   * Request file list from UploadThing storage.\n   * @param {object} opts\n   * @param {number} opts.limit The maximum number of files to return\n   * @param {number} opts.offset The number of files to skip\n   *\n   * @example\n   * const data = await listFiles({ limit: 1 });\n   * console.log(data); // { key: \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", id: \"2e0fdb64-9957-4262-8e45-f372ba903ac8\" }\n   */ this.listFiles = async (opts)=>{\n            guardServerOnly();\n            class ListFileResponse extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"ListFileResponse\")({\n                hasMore: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Boolean,\n                files: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Array(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Struct({\n                    id: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n                    customId: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.NullOr(_effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String),\n                    key: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n                    name: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String,\n                    status: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Literal(\"Deletion Pending\", \"Failed\", \"Uploaded\", \"Uploading\")\n                }))\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/api/listFiles\", {\n                ...opts\n            }, ListFileResponse));\n        };\n        this.renameFiles = async (updates)=>{\n            guardServerOnly();\n            class RenameFileResponse extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"RenameFileResponse\")({\n                success: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Boolean\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/api/renameFiles\", {\n                updates: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.asArray)(updates)\n            }, RenameFileResponse));\n        };\n        /** @deprecated Use {@link renameFiles} instead. */ this.renameFile = this.renameFiles;\n        this.getUsageInfo = async ()=>{\n            guardServerOnly();\n            class GetUsageInfoResponse extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"GetUsageInfoResponse\")({\n                totalBytes: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number,\n                appTotalBytes: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number,\n                filesUploaded: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number,\n                limitBytes: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Number\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/api/getUsageInfo\", {}, GetUsageInfoResponse));\n        };\n        /** Request a presigned url for a private file(s) */ this.getSignedURL = async (key, opts)=>{\n            guardServerOnly();\n            const expiresIn = opts?.expiresIn ? parseTimeToSeconds(opts.expiresIn) : undefined;\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            if (opts?.expiresIn && isNaN(expiresIn)) {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be a valid time string, for example '1d', '2 days', or a number of seconds.\"\n                });\n            }\n            if (expiresIn && expiresIn > 86400 * 7) {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be less than 7 days (604800 seconds).\"\n                });\n            }\n            class GetSignedUrlResponse extends _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Class(\"GetSignedUrlResponse\")({\n                url: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.String\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/api/requestFileAccess\", keyType === \"fileKey\" ? {\n                fileKey: key,\n                expiresIn\n            } : {\n                customId: key,\n                expiresIn\n            }, GetSignedUrlResponse));\n        };\n        /**\n   * Update the ACL of a file or set of files.\n   *\n   * @example\n   * // Make a single file public\n   * await utapi.updateACL(\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", \"public-read\");\n   *\n   * // Make multiple files private\n   * await utapi.updateACL(\n   *   [\n   *     \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\",\n   *     \"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\",\n   *   ],\n   *   \"private\",\n   * );\n   */ this.updateACL = async (keys, acl, opts)=>{\n            guardServerOnly();\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            const updates = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.asArray)(keys).map((key)=>{\n                return keyType === \"fileKey\" ? {\n                    fileKey: key,\n                    acl\n                } : {\n                    customId: key,\n                    acl\n                };\n            });\n            const responseSchema = _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Struct({\n                success: _effect_schema_Schema__WEBPACK_IMPORTED_MODULE_3__.Boolean\n            });\n            return await this.executeAsync(this.requestUploadThing(\"/api/updateACL\", {\n                updates\n            }, responseSchema));\n        };\n        // Assert some stuff\n        guardServerOnly();\n        incompatibleNodeGuard();\n        const apiKey = getApiKeyOrThrow(opts?.apiKey);\n        this.fetch = opts?.fetch ?? globalThis.fetch;\n        this.defaultHeaders = {\n            \"x-uploadthing-api-key\": apiKey,\n            \"x-uploadthing-version\": version,\n            \"x-uploadthing-be-adapter\": \"server-sdk\",\n            \"x-uploadthing-fe-package\": undefined\n        };\n        this.defaultKeyType = opts?.defaultKeyType ?? \"fileKey\";\n        this.logLevel = opts?.logLevel;\n    }\n    async uploadFiles(files, opts) {\n        guardServerOnly();\n        const uploads = await this.executeAsync(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.flatMap(uploadFilesInternal({\n            files: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.asArray)(files),\n            contentDisposition: opts?.contentDisposition ?? \"inline\",\n            metadata: opts?.metadata ?? {},\n            acl: opts?.acl\n        }), (ups)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.succeed(Array.isArray(files) ? ups : ups[0])).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tap((res)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Finished uploading:\", res))), opts?.signal);\n        return uploads;\n    }\n    async uploadFilesFromUrl(urls, opts) {\n        guardServerOnly();\n        const downloadErrors = {};\n        const uploads = await this.executeAsync(downloadFiles((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.asArray)(urls), downloadErrors).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((files)=>files.filter((f)=>f != null)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((files)=>uploadFilesInternal({\n                files,\n                contentDisposition: opts?.contentDisposition ?? \"inline\",\n                metadata: opts?.metadata ?? {},\n                acl: opts?.acl\n            }))), opts?.signal);\n        /** Put it all back together, preserve the order of files */ const responses = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.asArray)(urls).map((_, index)=>{\n            if (downloadErrors[index]) {\n                return {\n                    data: null,\n                    error: downloadErrors[index]\n                };\n            }\n            return uploads.shift();\n        });\n        /** Return single object or array based on input urls */ const uploadFileResponse = Array.isArray(urls) ? responses : responses[0];\n        effect_Effect__WEBPACK_IMPORTED_MODULE_4__.runSync(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logDebug(\"Finished uploading:\", uploadFileResponse));\n        return uploadFileResponse;\n    }\n}\n\nconst createUploadthing = (opts)=>createBuilder(opts);\n/** @internal */ const INTERNAL_DO_NOT_USE_createRouteHandlerCore = (opts, adapter)=>{\n    incompatibleNodeGuard();\n    const requestHandler = buildRequestHandler(opts, adapter);\n    const getBuildPerms = buildPermissionsInfoHandler(opts);\n    const POST = async (request)=>{\n        const req = request instanceof Request ? request : request.request;\n        const response = await runRequestHandlerAsync(requestHandler, {\n            req,\n            middlewareArgs: {\n                req,\n                event: undefined,\n                res: undefined\n            }\n        }, opts.config);\n        if (response.success === false) {\n            return Response.json(formatError(response.error, opts.router), {\n                status: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.getStatusCodeFromError)(response.error),\n                headers: {\n                    \"x-uploadthing-version\": version\n                }\n            });\n        }\n        const res = Response.json(response.body, {\n            headers: {\n                \"x-uploadthing-version\": version\n            }\n        });\n        // @ts-expect-error - this is a custom property\n        res.cleanup = response.cleanup;\n        return res;\n    };\n    const GET = (request)=>{\n        return Response.json(getBuildPerms(), {\n            headers: {\n                \"x-uploadthing-version\": version\n            }\n        });\n    };\n    return {\n        GET,\n        POST\n    };\n};\nconst createRouteHandler = (opts)=>INTERNAL_DO_NOT_USE_createRouteHandlerCore(opts, \"server\");\nconst extractRouterConfig = (router)=>buildPermissionsInfoHandler({\n        router\n    })();\n/**\n * @deprecated Use {@link createRouteHandler} instead\n */ const createServerHandler = createRouteHandler;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/uploadthing/server/index.js\n");

/***/ })

};
;