"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Canvas refs for preview generation\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fabricCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers\n                const editableLayers = JSON.parse(templateData.editableLayers);\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleTextChange = (layerId, value)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        // Create object URL for immediate preview\n        const imageUrl = URL.createObjectURL(file);\n        // Update customizations immediately\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: imageUrl\n            }));\n    // In a real implementation, you'd upload to your storage service here\n    // const uploadedUrl = await uploadToStorage(file);\n    // setCustomizations(prev => ({ ...prev, [layerId]: uploadedUrl }));\n    };\n    // Initialize canvas when template is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!template || !canvasRef.current) return;\n        // Clean up existing canvas\n        if (fabricCanvasRef.current) {\n            fabricCanvasRef.current.dispose();\n        }\n        // Create new fabric canvas with proper sizing\n        const canvas = new fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Canvas(canvasRef.current, {\n            width: template.width,\n            height: template.height,\n            selection: false,\n            preserveObjectStacking: true\n        });\n        fabricCanvasRef.current = canvas;\n        // Load template JSON\n        try {\n            const templateJson = JSON.parse(template.json);\n            canvas.loadFromJSON(templateJson, ()=>{\n                // Ensure canvas is properly sized and rendered\n                canvas.setDimensions({\n                    width: template.width,\n                    height: template.height\n                });\n                canvas.renderAll();\n                generatePreviewFromCanvas();\n            });\n        } catch (error) {\n            console.error(\"Failed to load template JSON:\", error);\n        }\n        return ()=>{\n            if (fabricCanvasRef.current) {\n                fabricCanvasRef.current.dispose();\n                fabricCanvasRef.current = null;\n            }\n        };\n    }, [\n        template\n    ]);\n    // Generate preview from canvas\n    const generatePreviewFromCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!fabricCanvasRef.current) return;\n        try {\n            const dataURL = fabricCanvasRef.current.toDataURL({\n                format: \"png\",\n                quality: 0.8,\n                multiplier: 1\n            });\n            setPreviewUrl(dataURL);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, []);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!fabricCanvasRef.current || !template) return;\n        const canvas = fabricCanvasRef.current;\n        // Apply text customizations\n        Object.entries(customizations).forEach((param)=>{\n            let [layerId, value] = param;\n            const object = canvas.getObjects().find((obj)=>obj.id === layerId);\n            if (object && object.type === \"textbox\") {\n                object.set(\"text\", value);\n            }\n        });\n        canvas.renderAll();\n        generatePreviewFromCanvas();\n    }, [\n        customizations,\n        template,\n        generatePreviewFromCanvas\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Generate preview from current canvas state\n            generatePreviewFromCanvas();\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // In a real implementation, you'd get a download URL from the API\n            const link = document.createElement(\"a\");\n            link.href = (template === null || template === void 0 ? void 0 : template.thumbnailUrl) || \"\";\n            link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n            link.click();\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Customize Elements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: customizations[layer.id] || \"\",\n                                                                onChange: (e)=>handleTextChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 361,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 362,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center bg-gray-100 rounded-lg p-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                                                style: {\n                                                    aspectRatio: \"\".concat(template.width, \"/\").concat(template.height),\n                                                    maxWidth: \"600px\",\n                                                    width: \"100%\"\n                                                },\n                                                children: previewUrl || template.thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: previewUrl || template.thumbnailUrl || \"\",\n                                                    alt: \"Template preview\",\n                                                    className: \"w-full h-full object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-full w-full items-center justify-center bg-gray-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    display: \"none\"\n                },\n                width: (template === null || template === void 0 ? void 0 : template.width) || 800,\n                height: (template === null || template === void 0 ? void 0 : template.height) || 600\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"QQPCiuc/A/FRTC+Fc3uRGNCk9XI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ })

});