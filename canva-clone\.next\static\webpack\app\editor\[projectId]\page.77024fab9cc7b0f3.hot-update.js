"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/editor.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Editor: function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-thumbnail-generator */ \"(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/components/navbar */ \"(app-pages-browser)/./src/features/editor/components/navbar.tsx\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/components/sidebar */ \"(app-pages-browser)/./src/features/editor/components/sidebar.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* harmony import */ var _features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/editor/components/shape-sidebar */ \"(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/editor/components/fill-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/fill-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/features/editor/components/stroke-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/editor/components/stroke-width-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-width-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/features/editor/components/opacity-sidebar */ \"(app-pages-browser)/./src/features/editor/components/opacity-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/features/editor/components/text-sidebar */ \"(app-pages-browser)/./src/features/editor/components/text-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/editor/components/font-sidebar */ \"(app-pages-browser)/./src/features/editor/components/font-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/editor/components/image-sidebar */ \"(app-pages-browser)/./src/features/editor/components/image-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/editor/components/filter-sidebar */ \"(app-pages-browser)/./src/features/editor/components/filter-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/editor/components/draw-sidebar */ \"(app-pages-browser)/./src/features/editor/components/draw-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/editor/components/ai-sidebar */ \"(app-pages-browser)/./src/features/editor/components/ai-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/editor/components/template-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/features/editor/components/remove-bg-sidebar */ \"(app-pages-browser)/./src/features/editor/components/remove-bg-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/editor/components/settings-sidebar */ \"(app-pages-browser)/./src/features/editor/components/settings-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/editor/components/template-config-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Editor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Editor = (param)=>{\n    let { initialData, initialActiveTool = \"select\" } = param;\n    _s();\n    const { mutate } = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject)(initialData.id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()((values)=>{\n        mutate(values);\n    }, 3000 // Increased from 500ms to 3 seconds\n    ), [\n        mutate\n    ]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(initialActiveTool);\n    const onClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        if (_features_editor_types__WEBPACK_IMPORTED_MODULE_6__.selectionDependentTools.includes(activeTool)) {\n            setActiveTool(\"select\");\n        }\n    }, [\n        activeTool\n    ]);\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor)({\n        defaultState: initialData.json,\n        defaultWidth: initialData.width,\n        defaultHeight: initialData.height,\n        clearSelectionCallback: onClearSelection,\n        saveCallback: debouncedSave\n    });\n    const handleManualSave = ()=>{\n        if (editor === null || editor === void 0 ? void 0 : editor.canvas) {\n            const workspace = editor.canvas.getObjects().find((object)=>object.name === \"clip\");\n            const height = (workspace === null || workspace === void 0 ? void 0 : workspace.height) || initialData.height;\n            const width = (workspace === null || workspace === void 0 ? void 0 : workspace.width) || initialData.width;\n            const json = JSON.stringify(editor.canvas.toJSON());\n            mutate({\n                json,\n                height,\n                width\n            });\n        }\n    };\n    // Generate thumbnails automatically when the canvas changes\n    const { debouncedGenerateThumbnail } = (0,_features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator)({\n        editor,\n        projectId: initialData.id\n    });\n    // Trigger thumbnail generation when canvas changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (editor === null || editor === void 0 ? void 0 : editor.canvas) {\n            const handleCanvasChange = ()=>{\n                debouncedGenerateThumbnail();\n            };\n            const canvas = editor.canvas;\n            canvas.on(\"object:added\", handleCanvasChange);\n            canvas.on(\"object:removed\", handleCanvasChange);\n            canvas.on(\"object:modified\", handleCanvasChange);\n            canvas.on(\"path:created\", handleCanvasChange);\n            // Generate initial thumbnail when editor is ready\n            const generateInitialThumbnail = ()=>{\n                setTimeout(()=>{\n                    console.log(\"Generating initial thumbnail...\");\n                    debouncedGenerateThumbnail();\n                }, 1000); // Wait a bit for canvas to be fully ready\n            };\n            generateInitialThumbnail();\n            return ()=>{\n                canvas.off(\"object:added\", handleCanvasChange);\n                canvas.off(\"object:removed\", handleCanvasChange);\n                canvas.off(\"object:modified\", handleCanvasChange);\n                canvas.off(\"path:created\", handleCanvasChange);\n            };\n        }\n    }, [\n        editor,\n        debouncedGenerateThumbnail\n    ]);\n    // Trigger thumbnail generation when editor changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (editor) {\n            debouncedGenerateThumbnail();\n        }\n    }, [\n        editor,\n        debouncedGenerateThumbnail\n    ]);\n    const onChangeActiveTool = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((tool)=>{\n        if (tool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.enableDrawingMode();\n        }\n        if (activeTool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.disableDrawingMode();\n        }\n        if (tool === activeTool) {\n            return setActiveTool(\"select\");\n        }\n        setActiveTool(tool);\n    }, [\n        activeTool,\n        editor\n    ]);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            // Ensure container is available and has dimensions\n            if (!containerRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            // Don't initialize if container has no dimensions\n            if (containerWidth === 0 || containerHeight === 0) {\n                console.warn(\"Container has zero dimensions, delaying canvas initialization\");\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_1__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        // Try to initialize immediately\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            // If initialization failed, retry after a short delay\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (!retryCanvas) {\n                    console.error(\"Failed to initialize canvas after retry\");\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        }\n        return ()=>{\n            canvas.dispose();\n        };\n    }, [\n        init\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__.Navbar, {\n                id: initialData.id,\n                editor: editor,\n                activeTool: activeTool,\n                onChangeActiveTool: onChangeActiveTool,\n                onSave: handleManualSave,\n                initialData: {\n                    name: initialData.name,\n                    isCustomizable: initialData.isCustomizable\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute h-[calc(100%-68px)] w-full top-[68px] flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__.ShapeSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__.FillColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__.StrokeColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__.StrokeWidthSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__.OpacitySidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__.TextSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__.FontSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__.ImageSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__.TemplateSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__.FilterSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__.AiSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__.RemoveBgSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__.DrawSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__.SettingsSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__.TemplateConfigSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool,\n                        projectId: initialData.id,\n                        initialData: {\n                            isCustomizable: initialData.isCustomizable,\n                            editableLayers: initialData.editableLayers\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-muted flex-1 overflow-auto relative flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__.Toolbar, {\n                                editor: editor,\n                                activeTool: activeTool,\n                                onChangeActiveTool: onChangeActiveTool\n                            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                                ref: containerRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                    ref: canvasRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__.Footer, {\n                                editor: editor\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Editor, \"tgiuclVd7L+4Z8LW5DMwulQ0Fe4=\", false, function() {\n    return [\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject,\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor,\n        _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator\n    ];\n});\n_c = Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx\n"));

/***/ })

});