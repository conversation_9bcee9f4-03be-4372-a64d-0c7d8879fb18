"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/app/editor/[projectId]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/editor/[projectId]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Loader_TriangleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader,TriangleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_Loader_TriangleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader,TriangleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _features_projects_api_use_get_project__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/projects/api/use-get-project */ \"(app-pages-browser)/./src/features/projects/api/use-get-project.ts\");\n/* harmony import */ var _features_editor_components_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/editor */ \"(app-pages-browser)/./src/features/editor/components/editor.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EditorProjectIdPage = (param)=>{\n    let { params } = param;\n    _s();\n    const { data, isLoading, isError } = (0,_features_projects_api_use_get_project__WEBPACK_IMPORTED_MODULE_2__.useGetProject)(params.projectId);\n    if (isLoading || !data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_TriangleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"size-6 animate-spin text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col gap-y-5 items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_TriangleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"size-6 text-muted-foreground\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground text-sm\",\n                    children: \"Failed to fetch project\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    asChild: true,\n                    variant: \"secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: \"/\",\n                        children: \"Back to Home\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_editor__WEBPACK_IMPORTED_MODULE_3__.Editor, {\n        initialData: data\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 10\n    }, undefined);\n};\n_s(EditorProjectIdPage, \"+CEF8HYjGIeTDJwrRrYzME64G1Q=\", false, function() {\n    return [\n        _features_projects_api_use_get_project__WEBPACK_IMPORTED_MODULE_2__.useGetProject\n    ];\n});\n_c = EditorProjectIdPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorProjectIdPage);\nvar _c;\n$RefreshReg$(_c, \"EditorProjectIdPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/editor/[projectId]/page.tsx\n"));

/***/ })

});