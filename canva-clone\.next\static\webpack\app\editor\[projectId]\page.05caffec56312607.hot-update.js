"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/editor.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Editor: function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-thumbnail-generator */ \"(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/components/navbar */ \"(app-pages-browser)/./src/features/editor/components/navbar.tsx\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/components/sidebar */ \"(app-pages-browser)/./src/features/editor/components/sidebar.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* harmony import */ var _features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/editor/components/shape-sidebar */ \"(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/editor/components/fill-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/fill-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/features/editor/components/stroke-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/editor/components/stroke-width-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-width-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/features/editor/components/opacity-sidebar */ \"(app-pages-browser)/./src/features/editor/components/opacity-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/features/editor/components/text-sidebar */ \"(app-pages-browser)/./src/features/editor/components/text-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/editor/components/font-sidebar */ \"(app-pages-browser)/./src/features/editor/components/font-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/editor/components/image-sidebar */ \"(app-pages-browser)/./src/features/editor/components/image-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/editor/components/filter-sidebar */ \"(app-pages-browser)/./src/features/editor/components/filter-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/editor/components/draw-sidebar */ \"(app-pages-browser)/./src/features/editor/components/draw-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/editor/components/ai-sidebar */ \"(app-pages-browser)/./src/features/editor/components/ai-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/editor/components/template-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/features/editor/components/remove-bg-sidebar */ \"(app-pages-browser)/./src/features/editor/components/remove-bg-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/editor/components/settings-sidebar */ \"(app-pages-browser)/./src/features/editor/components/settings-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/editor/components/template-config-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Editor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Editor = (param)=>{\n    let { initialData, initialActiveTool = \"select\" } = param;\n    _s();\n    const { mutate } = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject)(initialData.id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()((values)=>{\n        mutate(values);\n    }, 3000 // Increased from 500ms to 3 seconds\n    ), [\n        mutate\n    ]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(initialActiveTool);\n    const onClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        if (_features_editor_types__WEBPACK_IMPORTED_MODULE_6__.selectionDependentTools.includes(activeTool)) {\n            setActiveTool(\"select\");\n        }\n    }, [\n        activeTool\n    ]);\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor)({\n        defaultState: initialData.json,\n        defaultWidth: initialData.width,\n        defaultHeight: initialData.height,\n        clearSelectionCallback: onClearSelection,\n        saveCallback: debouncedSave\n    });\n    const handleManualSave = ()=>{\n        if (editor === null || editor === void 0 ? void 0 : editor.canvas) {\n            const workspace = editor.canvas.getObjects().find((object)=>object.name === \"clip\");\n            const height = (workspace === null || workspace === void 0 ? void 0 : workspace.height) || initialData.height;\n            const width = (workspace === null || workspace === void 0 ? void 0 : workspace.width) || initialData.width;\n            const json = JSON.stringify(editor.canvas.toJSON());\n            mutate({\n                json,\n                height,\n                width\n            });\n        }\n    };\n    // Generate thumbnails automatically when the canvas changes\n    const { debouncedGenerateThumbnail, forceRegenerateThumbnail } = (0,_features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator)({\n        editor,\n        projectId: initialData.id\n    });\n    // Trigger thumbnail generation when canvas changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (editor === null || editor === void 0 ? void 0 : editor.canvas) {\n            const handleCanvasChange = ()=>{\n                debouncedGenerateThumbnail();\n            };\n            const canvas = editor.canvas;\n            canvas.on(\"object:added\", handleCanvasChange);\n            canvas.on(\"object:removed\", handleCanvasChange);\n            canvas.on(\"object:modified\", handleCanvasChange);\n            canvas.on(\"path:created\", handleCanvasChange);\n            // Generate initial thumbnail when editor is ready\n            const generateInitialThumbnail = ()=>{\n                setTimeout(()=>{\n                    console.log(\"Generating initial thumbnail...\");\n                    debouncedGenerateThumbnail();\n                }, 1000); // Wait a bit for canvas to be fully ready\n            };\n            generateInitialThumbnail();\n            return ()=>{\n                canvas.off(\"object:added\", handleCanvasChange);\n                canvas.off(\"object:removed\", handleCanvasChange);\n                canvas.off(\"object:modified\", handleCanvasChange);\n                canvas.off(\"path:created\", handleCanvasChange);\n            };\n        }\n    }, [\n        editor,\n        debouncedGenerateThumbnail\n    ]);\n    // Trigger thumbnail generation when editor changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (editor) {\n            debouncedGenerateThumbnail();\n        }\n    }, [\n        editor,\n        debouncedGenerateThumbnail\n    ]);\n    const onChangeActiveTool = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((tool)=>{\n        if (tool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.enableDrawingMode();\n        }\n        if (activeTool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.disableDrawingMode();\n        }\n        if (tool === activeTool) {\n            return setActiveTool(\"select\");\n        }\n        setActiveTool(tool);\n    }, [\n        activeTool,\n        editor\n    ]);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            // Ensure container is available and has dimensions\n            if (!containerRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            // Don't initialize if container has no dimensions\n            if (containerWidth === 0 || containerHeight === 0) {\n                console.warn(\"Container has zero dimensions, delaying canvas initialization\");\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_1__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        // Try to initialize immediately\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            // If initialization failed, retry after a short delay\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (!retryCanvas) {\n                    console.error(\"Failed to initialize canvas after retry\");\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        }\n        return ()=>{\n            canvas.dispose();\n        };\n    }, [\n        init\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__.Navbar, {\n                id: initialData.id,\n                editor: editor,\n                activeTool: activeTool,\n                onChangeActiveTool: onChangeActiveTool,\n                onSave: handleManualSave,\n                initialData: {\n                    name: initialData.name,\n                    isCustomizable: initialData.isCustomizable || false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute h-[calc(100%-68px)] w-full top-[68px] flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__.ShapeSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__.FillColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__.StrokeColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__.StrokeWidthSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__.OpacitySidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__.TextSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__.FontSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__.ImageSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__.TemplateSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__.FilterSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__.AiSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__.RemoveBgSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__.DrawSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__.SettingsSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__.TemplateConfigSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool,\n                        projectId: initialData.id,\n                        initialData: {\n                            isCustomizable: initialData.isCustomizable || false,\n                            editableLayers: initialData.editableLayers\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-muted flex-1 overflow-auto relative flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__.Toolbar, {\n                                editor: editor,\n                                activeTool: activeTool,\n                                onChangeActiveTool: onChangeActiveTool\n                            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                                ref: containerRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                    ref: canvasRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__.Footer, {\n                                editor: editor\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Editor, \"4mKcfUiWcH97psEe8aPkKs+t6DM=\", false, function() {\n    return [\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject,\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor,\n        _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator\n    ];\n});\n_c = Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx\n"));

/***/ })

});