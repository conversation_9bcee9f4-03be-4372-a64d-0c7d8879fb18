"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/utils/thumbnail.ts":
/*!************************************************!*\
  !*** ./src/features/editor/utils/thumbnail.ts ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAndUploadThumbnail: function() { return /* binding */ generateAndUploadThumbnail; },\n/* harmony export */   generateThumbnail: function() { return /* binding */ generateThumbnail; },\n/* harmony export */   uploadThumbnail: function() { return /* binding */ uploadThumbnail; }\n/* harmony export */ });\nconst generateThumbnail = function(canvas) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _canvas_viewportTransform;\n    const { width = 600, height = 400, quality = 1.0, format = \"image/png\", maintainAspectRatio = true } = options;\n    // Validate canvas\n    if (!canvas) {\n        console.error(\"Canvas is null or undefined\");\n        throw new Error(\"Canvas is required for thumbnail generation\");\n    }\n    const canvasWidth = canvas.getWidth();\n    const canvasHeight = canvas.getHeight();\n    if (canvasWidth <= 0 || canvasHeight <= 0) {\n        console.error(\"Canvas has invalid dimensions:\", {\n            canvasWidth,\n            canvasHeight\n        });\n        throw new Error(\"Canvas has invalid dimensions\");\n    }\n    // Get the workspace (the main design area)\n    const workspace = canvas.getObjects().find((object)=>object.name === \"clip\");\n    if (!workspace) {\n        console.warn(\"No workspace found, using entire canvas\");\n        // Fallback: use entire canvas with ultra-high quality\n        return canvas.toDataURL({\n            format,\n            quality,\n            multiplier: 3\n        });\n    }\n    // Get workspace dimensions\n    const workspaceWidth = workspace.width * (workspace.scaleX || 1);\n    const workspaceHeight = workspace.height * (workspace.scaleY || 1);\n    // Validate workspace dimensions\n    if (workspaceWidth <= 0 || workspaceHeight <= 0) {\n        console.error(\"Workspace has invalid dimensions:\", {\n            workspaceWidth,\n            workspaceHeight\n        });\n        throw new Error(\"Workspace has invalid dimensions\");\n    }\n    let thumbnailWidth = width;\n    let thumbnailHeight = height;\n    if (maintainAspectRatio) {\n        // Calculate proper aspect ratio\n        const aspectRatio = workspaceWidth / workspaceHeight;\n        if (aspectRatio > 1) {\n            // Landscape: fit to width\n            thumbnailHeight = width / aspectRatio;\n        } else {\n            // Portrait: fit to height\n            thumbnailWidth = height * aspectRatio;\n        }\n    }\n    // Save current state\n    const currentTransform = (_canvas_viewportTransform = canvas.viewportTransform) === null || _canvas_viewportTransform === void 0 ? void 0 : _canvas_viewportTransform.slice();\n    const currentZoom = canvas.getZoom();\n    const activeObject = canvas.getActiveObject();\n    // Reset viewport to identity for clean rendering\n    canvas.setViewportTransform([\n        1,\n        0,\n        0,\n        1,\n        0,\n        0\n    ]);\n    canvas.setZoom(1);\n    // Temporarily hide any selection or controls for clean thumbnail\n    canvas.discardActiveObject();\n    canvas.renderAll();\n    // Use ultra-high multiplier for Canva-level crisp quality\n    const pixelRatio = window.devicePixelRatio || 1;\n    const multiplier = Math.max(4, pixelRatio * 2); // At least 4x for ultra-crisp quality\n    // Generate ultra-high-quality thumbnail with perfect scaling\n    const dataUrl = canvas.toDataURL({\n        format,\n        quality,\n        left: workspace.left,\n        top: workspace.top,\n        width: workspaceWidth,\n        height: workspaceHeight,\n        multiplier: multiplier,\n        enableRetinaScaling: true\n    });\n    // Restore original state\n    if (currentTransform) {\n        canvas.setViewportTransform(currentTransform);\n    }\n    canvas.setZoom(currentZoom);\n    if (activeObject) {\n        canvas.setActiveObject(activeObject);\n    }\n    canvas.renderAll();\n    return dataUrl;\n};\nconst uploadThumbnail = async (dataUrl, projectId)=>{\n    try {\n        var _uploadResult_;\n        // Convert data URL to blob\n        const response = await fetch(dataUrl);\n        const blob = await response.blob();\n        // Create form data\n        const formData = new FormData();\n        formData.append(\"files\", blob, \"thumbnail-\".concat(projectId, \".jpg\"));\n        // Upload to UploadThing\n        const uploadResponse = await fetch(\"/api/uploadthing\", {\n            method: \"POST\",\n            body: formData\n        });\n        if (!uploadResponse.ok) {\n            throw new Error(\"Failed to upload thumbnail\");\n        }\n        const uploadResult = await uploadResponse.json();\n        return ((_uploadResult_ = uploadResult[0]) === null || _uploadResult_ === void 0 ? void 0 : _uploadResult_.url) || null;\n    } catch (error) {\n        console.error(\"Error uploading thumbnail:\", error);\n        return null;\n    }\n};\nconst generateAndUploadThumbnail = async (canvas, projectId, options)=>{\n    try {\n        const thumbnailDataUrl = generateThumbnail(canvas, options);\n        const thumbnailUrl = await uploadThumbnail(thumbnailDataUrl, projectId);\n        return thumbnailUrl;\n    } catch (error) {\n        console.error(\"Error generating and uploading thumbnail:\", error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/utils/thumbnail.ts\n"));

/***/ })

});