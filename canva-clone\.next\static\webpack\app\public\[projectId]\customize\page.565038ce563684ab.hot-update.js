"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers safely\n                let editableLayers = [];\n                try {\n                    editableLayers = templateData.editableLayers ? JSON.parse(templateData.editableLayers) : [];\n                } catch (error) {\n                    console.error(\"Error parsing editable layers:\", error);\n                    editableLayers = [];\n                }\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleTextChange = (layerId, value)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        // Create object URL for immediate preview\n        const imageUrl = URL.createObjectURL(file);\n        // Update customizations immediately\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: imageUrl\n            }));\n    // In a real implementation, you'd upload to your storage service here\n    // const uploadedUrl = await uploadToStorage(file);\n    // setCustomizations(prev => ({ ...prev, [layerId]: uploadedUrl }));\n    };\n    // Initialize preview - start with template thumbnail\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!template) return;\n        // Always start with the template thumbnail for immediate preview\n        if (template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n            setCanvasInitialized(true);\n        }\n        // Only initialize Fabric.js canvas if there are editable layers and customizations\n        if (!template.editableLayers || template.editableLayers.length === 0) {\n            return;\n        }\n        // Clean up existing canvas\n        if (fabricCanvasRef.current) {\n            try {\n                fabricCanvasRef.current.dispose();\n            } catch (error) {\n                console.warn(\"Error disposing canvas:\", error);\n            }\n            fabricCanvasRef.current = null;\n        }\n        const initCanvas = async ()=>{\n            if (!canvasRef.current || !template) return;\n            try {\n                // Create a simple canvas for customization preview\n                const canvas = new fabric.Canvas(canvasRef.current, {\n                    width: template.width,\n                    height: template.height,\n                    selection: false,\n                    preserveObjectStacking: true,\n                    backgroundColor: \"white\"\n                });\n                fabricCanvasRef.current = canvas;\n                // Load template JSON if available\n                if (template.json) {\n                    try {\n                        const templateJson = JSON.parse(template.json);\n                        await new Promise((resolve, reject)=>{\n                            canvas.loadFromJSON(templateJson, ()=>{\n                                try {\n                                    // Remove workspace/clip objects that cause positioning issues\n                                    const objects = canvas.getObjects();\n                                    const workspace = objects.find((obj)=>obj.name === \"clip\");\n                                    if (workspace) {\n                                        canvas.remove(workspace);\n                                    }\n                                    // Remove any other editor-specific objects\n                                    objects.forEach((obj)=>{\n                                        if (obj.name === \"clip\" || obj.selectable === false) {\n                                            canvas.remove(obj);\n                                        }\n                                    });\n                                    canvas.renderAll();\n                                    resolve();\n                                } catch (error) {\n                                    reject(error);\n                                }\n                            }, (error)=>{\n                                reject(error);\n                            });\n                        });\n                    } catch (jsonError) {\n                        console.error(\"Error parsing template JSON:\", jsonError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize canvas:\", error);\n            }\n        };\n        // Delay initialization to ensure DOM is ready\n        const timeoutId = setTimeout(initCanvas, 200);\n        return ()=>{\n            clearTimeout(timeoutId);\n            if (fabricCanvasRef.current) {\n                try {\n                    fabricCanvasRef.current.dispose();\n                } catch (error) {\n                    console.warn(\"Error disposing canvas in cleanup:\", error);\n                }\n                fabricCanvasRef.current = null;\n            }\n        };\n    }, [\n        template\n    ]);\n    // Generate preview from canvas\n    const generatePreviewFromCanvas = useCallback(()=>{\n        if (!fabricCanvasRef.current || !template) {\n            // Fallback to template thumbnail\n            if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n            return;\n        }\n        try {\n            const canvas = fabricCanvasRef.current;\n            // Calculate appropriate multiplier for good quality but reasonable size\n            const maxPreviewSize = 800; // Max width or height for preview\n            const templateMaxDimension = Math.max(template.width, template.height);\n            const multiplier = Math.min(1, maxPreviewSize / templateMaxDimension);\n            const dataURL = canvas.toDataURL({\n                format: \"png\",\n                quality: 0.9,\n                multiplier: multiplier\n            });\n            setPreviewUrl(dataURL);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n            // Fallback to template thumbnail\n            if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n        }\n    }, [\n        template\n    ]);\n    // Apply customizations to canvas objects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!fabricCanvasRef.current || !template || !template.editableLayers) return;\n        // Check if there are any actual customizations\n        const hasCustomizations = Object.values(customizations).some((value)=>value && value.trim() !== \"\");\n        if (!hasCustomizations) {\n            // No customizations, keep using the template thumbnail\n            if (template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n            return;\n        }\n        try {\n            const canvas = fabricCanvasRef.current;\n            const objects = canvas.getObjects();\n            let hasChanges = false;\n            // Apply customizations to editable objects\n            template.editableLayers.forEach((layer)=>{\n                const customValue = customizations[layer.id];\n                if (!customValue || customValue === layer.originalValue) return;\n                // Find the canvas object by ID\n                const canvasObject = objects.find((obj)=>obj.id === layer.id);\n                if (!canvasObject) return;\n                hasChanges = true;\n                if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                    // Apply text customization\n                    canvasObject.set(\"text\", customValue);\n                } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                    // Apply image customization\n                    fabric.Image.fromURL(customValue, (img)=>{\n                        if (!fabricCanvasRef.current) return;\n                        try {\n                            // Scale image to fit the object bounds\n                            const scaleX = canvasObject.width / img.width;\n                            const scaleY = canvasObject.height / img.height;\n                            const scale = Math.min(scaleX, scaleY);\n                            img.set({\n                                left: canvasObject.left,\n                                top: canvasObject.top,\n                                scaleX: scale,\n                                scaleY: scale,\n                                originX: canvasObject.originX,\n                                originY: canvasObject.originY,\n                                id: layer.id\n                            });\n                            // Replace the object\n                            fabricCanvasRef.current.remove(canvasObject);\n                            fabricCanvasRef.current.add(img);\n                            fabricCanvasRef.current.renderAll();\n                            generatePreviewFromCanvas();\n                        } catch (error) {\n                            console.error(\"Error applying image customization:\", error);\n                        }\n                    });\n                    return; // Skip the renderAll below since it's handled in the callback\n                }\n            });\n            if (hasChanges) {\n                canvas.renderAll();\n                generatePreviewFromCanvas();\n            }\n        } catch (error) {\n            console.error(\"Error applying customizations:\", error);\n        }\n    }, [\n        customizations,\n        template,\n        generatePreviewFromCanvas\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Generate preview from current canvas state\n            generatePreviewFromCanvas();\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // In a real implementation, you'd get a download URL from the API\n            const link = document.createElement(\"a\");\n            link.href = (template === null || template === void 0 ? void 0 : template.thumbnailUrl) || \"\";\n            link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n            link.click();\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 356,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Customize Elements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: customizations[layer.id] || \"\",\n                                                                onChange: (e)=>handleTextChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 488,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 494,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 495,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 493,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center bg-gray-100 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-lg overflow-hidden flex items-center justify-center\",\n                                                style: {\n                                                    aspectRatio: \"\".concat(template.width, \"/\").concat(template.height),\n                                                    maxWidth: \"100%\",\n                                                    maxHeight: \"500px\",\n                                                    width: \"auto\",\n                                                    height: \"auto\"\n                                                },\n                                                children: previewUrl || template.thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: previewUrl || template.thumbnailUrl || \"\",\n                                                    alt: \"Template preview\",\n                                                    className: \"max-w-full max-h-full object-contain\",\n                                                    style: {\n                                                        width: \"auto\",\n                                                        height: \"auto\",\n                                                        maxWidth: \"100%\",\n                                                        maxHeight: \"100%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center bg-gray-50 w-full h-full min-h-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    display: \"none\"\n                },\n                width: (template === null || template === void 0 ? void 0 : template.width) || 800,\n                height: (template === null || template === void 0 ? void 0 : template.height) || 600\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 569,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 373,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"99Kx8dSoZ8kCDFnm2IQBCHtAsQg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcHVibGljL1twcm9qZWN0SWRdL2N1c3RvbWl6ZS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNXO0FBQ3VDO0FBRTlDO0FBQ0Y7QUFDQTtBQUNrQztBQUNsQztBQUsvQixTQUFTa0I7O0lBQ3RCLE1BQU1DLFNBQVNqQiwwREFBU0E7SUFDeEIsTUFBTWtCLFNBQVNqQiwwREFBU0E7SUFDeEIsTUFBTSxDQUFDa0IsVUFBVUMsWUFBWSxHQUFHckIsK0NBQVFBLENBQTBCO0lBQ2xFLE1BQU0sQ0FBQ3NCLFNBQVNDLFdBQVcsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3dCLE9BQU9DLFNBQVMsR0FBR3pCLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUMwQixnQkFBZ0JDLGtCQUFrQixHQUFHM0IsK0NBQVFBLENBQXlCLENBQUM7SUFDOUUsTUFBTSxDQUFDNEIsWUFBWUMsY0FBYyxHQUFHN0IsK0NBQVFBLENBQWdCO0lBQzVELE1BQU0sQ0FBQzhCLHFCQUFxQkMsdUJBQXVCLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNnQyxlQUFlQyxpQkFBaUIsR0FBR2pDLCtDQUFRQSxDQUFDO0lBRW5ERCxnREFBU0EsQ0FBQztRQUNSLE1BQU1tQyxnQkFBZ0I7WUFDcEIsSUFBSTtnQkFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sd0JBQXlDLE9BQWpCbEIsT0FBT21CLFNBQVM7Z0JBRXJFLElBQUksQ0FBQ0YsU0FBU0csRUFBRSxFQUFFO29CQUNoQixJQUFJSCxTQUFTSSxNQUFNLEtBQUssS0FBSzt3QkFDM0JkLFNBQVM7b0JBQ1gsT0FBTzt3QkFDTEEsU0FBUztvQkFDWDtvQkFDQTtnQkFDRjtnQkFFQSxNQUFNZSxPQUFPLE1BQU1MLFNBQVNNLElBQUk7Z0JBQ2hDLE1BQU1DLGVBQWVGLEtBQUtBLElBQUk7Z0JBRTlCLG9DQUFvQztnQkFDcEMsSUFBSSxDQUFDRSxhQUFhQyxjQUFjLElBQUksQ0FBQ0QsYUFBYUUsY0FBYyxFQUFFO29CQUNoRW5CLFNBQVM7b0JBQ1Q7Z0JBQ0Y7Z0JBRUEsK0JBQStCO2dCQUMvQixJQUFJbUIsaUJBQWtDLEVBQUU7Z0JBQ3hDLElBQUk7b0JBQ0ZBLGlCQUFpQkYsYUFBYUUsY0FBYyxHQUFHQyxLQUFLQyxLQUFLLENBQUNKLGFBQWFFLGNBQWMsSUFBSSxFQUFFO2dCQUM3RixFQUFFLE9BQU9wQixPQUFPO29CQUNkdUIsUUFBUXZCLEtBQUssQ0FBQyxrQ0FBa0NBO29CQUNoRG9CLGlCQUFpQixFQUFFO2dCQUNyQjtnQkFFQXZCLFlBQVk7b0JBQ1YsR0FBR3FCLFlBQVk7b0JBQ2ZFO2dCQUNGO2dCQUVBLGlEQUFpRDtnQkFDakQsTUFBTUksd0JBQWdELENBQUM7Z0JBQ3ZESixlQUFlSyxPQUFPLENBQUNDLENBQUFBO29CQUNyQkYscUJBQXFCLENBQUNFLE1BQU1DLEVBQUUsQ0FBQyxHQUFHRCxNQUFNRSxhQUFhLElBQUk7Z0JBQzNEO2dCQUNBekIsa0JBQWtCcUI7WUFFcEIsRUFBRSxPQUFPSyxLQUFLO2dCQUNaNUIsU0FBUztZQUNYLFNBQVU7Z0JBQ1JGLFdBQVc7WUFDYjtRQUNGO1FBRUEsSUFBSUwsT0FBT21CLFNBQVMsRUFBRTtZQUNwQkg7UUFDRjtJQUNGLEdBQUc7UUFBQ2hCLE9BQU9tQixTQUFTO0tBQUM7SUFFckIsTUFBTWlCLG1CQUFtQixDQUFDQyxTQUFpQkM7UUFDekM3QixrQkFBa0I4QixDQUFBQSxPQUFTO2dCQUN6QixHQUFHQSxJQUFJO2dCQUNQLENBQUNGLFFBQVEsRUFBRUM7WUFDYjtJQUNGO0lBRUEsTUFBTUUsb0JBQW9CLE9BQU9ILFNBQWlCSTtRQUNoRCwwQ0FBMEM7UUFDMUMsTUFBTUMsV0FBV0MsSUFBSUMsZUFBZSxDQUFDSDtRQUVyQyxvQ0FBb0M7UUFDcENoQyxrQkFBa0I4QixDQUFBQSxPQUFTO2dCQUN6QixHQUFHQSxJQUFJO2dCQUNQLENBQUNGLFFBQVEsRUFBRUs7WUFDYjtJQUVBLHNFQUFzRTtJQUN0RSxtREFBbUQ7SUFDbkQsb0VBQW9FO0lBQ3RFO0lBRUEscURBQXFEO0lBQ3JEN0QsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNxQixVQUFVO1FBRWYsaUVBQWlFO1FBQ2pFLElBQUlBLFNBQVMyQyxZQUFZLEVBQUU7WUFDekJsQyxjQUFjVCxTQUFTMkMsWUFBWTtZQUNuQ0MscUJBQXFCO1FBQ3ZCO1FBRUEsbUZBQW1GO1FBQ25GLElBQUksQ0FBQzVDLFNBQVN3QixjQUFjLElBQUl4QixTQUFTd0IsY0FBYyxDQUFDcUIsTUFBTSxLQUFLLEdBQUc7WUFDcEU7UUFDRjtRQUVBLDJCQUEyQjtRQUMzQixJQUFJQyxnQkFBZ0JDLE9BQU8sRUFBRTtZQUMzQixJQUFJO2dCQUNGRCxnQkFBZ0JDLE9BQU8sQ0FBQ0MsT0FBTztZQUNqQyxFQUFFLE9BQU81QyxPQUFPO2dCQUNkdUIsUUFBUXNCLElBQUksQ0FBQywyQkFBMkI3QztZQUMxQztZQUNBMEMsZ0JBQWdCQyxPQUFPLEdBQUc7UUFDNUI7UUFFQSxNQUFNRyxhQUFhO1lBQ2pCLElBQUksQ0FBQ0MsVUFBVUosT0FBTyxJQUFJLENBQUMvQyxVQUFVO1lBRXJDLElBQUk7Z0JBQ0YsbURBQW1EO2dCQUNuRCxNQUFNb0QsU0FBUyxJQUFJQyxPQUFPQyxNQUFNLENBQUNILFVBQVVKLE9BQU8sRUFBRTtvQkFDbERRLE9BQU92RCxTQUFTdUQsS0FBSztvQkFDckJDLFFBQVF4RCxTQUFTd0QsTUFBTTtvQkFDdkJDLFdBQVc7b0JBQ1hDLHdCQUF3QjtvQkFDeEJDLGlCQUFpQjtnQkFDbkI7Z0JBRUFiLGdCQUFnQkMsT0FBTyxHQUFHSztnQkFFMUIsa0NBQWtDO2dCQUNsQyxJQUFJcEQsU0FBU3FCLElBQUksRUFBRTtvQkFDakIsSUFBSTt3QkFDRixNQUFNdUMsZUFBZW5DLEtBQUtDLEtBQUssQ0FBQzFCLFNBQVNxQixJQUFJO3dCQUU3QyxNQUFNLElBQUl3QyxRQUFjLENBQUNDLFNBQVNDOzRCQUNoQ1gsT0FBT1ksWUFBWSxDQUFDSixjQUFjO2dDQUNoQyxJQUFJO29DQUNGLDhEQUE4RDtvQ0FDOUQsTUFBTUssVUFBVWIsT0FBT2MsVUFBVTtvQ0FDakMsTUFBTUMsWUFBWUYsUUFBUUcsSUFBSSxDQUFDLENBQUNDLE1BQWFBLElBQUlDLElBQUksS0FBSztvQ0FFMUQsSUFBSUgsV0FBVzt3Q0FDYmYsT0FBT21CLE1BQU0sQ0FBQ0o7b0NBQ2hCO29DQUVBLDJDQUEyQztvQ0FDM0NGLFFBQVFwQyxPQUFPLENBQUMsQ0FBQ3dDO3dDQUNmLElBQUlBLElBQUlDLElBQUksS0FBSyxVQUFVRCxJQUFJRyxVQUFVLEtBQUssT0FBTzs0Q0FDbkRwQixPQUFPbUIsTUFBTSxDQUFDRjt3Q0FDaEI7b0NBQ0Y7b0NBRUFqQixPQUFPcUIsU0FBUztvQ0FDaEJYO2dDQUNGLEVBQUUsT0FBTzFELE9BQU87b0NBQ2QyRCxPQUFPM0Q7Z0NBQ1Q7NEJBQ0YsR0FBRyxDQUFDQTtnQ0FDRjJELE9BQU8zRDs0QkFDVDt3QkFDRjtvQkFDRixFQUFFLE9BQU9zRSxXQUFXO3dCQUNsQi9DLFFBQVF2QixLQUFLLENBQUMsZ0NBQWdDc0U7b0JBQ2hEO2dCQUNGO1lBRUYsRUFBRSxPQUFPdEUsT0FBTztnQkFDZHVCLFFBQVF2QixLQUFLLENBQUMsZ0NBQWdDQTtZQUNoRDtRQUNGO1FBRUEsOENBQThDO1FBQzlDLE1BQU11RSxZQUFZQyxXQUFXMUIsWUFBWTtRQUV6QyxPQUFPO1lBQ0wyQixhQUFhRjtZQUNiLElBQUk3QixnQkFBZ0JDLE9BQU8sRUFBRTtnQkFDM0IsSUFBSTtvQkFDRkQsZ0JBQWdCQyxPQUFPLENBQUNDLE9BQU87Z0JBQ2pDLEVBQUUsT0FBTzVDLE9BQU87b0JBQ2R1QixRQUFRc0IsSUFBSSxDQUFDLHNDQUFzQzdDO2dCQUNyRDtnQkFDQTBDLGdCQUFnQkMsT0FBTyxHQUFHO1lBQzVCO1FBQ0Y7SUFDRixHQUFHO1FBQUMvQztLQUFTO0lBRWIsK0JBQStCO0lBQy9CLE1BQU04RSw0QkFBNEJDLFlBQVk7UUFDNUMsSUFBSSxDQUFDakMsZ0JBQWdCQyxPQUFPLElBQUksQ0FBQy9DLFVBQVU7WUFDekMsaUNBQWlDO1lBQ2pDLElBQUlBLHFCQUFBQSwrQkFBQUEsU0FBVTJDLFlBQVksRUFBRTtnQkFDMUJsQyxjQUFjVCxTQUFTMkMsWUFBWTtZQUNyQztZQUNBO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTVMsU0FBU04sZ0JBQWdCQyxPQUFPO1lBRXRDLHdFQUF3RTtZQUN4RSxNQUFNaUMsaUJBQWlCLEtBQUssa0NBQWtDO1lBQzlELE1BQU1DLHVCQUF1QkMsS0FBS0MsR0FBRyxDQUFDbkYsU0FBU3VELEtBQUssRUFBRXZELFNBQVN3RCxNQUFNO1lBQ3JFLE1BQU00QixhQUFhRixLQUFLRyxHQUFHLENBQUMsR0FBR0wsaUJBQWlCQztZQUVoRCxNQUFNSyxVQUFVbEMsT0FBT21DLFNBQVMsQ0FBQztnQkFDL0JDLFFBQVE7Z0JBQ1JDLFNBQVM7Z0JBQ1RMLFlBQVlBO1lBQ2Q7WUFDQTNFLGNBQWM2RTtRQUNoQixFQUFFLE9BQU9sRixPQUFPO1lBQ2R1QixRQUFRdkIsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0MsaUNBQWlDO1lBQ2pDLElBQUlKLHFCQUFBQSwrQkFBQUEsU0FBVTJDLFlBQVksRUFBRTtnQkFDMUJsQyxjQUFjVCxTQUFTMkMsWUFBWTtZQUNyQztRQUNGO0lBQ0YsR0FBRztRQUFDM0M7S0FBUztJQUViLHlDQUF5QztJQUN6Q3JCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDbUUsZ0JBQWdCQyxPQUFPLElBQUksQ0FBQy9DLFlBQVksQ0FBQ0EsU0FBU3dCLGNBQWMsRUFBRTtRQUV2RSwrQ0FBK0M7UUFDL0MsTUFBTWtFLG9CQUFvQkMsT0FBT0MsTUFBTSxDQUFDdEYsZ0JBQWdCdUYsSUFBSSxDQUFDekQsQ0FBQUEsUUFBU0EsU0FBU0EsTUFBTTBELElBQUksT0FBTztRQUVoRyxJQUFJLENBQUNKLG1CQUFtQjtZQUN0Qix1REFBdUQ7WUFDdkQsSUFBSTFGLFNBQVMyQyxZQUFZLEVBQUU7Z0JBQ3pCbEMsY0FBY1QsU0FBUzJDLFlBQVk7WUFDckM7WUFDQTtRQUNGO1FBRUEsSUFBSTtZQUNGLE1BQU1TLFNBQVNOLGdCQUFnQkMsT0FBTztZQUN0QyxNQUFNa0IsVUFBVWIsT0FBT2MsVUFBVTtZQUNqQyxJQUFJNkIsYUFBYTtZQUVqQiwyQ0FBMkM7WUFDM0MvRixTQUFTd0IsY0FBYyxDQUFDSyxPQUFPLENBQUNDLENBQUFBO2dCQUM5QixNQUFNa0UsY0FBYzFGLGNBQWMsQ0FBQ3dCLE1BQU1DLEVBQUUsQ0FBQztnQkFDNUMsSUFBSSxDQUFDaUUsZUFBZUEsZ0JBQWdCbEUsTUFBTUUsYUFBYSxFQUFFO2dCQUV6RCwrQkFBK0I7Z0JBQy9CLE1BQU1pRSxlQUFlaEMsUUFBUUcsSUFBSSxDQUFDLENBQUNDLE1BQWFBLElBQUl0QyxFQUFFLEtBQUtELE1BQU1DLEVBQUU7Z0JBQ25FLElBQUksQ0FBQ2tFLGNBQWM7Z0JBRW5CRixhQUFhO2dCQUViLElBQUlqRSxNQUFNb0UsSUFBSSxLQUFLLFVBQVVELGFBQWFDLElBQUksS0FBSyxXQUFXO29CQUM1RCwyQkFBMkI7b0JBQzFCRCxhQUFnQ0UsR0FBRyxDQUFDLFFBQVFIO2dCQUMvQyxPQUFPLElBQUlsRSxNQUFNb0UsSUFBSSxLQUFLLFdBQVdGLFlBQVlJLFVBQVUsQ0FBQyxVQUFVO29CQUNwRSw0QkFBNEI7b0JBQzVCL0MsT0FBT2xFLEtBQUssQ0FBQ2tILE9BQU8sQ0FBQ0wsYUFBYSxDQUFDTTt3QkFDakMsSUFBSSxDQUFDeEQsZ0JBQWdCQyxPQUFPLEVBQUU7d0JBRTlCLElBQUk7NEJBQ0YsdUNBQXVDOzRCQUN2QyxNQUFNd0QsU0FBU04sYUFBYTFDLEtBQUssR0FBSStDLElBQUkvQyxLQUFLOzRCQUM5QyxNQUFNaUQsU0FBU1AsYUFBYXpDLE1BQU0sR0FBSThDLElBQUk5QyxNQUFNOzRCQUNoRCxNQUFNaUQsUUFBUXZCLEtBQUtHLEdBQUcsQ0FBQ2tCLFFBQVFDOzRCQUUvQkYsSUFBSUgsR0FBRyxDQUFDO2dDQUNOTyxNQUFNVCxhQUFhUyxJQUFJO2dDQUN2QkMsS0FBS1YsYUFBYVUsR0FBRztnQ0FDckJKLFFBQVFFO2dDQUNSRCxRQUFRQztnQ0FDUkcsU0FBU1gsYUFBYVcsT0FBTztnQ0FDN0JDLFNBQVNaLGFBQWFZLE9BQU87Z0NBQzdCOUUsSUFBSUQsTUFBTUMsRUFBRTs0QkFDZDs0QkFFQSxxQkFBcUI7NEJBQ3JCZSxnQkFBZ0JDLE9BQU8sQ0FBQ3dCLE1BQU0sQ0FBQzBCOzRCQUMvQm5ELGdCQUFnQkMsT0FBTyxDQUFDK0QsR0FBRyxDQUFDUjs0QkFDNUJ4RCxnQkFBZ0JDLE9BQU8sQ0FBQzBCLFNBQVM7NEJBQ2pDSzt3QkFDRixFQUFFLE9BQU8xRSxPQUFPOzRCQUNkdUIsUUFBUXZCLEtBQUssQ0FBQyx1Q0FBdUNBO3dCQUN2RDtvQkFDRjtvQkFDQSxRQUFRLDhEQUE4RDtnQkFDeEU7WUFDRjtZQUVBLElBQUkyRixZQUFZO2dCQUNkM0MsT0FBT3FCLFNBQVM7Z0JBQ2hCSztZQUNGO1FBQ0YsRUFBRSxPQUFPMUUsT0FBTztZQUNkdUIsUUFBUXZCLEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ2xEO0lBQ0YsR0FBRztRQUFDRTtRQUFnQk47UUFBVThFO0tBQTBCO0lBRXhELE1BQU1pQyxrQkFBa0I7UUFDdEJwRyx1QkFBdUI7UUFDdkIsSUFBSTtZQUNGLDZDQUE2QztZQUM3Q21FO1lBQ0Esb0NBQW9DO1lBQ3BDLE1BQU0sSUFBSWpCLFFBQVFDLENBQUFBLFVBQVdjLFdBQVdkLFNBQVM7UUFDbkQsRUFBRSxPQUFPN0IsS0FBSztZQUNaTixRQUFRdkIsS0FBSyxDQUFDLCtCQUErQjZCO1FBQy9DLFNBQVU7WUFDUnRCLHVCQUF1QjtRQUN6QjtJQUNGO0lBRUEsTUFBTXFHLHFCQUFxQjtRQUN6Qm5HLGlCQUFpQjtRQUNqQixJQUFJO1lBQ0Ysd0VBQXdFO1lBQ3hFLDZCQUE2QjtZQUM3QixNQUFNLElBQUlnRCxRQUFRQyxDQUFBQSxVQUFXYyxXQUFXZCxTQUFTO1lBRWpELGtFQUFrRTtZQUNsRSxNQUFNbUQsT0FBT0MsU0FBU0MsYUFBYSxDQUFDO1lBQ3BDRixLQUFLRyxJQUFJLEdBQUdwSCxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVUyQyxZQUFZLEtBQUk7WUFDdENzRSxLQUFLSSxRQUFRLEdBQUcsY0FBeUMsT0FBM0JySCxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVzRSxJQUFJLEtBQUksVUFBUztZQUN6RDJDLEtBQUtLLEtBQUs7UUFDWixFQUFFLE9BQU9yRixLQUFLO1lBQ1pOLFFBQVF2QixLQUFLLENBQUMsdUJBQXVCNkI7UUFDdkMsU0FBVTtZQUNScEIsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxJQUFJWCxTQUFTO1FBQ1gscUJBQ0UsOERBQUNxSDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDekksaUhBQU9BO2dCQUFDeUksV0FBVTs7Ozs7Ozs7Ozs7SUFHekI7SUFFQSxJQUFJcEgsU0FBUyxDQUFDSixVQUFVO1FBQ3RCLHFCQUNFLDhEQUFDdUg7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQ1hwSCxTQUFTOzs7Ozs7c0NBRVosOERBQUNmLHlEQUFNQTs0QkFBQ3FJLFNBQVMsSUFBTTNILE9BQU80SCxJQUFJLENBQUM7NEJBQVlDLFNBQVE7OzhDQUNyRCw4REFBQzVJLGlIQUFTQTtvQ0FBQ3dJLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBT2xEO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDbkkseURBQU1BO3dDQUNMcUksU0FBUyxJQUFNM0gsT0FBTzRILElBQUksQ0FBQyxXQUE0QixPQUFqQjdILE9BQU9tQixTQUFTO3dDQUN0RDJHLFNBQVE7d0NBQ1JDLE1BQUs7OzBEQUVMLDhEQUFDN0ksaUhBQVNBO2dEQUFDd0ksV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7OztrREFHeEMsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNEOzswREFDQyw4REFBQ0U7Z0RBQUdELFdBQVU7O29EQUFzQztvREFDdEN4SCxTQUFTc0UsSUFBSTs7Ozs7OzswREFFM0IsOERBQUN3RDtnREFBRU4sV0FBVTswREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLekMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ25JLHlEQUFNQTt3Q0FDTHFJLFNBQVNYO3dDQUNUZ0IsVUFBVXJIO3dDQUNWa0gsU0FBUTs7NENBRVBsSCxvQ0FDQyw4REFBQzNCLGlIQUFPQTtnREFBQ3lJLFdBQVU7Ozs7O3VEQUNqQjs0Q0FBSzs7Ozs7OztrREFHWCw4REFBQ25JLHlEQUFNQTt3Q0FDTHFJLFNBQVNWO3dDQUNUZSxVQUFVbkg7d0NBQ1Y0RyxXQUFVOzs0Q0FFVDVHLDhCQUNDLDhEQUFDN0IsaUhBQU9BO2dEQUFDeUksV0FBVTs7Ozs7cUVBRW5CLDhEQUFDdkksa0hBQVFBO2dEQUFDdUksV0FBVTs7Ozs7OzRDQUNwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU1osOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDaEkscURBQUlBOztrREFDSCw4REFBQ0UsMkRBQVVBO2tEQUNULDRFQUFDQywwREFBU0E7NENBQUM2SCxXQUFVO3NEQUFVOzs7Ozs7Ozs7OztrREFFakMsOERBQUMvSCw0REFBV0E7d0NBQUMrSCxXQUFVO2tEQUNwQnhILFNBQVN3QixjQUFjLENBQUN3RyxHQUFHLENBQUMsQ0FBQ2xHO2dEQXVCVEEsb0JBR1pBLHFCQUVJeEIsMEJBWU93QixtQ0FBQUEscUJBb0NUQTtpRUEzRVQsOERBQUN5RjtnREFBbUJDLFdBQVU7O2tFQUM1Qiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDNUgsdURBQUtBO2dFQUFDZ0ksU0FBUzlGLE1BQU1vRSxJQUFJLEtBQUssU0FBUyxZQUFZOztvRUFDakRwRSxNQUFNb0UsSUFBSSxLQUFLLHVCQUNkLDhEQUFDaEgsa0hBQUlBO3dFQUFDc0ksV0FBVTs7Ozs7NkZBRWhCLDhEQUFDcEksa0hBQVNBO3dFQUFDb0ksV0FBVTs7Ozs7O29FQUV0QjFGLE1BQU1vRSxJQUFJOzs7Ozs7OzBFQUViLDhEQUFDK0I7Z0VBQUtULFdBQVU7MEVBQXVCMUYsTUFBTXdDLElBQUk7Ozs7Ozs7Ozs7OztvREFHbER4QyxNQUFNb0UsSUFBSSxLQUFLLHVCQUNkLDhEQUFDcUI7OzBFQUNDLDhEQUFDaEksdURBQUtBO2dFQUFDaUksV0FBVTswRUFDZDFGLE1BQU1vRyxXQUFXLElBQUk7Ozs7OzswRUFFeEIsOERBQUM1SSx1REFBS0E7Z0VBQ0o4QyxPQUFPOUIsY0FBYyxDQUFDd0IsTUFBTUMsRUFBRSxDQUFDLElBQUk7Z0VBQ25Db0csVUFBVSxDQUFDQyxJQUFNbEcsaUJBQWlCSixNQUFNQyxFQUFFLEVBQUVxRyxFQUFFQyxNQUFNLENBQUNqRyxLQUFLO2dFQUMxRDhGLGFBQWFwRyxNQUFNb0csV0FBVztnRUFDOUJJLFNBQVMsR0FBRXhHLHFCQUFBQSxNQUFNeUcsV0FBVyxjQUFqQnpHLHlDQUFBQSxtQkFBbUJ3RyxTQUFTO2dFQUN2Q2QsV0FBVTs7Ozs7OzREQUVYMUYsRUFBQUEsc0JBQUFBLE1BQU15RyxXQUFXLGNBQWpCekcsMENBQUFBLG9CQUFtQndHLFNBQVMsbUJBQzNCLDhEQUFDUjtnRUFBRU4sV0FBVTs7b0VBQ1ZsSCxFQUFBQSwyQkFBQUEsY0FBYyxDQUFDd0IsTUFBTUMsRUFBRSxDQUFDLGNBQXhCekIsK0NBQUFBLHlCQUEwQnVDLE1BQU0sS0FBSTtvRUFBRTtvRUFBRWYsTUFBTXlHLFdBQVcsQ0FBQ0QsU0FBUztvRUFBQzs7Ozs7Ozs7Ozs7OzZFQUszRSw4REFBQ2Y7OzBFQUNDLDhEQUFDaEksdURBQUtBO2dFQUFDaUksV0FBVTswRUFBd0I7Ozs7OzswRUFHekMsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ2dCO3dFQUNDdEMsTUFBSzt3RUFDTHVDLFFBQVEzRyxFQUFBQSxzQkFBQUEsTUFBTXlHLFdBQVcsY0FBakJ6RywyQ0FBQUEsb0NBQUFBLG9CQUFtQjRHLGNBQWMsY0FBakM1Ryx3REFBQUEsa0NBQW1Da0csR0FBRyxDQUFDVyxDQUFBQSxJQUFLLElBQU0sT0FBRkEsSUFBS0MsSUFBSSxDQUFDLFNBQVE7d0VBQzFFVCxVQUFVLENBQUNDO2dGQUNJQTs0RUFBYixNQUFNN0YsUUFBTzZGLGtCQUFBQSxFQUFFQyxNQUFNLENBQUNRLEtBQUssY0FBZFQsc0NBQUFBLGVBQWdCLENBQUMsRUFBRTs0RUFDaEMsSUFBSTdGLE1BQU07Z0ZBQ1JELGtCQUFrQlIsTUFBTUMsRUFBRSxFQUFFUTs0RUFDOUI7d0VBQ0Y7d0VBQ0FpRixXQUFVOzs7Ozs7b0VBSVhsSCxjQUFjLENBQUN3QixNQUFNQyxFQUFFLENBQUMsa0JBQ3ZCLDhEQUFDd0Y7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDbEI7Z0ZBQ0N3QyxLQUFLeEksY0FBYyxDQUFDd0IsTUFBTUMsRUFBRSxDQUFDO2dGQUM3QmdILEtBQUk7Z0ZBQ0p2QixXQUFVOzs7Ozs7MEZBRVosOERBQUNEO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ007d0ZBQUVOLFdBQVU7a0dBQXdCOzs7Ozs7a0dBQ3JDLDhEQUFDd0I7d0ZBQ0N0QixTQUFTOzRGQUNQbkgsa0JBQWtCOEIsQ0FBQUE7Z0dBQ2hCLE1BQU00RyxVQUFVO29HQUFFLEdBQUc1RyxJQUFJO2dHQUFDO2dHQUMxQixPQUFPNEcsT0FBTyxDQUFDbkgsTUFBTUMsRUFBRSxDQUFDO2dHQUN4QixPQUFPa0g7NEZBQ1Q7d0ZBQ0Y7d0ZBQ0F6QixXQUFVO2tHQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0VBT04xRixFQUFBQSxzQkFBQUEsTUFBTXlHLFdBQVcsY0FBakJ6RywwQ0FBQUEsb0JBQW1Cb0gsV0FBVyxtQkFDN0IsOERBQUNwQjt3RUFBRU4sV0FBVTs7NEVBQXdCOzRFQUN4QnRDLEtBQUtpRSxLQUFLLENBQUNySCxNQUFNeUcsV0FBVyxDQUFDVyxXQUFXLEdBQUcsT0FBTzs0RUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0NBN0VyRXBILE1BQU1DLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBMEYxQiw4REFBQ3dGOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDaEkscURBQUlBOztrREFDSCw4REFBQ0UsMkRBQVVBO2tEQUNULDRFQUFDQywwREFBU0E7NENBQUM2SCxXQUFVO3NEQUFVOzs7Ozs7Ozs7OztrREFFakMsOERBQUMvSCw0REFBV0E7a0RBQ1YsNEVBQUM4SDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQ0NDLFdBQVU7Z0RBQ1Y0QixPQUFPO29EQUNMQyxhQUFhLEdBQXFCckosT0FBbEJBLFNBQVN1RCxLQUFLLEVBQUMsS0FBbUIsT0FBaEJ2RCxTQUFTd0QsTUFBTTtvREFDakQ4RixVQUFVO29EQUNWQyxXQUFXO29EQUNYaEcsT0FBTztvREFDUEMsUUFBUTtnREFDVjswREFFQ2hELGNBQWNSLFNBQVMyQyxZQUFZLGlCQUNsQyw4REFBQzJEO29EQUNDd0MsS0FBS3RJLGNBQWNSLFNBQVMyQyxZQUFZLElBQUk7b0RBQzVDb0csS0FBSTtvREFDSnZCLFdBQVU7b0RBQ1Y0QixPQUFPO3dEQUNMN0YsT0FBTzt3REFDUEMsUUFBUTt3REFDUjhGLFVBQVU7d0RBQ1ZDLFdBQVc7b0RBQ2I7Ozs7O3lFQUdGLDhEQUFDaEM7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNwSSxrSEFBU0E7d0RBQUNvSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBWXZDLDhEQUFDcEU7Z0JBQ0NvRyxLQUFLckc7Z0JBQ0xpRyxPQUFPO29CQUFFSyxTQUFTO2dCQUFPO2dCQUN6QmxHLE9BQU92RCxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVV1RCxLQUFLLEtBQUk7Z0JBQzFCQyxRQUFReEQsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVd0QsTUFBTSxLQUFJOzs7Ozs7Ozs7Ozs7QUFJcEM7R0FqakJ3QjNEOztRQUNQaEIsc0RBQVNBO1FBQ1RDLHNEQUFTQTs7O0tBRkZlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcHVibGljL1twcm9qZWN0SWRdL2N1c3RvbWl6ZS9wYWdlLnRzeD8xOWQxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VQYXJhbXMsIHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcbmltcG9ydCB7IExvYWRlcjIsIEFycm93TGVmdCwgRG93bmxvYWQsIFVwbG9hZCwgVHlwZSwgSW1hZ2UgYXMgSW1hZ2VJY29uIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiO1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCI7XG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvbGFiZWxcIjtcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIjtcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiO1xuXG5pbXBvcnQgeyBUZW1wbGF0ZVJlc3BvbnNlLCBFZGl0YWJsZUxheWVyIH0gZnJvbSBcIkAvdHlwZXMvdGVtcGxhdGVcIjtcbmltcG9ydCB7IEN1c3RvbWl6YXRpb25FZGl0b3IgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvY29tcG9uZW50cy9jdXN0b21pemF0aW9uLWVkaXRvclwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDdXN0b21pemVUZW1wbGF0ZVBhZ2UoKSB7XG4gIGNvbnN0IHBhcmFtcyA9IHVzZVBhcmFtcygpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgW3RlbXBsYXRlLCBzZXRUZW1wbGF0ZV0gPSB1c2VTdGF0ZTxUZW1wbGF0ZVJlc3BvbnNlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbY3VzdG9taXphdGlvbnMsIHNldEN1c3RvbWl6YXRpb25zXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIHN0cmluZz4+KHt9KTtcbiAgY29uc3QgW3ByZXZpZXdVcmwsIHNldFByZXZpZXdVcmxdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc0dlbmVyYXRpbmdQcmV2aWV3LCBzZXRJc0dlbmVyYXRpbmdQcmV2aWV3XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzRG93bmxvYWRpbmcsIHNldElzRG93bmxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgZmV0Y2hUZW1wbGF0ZSA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvcHJvamVjdHMvcHVibGljLyR7cGFyYW1zLnByb2plY3RJZH1gKTtcblxuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDA0KSB7XG4gICAgICAgICAgICBzZXRFcnJvcihcIlRlbXBsYXRlIG5vdCBmb3VuZCBvciBub3QgcHVibGljXCIpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzZXRFcnJvcihcIkZhaWxlZCB0byBsb2FkIHRlbXBsYXRlXCIpO1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zdCB0ZW1wbGF0ZURhdGEgPSBkYXRhLmRhdGE7XG5cbiAgICAgICAgLy8gQ2hlY2sgaWYgdGVtcGxhdGUgaXMgY3VzdG9taXphYmxlXG4gICAgICAgIGlmICghdGVtcGxhdGVEYXRhLmlzQ3VzdG9taXphYmxlIHx8ICF0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMpIHtcbiAgICAgICAgICBzZXRFcnJvcihcIlRoaXMgdGVtcGxhdGUgaXMgbm90IGN1c3RvbWl6YWJsZVwiKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyBQYXJzZSBlZGl0YWJsZSBsYXllcnMgc2FmZWx5XG4gICAgICAgIGxldCBlZGl0YWJsZUxheWVyczogRWRpdGFibGVMYXllcltdID0gW107XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgZWRpdGFibGVMYXllcnMgPSB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMgPyBKU09OLnBhcnNlKHRlbXBsYXRlRGF0YS5lZGl0YWJsZUxheWVycykgOiBbXTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIGVkaXRhYmxlIGxheWVyczonLCBlcnJvcik7XG4gICAgICAgICAgZWRpdGFibGVMYXllcnMgPSBbXTtcbiAgICAgICAgfVxuXG4gICAgICAgIHNldFRlbXBsYXRlKHtcbiAgICAgICAgICAuLi50ZW1wbGF0ZURhdGEsXG4gICAgICAgICAgZWRpdGFibGVMYXllcnMsXG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIEluaXRpYWxpemUgY3VzdG9taXphdGlvbnMgd2l0aCBvcmlnaW5hbCB2YWx1ZXNcbiAgICAgICAgY29uc3QgaW5pdGlhbEN1c3RvbWl6YXRpb25zOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge307XG4gICAgICAgIGVkaXRhYmxlTGF5ZXJzLmZvckVhY2gobGF5ZXIgPT4ge1xuICAgICAgICAgIGluaXRpYWxDdXN0b21pemF0aW9uc1tsYXllci5pZF0gPSBsYXllci5vcmlnaW5hbFZhbHVlIHx8ICcnO1xuICAgICAgICB9KTtcbiAgICAgICAgc2V0Q3VzdG9taXphdGlvbnMoaW5pdGlhbEN1c3RvbWl6YXRpb25zKTtcblxuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIHNldEVycm9yKFwiRmFpbGVkIHRvIGxvYWQgdGVtcGxhdGVcIik7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaWYgKHBhcmFtcy5wcm9qZWN0SWQpIHtcbiAgICAgIGZldGNoVGVtcGxhdGUoKTtcbiAgICB9XG4gIH0sIFtwYXJhbXMucHJvamVjdElkXSk7XG5cbiAgY29uc3QgaGFuZGxlVGV4dENoYW5nZSA9IChsYXllcklkOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRDdXN0b21pemF0aW9ucyhwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW2xheWVySWRdOiB2YWx1ZSxcbiAgICB9KSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VVcGxvYWQgPSBhc3luYyAobGF5ZXJJZDogc3RyaW5nLCBmaWxlOiBGaWxlKSA9PiB7XG4gICAgLy8gQ3JlYXRlIG9iamVjdCBVUkwgZm9yIGltbWVkaWF0ZSBwcmV2aWV3XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGZpbGUpO1xuXG4gICAgLy8gVXBkYXRlIGN1c3RvbWl6YXRpb25zIGltbWVkaWF0ZWx5XG4gICAgc2V0Q3VzdG9taXphdGlvbnMocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtsYXllcklkXTogaW1hZ2VVcmwsXG4gICAgfSkpO1xuXG4gICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB5b3UnZCB1cGxvYWQgdG8geW91ciBzdG9yYWdlIHNlcnZpY2UgaGVyZVxuICAgIC8vIGNvbnN0IHVwbG9hZGVkVXJsID0gYXdhaXQgdXBsb2FkVG9TdG9yYWdlKGZpbGUpO1xuICAgIC8vIHNldEN1c3RvbWl6YXRpb25zKHByZXYgPT4gKHsgLi4ucHJldiwgW2xheWVySWRdOiB1cGxvYWRlZFVybCB9KSk7XG4gIH07XG5cbiAgLy8gSW5pdGlhbGl6ZSBwcmV2aWV3IC0gc3RhcnQgd2l0aCB0ZW1wbGF0ZSB0aHVtYm5haWxcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXRlbXBsYXRlKSByZXR1cm47XG5cbiAgICAvLyBBbHdheXMgc3RhcnQgd2l0aCB0aGUgdGVtcGxhdGUgdGh1bWJuYWlsIGZvciBpbW1lZGlhdGUgcHJldmlld1xuICAgIGlmICh0ZW1wbGF0ZS50aHVtYm5haWxVcmwpIHtcbiAgICAgIHNldFByZXZpZXdVcmwodGVtcGxhdGUudGh1bWJuYWlsVXJsKTtcbiAgICAgIHNldENhbnZhc0luaXRpYWxpemVkKHRydWUpO1xuICAgIH1cblxuICAgIC8vIE9ubHkgaW5pdGlhbGl6ZSBGYWJyaWMuanMgY2FudmFzIGlmIHRoZXJlIGFyZSBlZGl0YWJsZSBsYXllcnMgYW5kIGN1c3RvbWl6YXRpb25zXG4gICAgaWYgKCF0ZW1wbGF0ZS5lZGl0YWJsZUxheWVycyB8fCB0ZW1wbGF0ZS5lZGl0YWJsZUxheWVycy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBDbGVhbiB1cCBleGlzdGluZyBjYW52YXNcbiAgICBpZiAoZmFicmljQ2FudmFzUmVmLmN1cnJlbnQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50LmRpc3Bvc2UoKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignRXJyb3IgZGlzcG9zaW5nIGNhbnZhczonLCBlcnJvcik7XG4gICAgICB9XG4gICAgICBmYWJyaWNDYW52YXNSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuXG4gICAgY29uc3QgaW5pdENhbnZhcyA9IGFzeW5jICgpID0+IHtcbiAgICAgIGlmICghY2FudmFzUmVmLmN1cnJlbnQgfHwgIXRlbXBsYXRlKSByZXR1cm47XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIENyZWF0ZSBhIHNpbXBsZSBjYW52YXMgZm9yIGN1c3RvbWl6YXRpb24gcHJldmlld1xuICAgICAgICBjb25zdCBjYW52YXMgPSBuZXcgZmFicmljLkNhbnZhcyhjYW52YXNSZWYuY3VycmVudCwge1xuICAgICAgICAgIHdpZHRoOiB0ZW1wbGF0ZS53aWR0aCxcbiAgICAgICAgICBoZWlnaHQ6IHRlbXBsYXRlLmhlaWdodCxcbiAgICAgICAgICBzZWxlY3Rpb246IGZhbHNlLFxuICAgICAgICAgIHByZXNlcnZlT2JqZWN0U3RhY2tpbmc6IHRydWUsXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLFxuICAgICAgICB9KTtcblxuICAgICAgICBmYWJyaWNDYW52YXNSZWYuY3VycmVudCA9IGNhbnZhcztcblxuICAgICAgICAvLyBMb2FkIHRlbXBsYXRlIEpTT04gaWYgYXZhaWxhYmxlXG4gICAgICAgIGlmICh0ZW1wbGF0ZS5qc29uKSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHRlbXBsYXRlSnNvbiA9IEpTT04ucGFyc2UodGVtcGxhdGUuanNvbik7XG5cbiAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlPHZvaWQ+KChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgICAgY2FudmFzLmxvYWRGcm9tSlNPTih0ZW1wbGF0ZUpzb24sICgpID0+IHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgLy8gUmVtb3ZlIHdvcmtzcGFjZS9jbGlwIG9iamVjdHMgdGhhdCBjYXVzZSBwb3NpdGlvbmluZyBpc3N1ZXNcbiAgICAgICAgICAgICAgICAgIGNvbnN0IG9iamVjdHMgPSBjYW52YXMuZ2V0T2JqZWN0cygpO1xuICAgICAgICAgICAgICAgICAgY29uc3Qgd29ya3NwYWNlID0gb2JqZWN0cy5maW5kKChvYmo6IGFueSkgPT4gb2JqLm5hbWUgPT09IFwiY2xpcFwiKTtcblxuICAgICAgICAgICAgICAgICAgaWYgKHdvcmtzcGFjZSkge1xuICAgICAgICAgICAgICAgICAgICBjYW52YXMucmVtb3ZlKHdvcmtzcGFjZSk7XG4gICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgIC8vIFJlbW92ZSBhbnkgb3RoZXIgZWRpdG9yLXNwZWNpZmljIG9iamVjdHNcbiAgICAgICAgICAgICAgICAgIG9iamVjdHMuZm9yRWFjaCgob2JqOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG9iai5uYW1lID09PSBcImNsaXBcIiB8fCBvYmouc2VsZWN0YWJsZSA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICAgICAgICBjYW52YXMucmVtb3ZlKG9iaik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgICBjYW52YXMucmVuZGVyQWxsKCk7XG4gICAgICAgICAgICAgICAgICByZXNvbHZlKCk7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgIHJlamVjdChlcnJvcik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9LCAoZXJyb3I6IGFueSkgPT4ge1xuICAgICAgICAgICAgICAgIHJlamVjdChlcnJvcik7XG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfSBjYXRjaCAoanNvbkVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIHRlbXBsYXRlIEpTT046JywganNvbkVycm9yKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGluaXRpYWxpemUgY2FudmFzOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgLy8gRGVsYXkgaW5pdGlhbGl6YXRpb24gdG8gZW5zdXJlIERPTSBpcyByZWFkeVxuICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoaW5pdENhbnZhcywgMjAwKTtcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKTtcbiAgICAgIGlmIChmYWJyaWNDYW52YXNSZWYuY3VycmVudCkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50LmRpc3Bvc2UoKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ0Vycm9yIGRpc3Bvc2luZyBjYW52YXMgaW4gY2xlYW51cDonLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICAgICAgZmFicmljQ2FudmFzUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgfVxuICAgIH07XG4gIH0sIFt0ZW1wbGF0ZV0pO1xuXG4gIC8vIEdlbmVyYXRlIHByZXZpZXcgZnJvbSBjYW52YXNcbiAgY29uc3QgZ2VuZXJhdGVQcmV2aWV3RnJvbUNhbnZhcyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoIWZhYnJpY0NhbnZhc1JlZi5jdXJyZW50IHx8ICF0ZW1wbGF0ZSkge1xuICAgICAgLy8gRmFsbGJhY2sgdG8gdGVtcGxhdGUgdGh1bWJuYWlsXG4gICAgICBpZiAodGVtcGxhdGU/LnRodW1ibmFpbFVybCkge1xuICAgICAgICBzZXRQcmV2aWV3VXJsKHRlbXBsYXRlLnRodW1ibmFpbFVybCk7XG4gICAgICB9XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNhbnZhcyA9IGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50O1xuXG4gICAgICAvLyBDYWxjdWxhdGUgYXBwcm9wcmlhdGUgbXVsdGlwbGllciBmb3IgZ29vZCBxdWFsaXR5IGJ1dCByZWFzb25hYmxlIHNpemVcbiAgICAgIGNvbnN0IG1heFByZXZpZXdTaXplID0gODAwOyAvLyBNYXggd2lkdGggb3IgaGVpZ2h0IGZvciBwcmV2aWV3XG4gICAgICBjb25zdCB0ZW1wbGF0ZU1heERpbWVuc2lvbiA9IE1hdGgubWF4KHRlbXBsYXRlLndpZHRoLCB0ZW1wbGF0ZS5oZWlnaHQpO1xuICAgICAgY29uc3QgbXVsdGlwbGllciA9IE1hdGgubWluKDEsIG1heFByZXZpZXdTaXplIC8gdGVtcGxhdGVNYXhEaW1lbnNpb24pO1xuXG4gICAgICBjb25zdCBkYXRhVVJMID0gY2FudmFzLnRvRGF0YVVSTCh7XG4gICAgICAgIGZvcm1hdDogJ3BuZycsXG4gICAgICAgIHF1YWxpdHk6IDAuOSxcbiAgICAgICAgbXVsdGlwbGllcjogbXVsdGlwbGllcixcbiAgICAgIH0pO1xuICAgICAgc2V0UHJldmlld1VybChkYXRhVVJMKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGdlbmVyYXRlIHByZXZpZXc6JywgZXJyb3IpO1xuICAgICAgLy8gRmFsbGJhY2sgdG8gdGVtcGxhdGUgdGh1bWJuYWlsXG4gICAgICBpZiAodGVtcGxhdGU/LnRodW1ibmFpbFVybCkge1xuICAgICAgICBzZXRQcmV2aWV3VXJsKHRlbXBsYXRlLnRodW1ibmFpbFVybCk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbdGVtcGxhdGVdKTtcblxuICAvLyBBcHBseSBjdXN0b21pemF0aW9ucyB0byBjYW52YXMgb2JqZWN0c1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZmFicmljQ2FudmFzUmVmLmN1cnJlbnQgfHwgIXRlbXBsYXRlIHx8ICF0ZW1wbGF0ZS5lZGl0YWJsZUxheWVycykgcmV0dXJuO1xuXG4gICAgLy8gQ2hlY2sgaWYgdGhlcmUgYXJlIGFueSBhY3R1YWwgY3VzdG9taXphdGlvbnNcbiAgICBjb25zdCBoYXNDdXN0b21pemF0aW9ucyA9IE9iamVjdC52YWx1ZXMoY3VzdG9taXphdGlvbnMpLnNvbWUodmFsdWUgPT4gdmFsdWUgJiYgdmFsdWUudHJpbSgpICE9PSAnJyk7XG5cbiAgICBpZiAoIWhhc0N1c3RvbWl6YXRpb25zKSB7XG4gICAgICAvLyBObyBjdXN0b21pemF0aW9ucywga2VlcCB1c2luZyB0aGUgdGVtcGxhdGUgdGh1bWJuYWlsXG4gICAgICBpZiAodGVtcGxhdGUudGh1bWJuYWlsVXJsKSB7XG4gICAgICAgIHNldFByZXZpZXdVcmwodGVtcGxhdGUudGh1bWJuYWlsVXJsKTtcbiAgICAgIH1cbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgY2FudmFzID0gZmFicmljQ2FudmFzUmVmLmN1cnJlbnQ7XG4gICAgICBjb25zdCBvYmplY3RzID0gY2FudmFzLmdldE9iamVjdHMoKTtcbiAgICAgIGxldCBoYXNDaGFuZ2VzID0gZmFsc2U7XG5cbiAgICAgIC8vIEFwcGx5IGN1c3RvbWl6YXRpb25zIHRvIGVkaXRhYmxlIG9iamVjdHNcbiAgICAgIHRlbXBsYXRlLmVkaXRhYmxlTGF5ZXJzLmZvckVhY2gobGF5ZXIgPT4ge1xuICAgICAgICBjb25zdCBjdXN0b21WYWx1ZSA9IGN1c3RvbWl6YXRpb25zW2xheWVyLmlkXTtcbiAgICAgICAgaWYgKCFjdXN0b21WYWx1ZSB8fCBjdXN0b21WYWx1ZSA9PT0gbGF5ZXIub3JpZ2luYWxWYWx1ZSkgcmV0dXJuO1xuXG4gICAgICAgIC8vIEZpbmQgdGhlIGNhbnZhcyBvYmplY3QgYnkgSURcbiAgICAgICAgY29uc3QgY2FudmFzT2JqZWN0ID0gb2JqZWN0cy5maW5kKChvYmo6IGFueSkgPT4gb2JqLmlkID09PSBsYXllci5pZCk7XG4gICAgICAgIGlmICghY2FudmFzT2JqZWN0KSByZXR1cm47XG5cbiAgICAgICAgaGFzQ2hhbmdlcyA9IHRydWU7XG5cbiAgICAgICAgaWYgKGxheWVyLnR5cGUgPT09ICd0ZXh0JyAmJiBjYW52YXNPYmplY3QudHlwZSA9PT0gJ3RleHRib3gnKSB7XG4gICAgICAgICAgLy8gQXBwbHkgdGV4dCBjdXN0b21pemF0aW9uXG4gICAgICAgICAgKGNhbnZhc09iamVjdCBhcyBmYWJyaWMuVGV4dGJveCkuc2V0KCd0ZXh0JywgY3VzdG9tVmFsdWUpO1xuICAgICAgICB9IGVsc2UgaWYgKGxheWVyLnR5cGUgPT09ICdpbWFnZScgJiYgY3VzdG9tVmFsdWUuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgICAgIC8vIEFwcGx5IGltYWdlIGN1c3RvbWl6YXRpb25cbiAgICAgICAgICBmYWJyaWMuSW1hZ2UuZnJvbVVSTChjdXN0b21WYWx1ZSwgKGltZykgPT4ge1xuICAgICAgICAgICAgaWYgKCFmYWJyaWNDYW52YXNSZWYuY3VycmVudCkgcmV0dXJuO1xuXG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAvLyBTY2FsZSBpbWFnZSB0byBmaXQgdGhlIG9iamVjdCBib3VuZHNcbiAgICAgICAgICAgICAgY29uc3Qgc2NhbGVYID0gY2FudmFzT2JqZWN0LndpZHRoISAvIGltZy53aWR0aCE7XG4gICAgICAgICAgICAgIGNvbnN0IHNjYWxlWSA9IGNhbnZhc09iamVjdC5oZWlnaHQhIC8gaW1nLmhlaWdodCE7XG4gICAgICAgICAgICAgIGNvbnN0IHNjYWxlID0gTWF0aC5taW4oc2NhbGVYLCBzY2FsZVkpO1xuXG4gICAgICAgICAgICAgIGltZy5zZXQoe1xuICAgICAgICAgICAgICAgIGxlZnQ6IGNhbnZhc09iamVjdC5sZWZ0LFxuICAgICAgICAgICAgICAgIHRvcDogY2FudmFzT2JqZWN0LnRvcCxcbiAgICAgICAgICAgICAgICBzY2FsZVg6IHNjYWxlLFxuICAgICAgICAgICAgICAgIHNjYWxlWTogc2NhbGUsXG4gICAgICAgICAgICAgICAgb3JpZ2luWDogY2FudmFzT2JqZWN0Lm9yaWdpblgsXG4gICAgICAgICAgICAgICAgb3JpZ2luWTogY2FudmFzT2JqZWN0Lm9yaWdpblksXG4gICAgICAgICAgICAgICAgaWQ6IGxheWVyLmlkLCAvLyBQcmVzZXJ2ZSB0aGUgSURcbiAgICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgICAgLy8gUmVwbGFjZSB0aGUgb2JqZWN0XG4gICAgICAgICAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50LnJlbW92ZShjYW52YXNPYmplY3QpO1xuICAgICAgICAgICAgICBmYWJyaWNDYW52YXNSZWYuY3VycmVudC5hZGQoaW1nKTtcbiAgICAgICAgICAgICAgZmFicmljQ2FudmFzUmVmLmN1cnJlbnQucmVuZGVyQWxsKCk7XG4gICAgICAgICAgICAgIGdlbmVyYXRlUHJldmlld0Zyb21DYW52YXMoKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFwcGx5aW5nIGltYWdlIGN1c3RvbWl6YXRpb246JywgZXJyb3IpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybjsgLy8gU2tpcCB0aGUgcmVuZGVyQWxsIGJlbG93IHNpbmNlIGl0J3MgaGFuZGxlZCBpbiB0aGUgY2FsbGJhY2tcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIGlmIChoYXNDaGFuZ2VzKSB7XG4gICAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcbiAgICAgICAgZ2VuZXJhdGVQcmV2aWV3RnJvbUNhbnZhcygpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhcHBseWluZyBjdXN0b21pemF0aW9uczonLCBlcnJvcik7XG4gICAgfVxuICB9LCBbY3VzdG9taXphdGlvbnMsIHRlbXBsYXRlLCBnZW5lcmF0ZVByZXZpZXdGcm9tQ2FudmFzXSk7XG5cbiAgY29uc3QgZ2VuZXJhdGVQcmV2aWV3ID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzR2VuZXJhdGluZ1ByZXZpZXcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdlbmVyYXRlIHByZXZpZXcgZnJvbSBjdXJyZW50IGNhbnZhcyBzdGF0ZVxuICAgICAgZ2VuZXJhdGVQcmV2aWV3RnJvbUNhbnZhcygpO1xuICAgICAgLy8gU21hbGwgZGVsYXkgdG8gc2hvdyBsb2FkaW5nIHN0YXRlXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwKSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2VuZXJhdGUgcHJldmlldzonLCBlcnIpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0dlbmVyYXRpbmdQcmV2aWV3KGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZG93bmxvYWRDdXN0b21pemVkID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzRG93bmxvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFRoaXMgd291bGQgY2FsbCBhbiBBUEkgdG8gZ2VuZXJhdGUgYW5kIGRvd25sb2FkIHRoZSBjdXN0b21pemVkIGRlc2lnblxuICAgICAgLy8gRm9yIG5vdywgd2UnbGwgc2ltdWxhdGUgaXRcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAzMDAwKSk7XG4gICAgICBcbiAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgeW91J2QgZ2V0IGEgZG93bmxvYWQgVVJMIGZyb20gdGhlIEFQSVxuICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgICAgIGxpbmsuaHJlZiA9IHRlbXBsYXRlPy50aHVtYm5haWxVcmwgfHwgJyc7XG4gICAgICBsaW5rLmRvd25sb2FkID0gYGN1c3RvbWl6ZWQtJHt0ZW1wbGF0ZT8ubmFtZSB8fCAnZGVzaWduJ30ucG5nYDtcbiAgICAgIGxpbmsuY2xpY2soKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBkb3dubG9hZDonLCBlcnIpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0Rvd25sb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTggdy04IGFuaW1hdGUtc3BpbiB0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmIChlcnJvciB8fCAhdGVtcGxhdGUpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICAgIHtlcnJvciB8fCBcIlRlbXBsYXRlIG5vdCBmb3VuZFwifVxuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goXCIvcHVibGljXCIpfSB2YXJpYW50PVwib3V0bGluZVwiPlxuICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIEJhY2sgdG8gR2FsbGVyeVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goYC9wdWJsaWMvJHtwYXJhbXMucHJvamVjdElkfWApfSBcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIiBcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIEJhY2tcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02IHctcHggYmctZ3JheS0zMDBcIiAvPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgQ3VzdG9taXplOiB7dGVtcGxhdGUubmFtZX1cbiAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgTWFrZSB0aGlzIHRlbXBsYXRlIHlvdXIgb3duXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dlbmVyYXRlUHJldmlld31cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNHZW5lcmF0aW5nUHJldmlld31cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNHZW5lcmF0aW5nUHJldmlldyA/IChcbiAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtNCB3LTQgYW5pbWF0ZS1zcGluIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICkgOiBudWxsfVxuICAgICAgICAgICAgICAgIFByZXZpZXdcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtkb3dubG9hZEN1c3RvbWl6ZWR9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRG93bmxvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHVycGxlLTYwMCBob3ZlcjpiZy1wdXJwbGUtNzAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc0Rvd25sb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC00IHctNCBhbmltYXRlLXNwaW4gbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgRG93bmxvYWRcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICB7LyogQ3VzdG9taXphdGlvbiBQYW5lbCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTFcIj5cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj5DdXN0b21pemUgRWxlbWVudHM8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAge3RlbXBsYXRlLmVkaXRhYmxlTGF5ZXJzLm1hcCgobGF5ZXIpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtsYXllci5pZH0gY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9e2xheWVyLnR5cGUgPT09ICd0ZXh0JyA/ICdkZWZhdWx0JyA6ICdzZWNvbmRhcnknfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtsYXllci50eXBlID09PSAndGV4dCcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBlIGNsYXNzTmFtZT1cImgtMyB3LTMgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW1hZ2VJY29uIGNsYXNzTmFtZT1cImgtMyB3LTMgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAge2xheWVyLnR5cGV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+e2xheWVyLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7bGF5ZXIudHlwZSA9PT0gJ3RleHQnID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtsYXllci5wbGFjZWhvbGRlciB8fCAnRW50ZXIgdGV4dCd9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjdXN0b21pemF0aW9uc1tsYXllci5pZF0gfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlVGV4dENoYW5nZShsYXllci5pZCwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17bGF5ZXIucGxhY2Vob2xkZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1heExlbmd0aD17bGF5ZXIuY29uc3RyYWludHM/Lm1heExlbmd0aH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAge2xheWVyLmNvbnN0cmFpbnRzPy5tYXhMZW5ndGggJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjdXN0b21pemF0aW9uc1tsYXllci5pZF0/Lmxlbmd0aCB8fCAwfS97bGF5ZXIuY29uc3RyYWludHMubWF4TGVuZ3RofSBjaGFyYWN0ZXJzXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgVXBsb2FkIHlvdXIgaW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEgc3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY2NlcHQ9e2xheWVyLmNvbnN0cmFpbnRzPy5hbGxvd2VkRm9ybWF0cz8ubWFwKGYgPT4gYC4ke2Z9YCkuam9pbignLCcpIHx8ICdpbWFnZS8qJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGUgPSBlLnRhcmdldC5maWxlcz8uWzBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSW1hZ2VVcGxvYWQobGF5ZXIuaWQsIGZpbGUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdy1mdWxsIHRleHQtc20gdGV4dC1ncmF5LTUwMCBmaWxlOm1yLTQgZmlsZTpweS0yIGZpbGU6cHgtNCBmaWxlOnJvdW5kZWQtZnVsbCBmaWxlOmJvcmRlci0wIGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtc2VtaWJvbGQgZmlsZTpiZy1wdXJwbGUtNTAgZmlsZTp0ZXh0LXB1cnBsZS03MDAgaG92ZXI6ZmlsZTpiZy1wdXJwbGUtMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogU21hbGwgcHJldmlldyBvZiB1cGxvYWRlZCBpbWFnZSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2N1c3RvbWl6YXRpb25zW2xheWVyLmlkXSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtjdXN0b21pemF0aW9uc1tsYXllci5pZF19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD1cIlVwbG9hZGVkIHByZXZpZXdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgb2JqZWN0LWNvdmVyIHJvdW5kZWQgYm9yZGVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5JbWFnZSB1cGxvYWRlZDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEN1c3RvbWl6YXRpb25zKHByZXYgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkID0geyAuLi5wcmV2IH07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlbGV0ZSB1cGRhdGVkW2xheWVyLmlkXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHVwZGF0ZWQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1yZWQtNTAwIGhvdmVyOnRleHQtcmVkLTcwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZW1vdmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bGF5ZXIuY29uc3RyYWludHM/Lm1heEZpbGVTaXplICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1heCBzaXplOiB7TWF0aC5yb3VuZChsYXllci5jb25zdHJhaW50cy5tYXhGaWxlU2l6ZSAvIDEwMjQgLyAxMDI0KX1NQlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUHJldmlldyBQYW5lbCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj5QcmV2aWV3PC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS0xMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgb3ZlcmZsb3ctaGlkZGVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBhc3BlY3RSYXRpbzogYCR7dGVtcGxhdGUud2lkdGh9LyR7dGVtcGxhdGUuaGVpZ2h0fWAsXG4gICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6IFwiMTAwJVwiLFxuICAgICAgICAgICAgICAgICAgICAgIG1heEhlaWdodDogXCI1MDBweFwiLFxuICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiBcImF1dG9cIixcbiAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IFwiYXV0b1wiXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtwcmV2aWV3VXJsIHx8IHRlbXBsYXRlLnRodW1ibmFpbFVybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICBzcmM9e3ByZXZpZXdVcmwgfHwgdGVtcGxhdGUudGh1bWJuYWlsVXJsIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiVGVtcGxhdGUgcHJldmlld1wiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy1mdWxsIG1heC1oLWZ1bGwgb2JqZWN0LWNvbnRhaW5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IFwiYXV0b1wiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IFwiYXV0b1wiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDogXCIxMDAlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1heEhlaWdodDogXCIxMDAlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWdyYXktNTAgdy1mdWxsIGgtZnVsbCBtaW4taC1bMjAwcHhdXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SW1hZ2VJY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogSGlkZGVuIGNhbnZhcyBmb3IgRmFicmljLmpzIHJlbmRlcmluZyAqL31cbiAgICAgIDxjYW52YXNcbiAgICAgICAgcmVmPXtjYW52YXNSZWZ9XG4gICAgICAgIHN0eWxlPXt7IGRpc3BsYXk6ICdub25lJyB9fVxuICAgICAgICB3aWR0aD17dGVtcGxhdGU/LndpZHRoIHx8IDgwMH1cbiAgICAgICAgaGVpZ2h0PXt0ZW1wbGF0ZT8uaGVpZ2h0IHx8IDYwMH1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VQYXJhbXMiLCJ1c2VSb3V0ZXIiLCJMb2FkZXIyIiwiQXJyb3dMZWZ0IiwiRG93bmxvYWQiLCJUeXBlIiwiSW1hZ2UiLCJJbWFnZUljb24iLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJhZGdlIiwiQ3VzdG9taXplVGVtcGxhdGVQYWdlIiwicGFyYW1zIiwicm91dGVyIiwidGVtcGxhdGUiLCJzZXRUZW1wbGF0ZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImN1c3RvbWl6YXRpb25zIiwic2V0Q3VzdG9taXphdGlvbnMiLCJwcmV2aWV3VXJsIiwic2V0UHJldmlld1VybCIsImlzR2VuZXJhdGluZ1ByZXZpZXciLCJzZXRJc0dlbmVyYXRpbmdQcmV2aWV3IiwiaXNEb3dubG9hZGluZyIsInNldElzRG93bmxvYWRpbmciLCJmZXRjaFRlbXBsYXRlIiwicmVzcG9uc2UiLCJmZXRjaCIsInByb2plY3RJZCIsIm9rIiwic3RhdHVzIiwiZGF0YSIsImpzb24iLCJ0ZW1wbGF0ZURhdGEiLCJpc0N1c3RvbWl6YWJsZSIsImVkaXRhYmxlTGF5ZXJzIiwiSlNPTiIsInBhcnNlIiwiY29uc29sZSIsImluaXRpYWxDdXN0b21pemF0aW9ucyIsImZvckVhY2giLCJsYXllciIsImlkIiwib3JpZ2luYWxWYWx1ZSIsImVyciIsImhhbmRsZVRleHRDaGFuZ2UiLCJsYXllcklkIiwidmFsdWUiLCJwcmV2IiwiaGFuZGxlSW1hZ2VVcGxvYWQiLCJmaWxlIiwiaW1hZ2VVcmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJ0aHVtYm5haWxVcmwiLCJzZXRDYW52YXNJbml0aWFsaXplZCIsImxlbmd0aCIsImZhYnJpY0NhbnZhc1JlZiIsImN1cnJlbnQiLCJkaXNwb3NlIiwid2FybiIsImluaXRDYW52YXMiLCJjYW52YXNSZWYiLCJjYW52YXMiLCJmYWJyaWMiLCJDYW52YXMiLCJ3aWR0aCIsImhlaWdodCIsInNlbGVjdGlvbiIsInByZXNlcnZlT2JqZWN0U3RhY2tpbmciLCJiYWNrZ3JvdW5kQ29sb3IiLCJ0ZW1wbGF0ZUpzb24iLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsImxvYWRGcm9tSlNPTiIsIm9iamVjdHMiLCJnZXRPYmplY3RzIiwid29ya3NwYWNlIiwiZmluZCIsIm9iaiIsIm5hbWUiLCJyZW1vdmUiLCJzZWxlY3RhYmxlIiwicmVuZGVyQWxsIiwianNvbkVycm9yIiwidGltZW91dElkIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImdlbmVyYXRlUHJldmlld0Zyb21DYW52YXMiLCJ1c2VDYWxsYmFjayIsIm1heFByZXZpZXdTaXplIiwidGVtcGxhdGVNYXhEaW1lbnNpb24iLCJNYXRoIiwibWF4IiwibXVsdGlwbGllciIsIm1pbiIsImRhdGFVUkwiLCJ0b0RhdGFVUkwiLCJmb3JtYXQiLCJxdWFsaXR5IiwiaGFzQ3VzdG9taXphdGlvbnMiLCJPYmplY3QiLCJ2YWx1ZXMiLCJzb21lIiwidHJpbSIsImhhc0NoYW5nZXMiLCJjdXN0b21WYWx1ZSIsImNhbnZhc09iamVjdCIsInR5cGUiLCJzZXQiLCJzdGFydHNXaXRoIiwiZnJvbVVSTCIsImltZyIsInNjYWxlWCIsInNjYWxlWSIsInNjYWxlIiwibGVmdCIsInRvcCIsIm9yaWdpblgiLCJvcmlnaW5ZIiwiYWRkIiwiZ2VuZXJhdGVQcmV2aWV3IiwiZG93bmxvYWRDdXN0b21pemVkIiwibGluayIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsImhyZWYiLCJkb3dubG9hZCIsImNsaWNrIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJvbkNsaWNrIiwicHVzaCIsInZhcmlhbnQiLCJzaXplIiwicCIsImRpc2FibGVkIiwibWFwIiwic3BhbiIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwibWF4TGVuZ3RoIiwiY29uc3RyYWludHMiLCJpbnB1dCIsImFjY2VwdCIsImFsbG93ZWRGb3JtYXRzIiwiZiIsImpvaW4iLCJmaWxlcyIsInNyYyIsImFsdCIsImJ1dHRvbiIsInVwZGF0ZWQiLCJtYXhGaWxlU2l6ZSIsInJvdW5kIiwic3R5bGUiLCJhc3BlY3RSYXRpbyIsIm1heFdpZHRoIiwibWF4SGVpZ2h0IiwicmVmIiwiZGlzcGxheSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ })

});