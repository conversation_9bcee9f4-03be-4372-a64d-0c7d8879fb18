"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/editor.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Editor: function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-thumbnail-generator */ \"(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/components/navbar */ \"(app-pages-browser)/./src/features/editor/components/navbar.tsx\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/components/sidebar */ \"(app-pages-browser)/./src/features/editor/components/sidebar.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* harmony import */ var _features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/editor/components/shape-sidebar */ \"(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/editor/components/fill-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/fill-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/features/editor/components/stroke-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/editor/components/stroke-width-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-width-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/features/editor/components/opacity-sidebar */ \"(app-pages-browser)/./src/features/editor/components/opacity-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/features/editor/components/text-sidebar */ \"(app-pages-browser)/./src/features/editor/components/text-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/editor/components/font-sidebar */ \"(app-pages-browser)/./src/features/editor/components/font-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/editor/components/image-sidebar */ \"(app-pages-browser)/./src/features/editor/components/image-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/editor/components/filter-sidebar */ \"(app-pages-browser)/./src/features/editor/components/filter-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/editor/components/draw-sidebar */ \"(app-pages-browser)/./src/features/editor/components/draw-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/editor/components/ai-sidebar */ \"(app-pages-browser)/./src/features/editor/components/ai-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/editor/components/template-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/features/editor/components/remove-bg-sidebar */ \"(app-pages-browser)/./src/features/editor/components/remove-bg-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/editor/components/settings-sidebar */ \"(app-pages-browser)/./src/features/editor/components/settings-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/editor/components/template-config-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Editor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Editor = (param)=>{\n    let { initialData } = param;\n    _s();\n    const { mutate } = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject)(initialData.id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()((values)=>{\n        mutate(values);\n    }, 3000 // Increased from 500ms to 3 seconds\n    ), [\n        mutate\n    ]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"select\");\n    const onClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        if (_features_editor_types__WEBPACK_IMPORTED_MODULE_6__.selectionDependentTools.includes(activeTool)) {\n            setActiveTool(\"select\");\n        }\n    }, [\n        activeTool\n    ]);\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor)({\n        defaultState: initialData.json,\n        defaultWidth: initialData.width,\n        defaultHeight: initialData.height,\n        clearSelectionCallback: onClearSelection,\n        saveCallback: debouncedSave\n    });\n    const handleManualSave = ()=>{\n        if (editor === null || editor === void 0 ? void 0 : editor.canvas) {\n            const workspace = editor.canvas.getObjects().find((object)=>object.name === \"clip\");\n            const height = (workspace === null || workspace === void 0 ? void 0 : workspace.height) || initialData.height;\n            const width = (workspace === null || workspace === void 0 ? void 0 : workspace.width) || initialData.width;\n            const json = JSON.stringify(editor.canvas.toJSON());\n            mutate({\n                json,\n                height,\n                width\n            });\n        }\n    };\n    // Generate thumbnails automatically when the canvas changes\n    const { debouncedGenerateThumbnail } = (0,_features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator)({\n        editor,\n        projectId: initialData.id\n    });\n    // Trigger thumbnail generation when editor changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (editor) {\n            debouncedGenerateThumbnail();\n        }\n    }, [\n        editor,\n        debouncedGenerateThumbnail\n    ]);\n    const onChangeActiveTool = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((tool)=>{\n        if (tool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.enableDrawingMode();\n        }\n        if (activeTool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.disableDrawingMode();\n        }\n        if (tool === activeTool) {\n            return setActiveTool(\"select\");\n        }\n        setActiveTool(tool);\n    }, [\n        activeTool,\n        editor\n    ]);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const canvas = new fabric__WEBPACK_IMPORTED_MODULE_1__.fabric.Canvas(canvasRef.current, {\n            controlsAboveOverlay: true,\n            preserveObjectStacking: true\n        });\n        init({\n            initialCanvas: canvas,\n            initialContainer: containerRef.current\n        });\n        return ()=>{\n            canvas.dispose();\n        };\n    }, [\n        init\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__.Navbar, {\n                id: initialData.id,\n                editor: editor,\n                activeTool: activeTool,\n                onChangeActiveTool: onChangeActiveTool,\n                onSave: handleManualSave,\n                initialData: {\n                    name: initialData.name,\n                    isCustomizable: initialData.isCustomizable\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute h-[calc(100%-68px)] w-full top-[68px] flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__.ShapeSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__.FillColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__.StrokeColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__.StrokeWidthSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__.OpacitySidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__.TextSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__.FontSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__.ImageSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__.TemplateSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__.FilterSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__.AiSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__.RemoveBgSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__.DrawSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__.SettingsSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__.TemplateConfigSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool,\n                        projectId: initialData.id,\n                        initialData: {\n                            isCustomizable: initialData.isCustomizable,\n                            editableLayers: initialData.editableLayers\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-muted flex-1 overflow-auto relative flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__.Toolbar, {\n                                editor: editor,\n                                activeTool: activeTool,\n                                onChangeActiveTool: onChangeActiveTool\n                            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                                ref: containerRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                    ref: canvasRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__.Footer, {\n                                editor: editor\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Editor, \"74z/qZAt4Wo1k+syyS2i9LDaVik=\", false, function() {\n    return [\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject,\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor,\n        _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator\n    ];\n});\n_c = Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx\n"));

/***/ })

});