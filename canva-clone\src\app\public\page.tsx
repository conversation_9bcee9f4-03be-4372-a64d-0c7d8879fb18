"use client";

import { useState } from "react";
import { useInfiniteQuery } from "@tanstack/react-query";
import { Loader2, User, Calendar, Eye, Palette } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Image from "next/image";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface PublicProject {
  id: string;
  name: string;
  width: number;
  height: number;
  thumbnailUrl: string | null;
  isCustomizable: boolean;
  editableLayers: string | null;
  createdAt: string;
  updatedAt: string;
  user: {
    name: string | null;
    image: string | null;
  } | null;
}

interface PublicProjectsResponse {
  data: PublicProject[];
  nextPage: number | null;
}

const fetchPublicProjects = async ({ pageParam = 1 }): Promise<PublicProjectsResponse> => {
  const response = await fetch(`/api/projects/public?page=${pageParam}&limit=12`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch public projects');
  }
  
  return response.json();
};

export default function PublicProjectsPage() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useInfiniteQuery({
    queryKey: ['public-projects'],
    queryFn: fetchPublicProjects,
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
  });

  const allProjects = data?.pages.flatMap(page => page.data) ?? [];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Error loading projects</h1>
            <p className="text-gray-600">Please try again later.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Public Gallery</h1>
              <p className="text-gray-600 mt-2">
                Discover amazing designs created by our community
                {allProjects.length > 0 && (
                  <span className="ml-2 text-purple-600 font-medium">
                    ({allProjects.length} design{allProjects.length !== 1 ? 's' : ''})
                  </span>
                )}
              </p>
            </div>
            <Link href="/">
              <Button variant="outline">
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        {allProjects.length === 0 ? (
          <div className="text-center py-12">
            <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No public projects yet</h2>
            <p className="text-gray-600">Be the first to share your design with the community!</p>
          </div>
        ) : (
          <>
            {/* Projects Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {allProjects.map((project) => (
                <Link key={project.id} href={`/public/${project.id}`}>
                  <Card className="group hover:shadow-lg transition-all duration-200 cursor-pointer">
                    <CardContent className="p-0">
                    {/* Thumbnail */}
                    <div 
                      className="relative w-full bg-white rounded-t-lg overflow-hidden"
                      style={{ aspectRatio: `${project.width}/${project.height}` }}
                    >
                      {project.thumbnailUrl ? (
                        <Image
                          src={project.thumbnailUrl}
                          alt={project.name}
                          fill
                          className="object-contain bg-white group-hover:scale-105 transition-transform duration-200"
                          sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                          quality={100}
                          unoptimized={true}
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center bg-gray-100">
                          <Eye className="h-8 w-8 text-gray-400" />
                        </div>
                      )}

                      {/* Customizable indicator */}
                      {project.isCustomizable && project.editableLayers && (
                        <div className="absolute top-2 left-2 bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                          <Palette className="h-3 w-3" />
                          <span>Customizable</span>
                        </div>
                      )}

                      {/* Customizable indicator */}
                      {project.isCustomizable && project.editableLayers && (
                        <div className="absolute top-2 left-2 bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                          <Palette className="h-3 w-3" />
                          <span>Customizable</span>
                        </div>
                      )}
                    </div>

                    {/* Project Info */}
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 truncate mb-2">
                        {project.name}
                      </h3>
                      
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                        <Badge variant="secondary" className="text-xs">
                          {project.width} × {project.height}
                        </Badge>
                        <span className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDistanceToNow(new Date(project.updatedAt), { addSuffix: true })}
                        </span>
                      </div>

                      {/* Creator Info */}
                      {project.user && (
                        <div className="flex items-center space-x-2">
                          {project.user.image ? (
                            <Image
                              src={project.user.image}
                              alt={project.user.name || "User"}
                              width={20}
                              height={20}
                              className="rounded-full"
                            />
                          ) : (
                            <div className="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center">
                              <User className="h-3 w-3 text-gray-600" />
                            </div>
                          )}
                          <span className="text-sm text-gray-600 truncate">
                            {project.user.name || "Anonymous"}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
                </Link>
              ))}
            </div>

            {/* Load More Button */}
            {hasNextPage && (
              <div className="text-center mt-8">
                <Button
                  onClick={() => fetchNextPage()}
                  disabled={isFetchingNextPage}
                  variant="outline"
                  size="lg"
                >
                  {isFetchingNextPage ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
