import { useCallback, useRef } from "react";
import debounce from "lodash.debounce";

import { Editor } from "@/features/editor/types";
import { client } from "@/lib/hono";

interface UseThumbnailGeneratorProps {
  editor: Editor | undefined;
  projectId: string;
  onThumbnailGenerated?: (thumbnailUrl: string) => void;
}

export const useThumbnailGenerator = ({
  editor,
  projectId,
  onThumbnailGenerated,
}: UseThumbnailGeneratorProps) => {
  const lastThumbnailRef = useRef<string | null>(null);

  const generateThumbnail = useCallback(async () => {
    if (!editor) {
      console.warn("No editor available for thumbnail generation");
      return;
    }

    if (!editor.canvas) {
      console.warn("No canvas available for thumbnail generation");
      return;
    }

    try {
      console.log("Generating thumbnail for project:", projectId);

      // Generate thumbnail data URL with smaller size for testing
      const thumbnailDataUrl = editor.generateThumbnail({
        width: 300,
        height: 200,
        quality: 0.8,
      });

      if (!thumbnailDataUrl) {
        console.error("Failed to generate thumbnail data URL");
        return;
      }

      console.log("Generated thumbnail data URL length:", thumbnailDataUrl.length);
      console.log("Thumbnail data URL preview:", thumbnailDataUrl.substring(0, 100) + "...");

      // Skip if thumbnail hasn't changed significantly
      if (lastThumbnailRef.current === thumbnailDataUrl) {
        console.log("Thumbnail unchanged, skipping update");
        return;
      }

      lastThumbnailRef.current = thumbnailDataUrl;
      console.log("Thumbnail generated, updating project...");

      // For now, let's store the thumbnail as a data URL directly in the database
      // This is simpler and doesn't require external file uploads
      // In production, you might want to upload to a CDN

      // Update project with thumbnail data URL
      const response = await client.api.projects[":id"].$patch({
        param: { id: projectId },
        json: {
          thumbnailUrl: thumbnailDataUrl,
        },
      });

      if (response.ok) {
        console.log("Thumbnail updated successfully");
        onThumbnailGenerated?.(thumbnailDataUrl);
      } else {
        console.error("Failed to update thumbnail:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Error generating thumbnail:", error);
    }
  }, [editor, projectId, onThumbnailGenerated]);

  // Debounced version to avoid too frequent thumbnail generation
  const debouncedGenerateThumbnail = useCallback(
    debounce(generateThumbnail, 2000), // Generate thumbnail 2 seconds after last change
    [generateThumbnail]
  );

  const forceRegenerateThumbnail = useCallback(async () => {
    // Reset the last thumbnail to force regeneration
    lastThumbnailRef.current = null;
    await generateThumbnail();
  }, [generateThumbnail]);

  return {
    generateThumbnail,
    debouncedGenerateThumbnail,
    forceRegenerateThumbnail,
  };
};
