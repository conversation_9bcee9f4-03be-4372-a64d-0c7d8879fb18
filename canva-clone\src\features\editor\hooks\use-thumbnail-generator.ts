import { useCallback, useRef } from "react";
import debounce from "lodash.debounce";

import { Editor } from "@/features/editor/types";
import { client } from "@/lib/hono";

interface UseThumbnailGeneratorProps {
  editor: Editor | undefined;
  projectId: string;
  onThumbnailGenerated?: (thumbnailUrl: string) => void;
}

export const useThumbnailGenerator = ({
  editor,
  projectId,
  onThumbnailGenerated,
}: UseThumbnailGeneratorProps) => {
  const lastThumbnailRef = useRef<string | null>(null);

  const generateThumbnail = useCallback(async () => {
    if (!editor) return;

    try {
      // Generate thumbnail data URL with ultra-high quality (Canva-level)
      const thumbnailDataUrl = editor.generateThumbnail({
        width: 600,
        height: 400,
        quality: 1.0,
        format: "image/png",
        maintainAspectRatio: true,
      });

      // Skip if thumbnail hasn't changed significantly
      if (lastThumbnailRef.current === thumbnailDataUrl) {
        return;
      }

      lastThumbnailRef.current = thumbnailDataUrl;

      // For now, let's store the thumbnail as a data URL directly in the database
      // This is simpler and doesn't require external file uploads
      // In production, you might want to upload to a CDN

      // Update project with thumbnail data URL
      const response = await client.api.projects[":id"].$patch({
        param: { id: projectId },
        json: {
          thumbnailUrl: thumbnailDataUrl,
        },
      });

      if (response.ok) {
        onThumbnailGenerated?.(thumbnailDataUrl);
      }
    } catch (error) {
      console.error("Error generating thumbnail:", error);
    }
  }, [editor, projectId, onThumbnailGenerated]);

  // Debounced version to avoid too frequent thumbnail generation
  const debouncedGenerateThumbnail = useCallback(
    debounce(generateThumbnail, 2000), // Generate thumbnail 2 seconds after last change
    [generateThumbnail]
  );

  const forceRegenerateThumbnail = useCallback(async () => {
    // Reset the last thumbnail to force regeneration
    lastThumbnailRef.current = null;
    await generateThumbnail();
  }, [generateThumbnail]);

  return {
    generateThumbnail,
    debouncedGenerateThumbnail,
    forceRegenerateThumbnail,
  };
};
