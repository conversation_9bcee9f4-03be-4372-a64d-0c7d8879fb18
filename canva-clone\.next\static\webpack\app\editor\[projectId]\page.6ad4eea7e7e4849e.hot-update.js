"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/editor.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Editor: function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-thumbnail-generator */ \"(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/components/navbar */ \"(app-pages-browser)/./src/features/editor/components/navbar.tsx\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/components/sidebar */ \"(app-pages-browser)/./src/features/editor/components/sidebar.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* harmony import */ var _features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/editor/components/shape-sidebar */ \"(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/editor/components/fill-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/fill-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/features/editor/components/stroke-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/editor/components/stroke-width-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-width-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/features/editor/components/opacity-sidebar */ \"(app-pages-browser)/./src/features/editor/components/opacity-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/features/editor/components/text-sidebar */ \"(app-pages-browser)/./src/features/editor/components/text-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/editor/components/font-sidebar */ \"(app-pages-browser)/./src/features/editor/components/font-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/editor/components/image-sidebar */ \"(app-pages-browser)/./src/features/editor/components/image-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/editor/components/filter-sidebar */ \"(app-pages-browser)/./src/features/editor/components/filter-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/editor/components/draw-sidebar */ \"(app-pages-browser)/./src/features/editor/components/draw-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/editor/components/ai-sidebar */ \"(app-pages-browser)/./src/features/editor/components/ai-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/editor/components/template-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/features/editor/components/remove-bg-sidebar */ \"(app-pages-browser)/./src/features/editor/components/remove-bg-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/editor/components/settings-sidebar */ \"(app-pages-browser)/./src/features/editor/components/settings-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/editor/components/template-config-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Editor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Editor = (param)=>{\n    let { initialData, initialActiveTool = \"select\" } = param;\n    _s();\n    const { mutate } = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject)(initialData.id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()((values)=>{\n        mutate(values);\n    }, 3000 // Increased from 500ms to 3 seconds\n    ), [\n        mutate\n    ]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(initialActiveTool);\n    const onClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        if (_features_editor_types__WEBPACK_IMPORTED_MODULE_6__.selectionDependentTools.includes(activeTool)) {\n            setActiveTool(\"select\");\n        }\n    }, [\n        activeTool\n    ]);\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor)({\n        defaultState: initialData.json,\n        defaultWidth: initialData.width,\n        defaultHeight: initialData.height,\n        clearSelectionCallback: onClearSelection,\n        saveCallback: debouncedSave\n    });\n    const handleManualSave = ()=>{\n        if (editor === null || editor === void 0 ? void 0 : editor.canvas) {\n            const workspace = editor.canvas.getObjects().find((object)=>object.name === \"clip\");\n            const height = (workspace === null || workspace === void 0 ? void 0 : workspace.height) || initialData.height;\n            const width = (workspace === null || workspace === void 0 ? void 0 : workspace.width) || initialData.width;\n            const json = JSON.stringify(editor.canvas.toJSON());\n            mutate({\n                json,\n                height,\n                width\n            });\n        }\n    };\n    // Generate thumbnails automatically when the canvas changes\n    const { debouncedGenerateThumbnail } = (0,_features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator)({\n        editor,\n        projectId: initialData.id\n    });\n    // Trigger thumbnail generation when editor changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (editor) {\n            debouncedGenerateThumbnail();\n        }\n    }, [\n        editor,\n        debouncedGenerateThumbnail\n    ]);\n    const onChangeActiveTool = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((tool)=>{\n        if (tool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.enableDrawingMode();\n        }\n        if (activeTool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.disableDrawingMode();\n        }\n        if (tool === activeTool) {\n            return setActiveTool(\"select\");\n        }\n        setActiveTool(tool);\n    }, [\n        activeTool,\n        editor\n    ]);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            // Ensure container is available and has dimensions\n            if (!containerRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            // Don't initialize if container has no dimensions\n            if (containerWidth === 0 || containerHeight === 0) {\n                console.warn(\"Container has zero dimensions, delaying canvas initialization\");\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_1__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        // Try to initialize immediately\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            // If initialization failed, retry after a short delay\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (!retryCanvas) {\n                    console.error(\"Failed to initialize canvas after retry\");\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        }\n        return ()=>{\n            canvas.dispose();\n        };\n    }, [\n        init\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__.Navbar, {\n                id: initialData.id,\n                editor: editor,\n                activeTool: activeTool,\n                onChangeActiveTool: onChangeActiveTool,\n                onSave: handleManualSave,\n                initialData: {\n                    name: initialData.name,\n                    isCustomizable: initialData.isCustomizable\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute h-[calc(100%-68px)] w-full top-[68px] flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__.ShapeSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__.FillColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__.StrokeColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__.StrokeWidthSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__.OpacitySidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__.TextSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__.FontSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__.ImageSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__.TemplateSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__.FilterSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__.AiSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__.RemoveBgSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__.DrawSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__.SettingsSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__.TemplateConfigSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool,\n                        projectId: initialData.id,\n                        initialData: {\n                            isCustomizable: initialData.isCustomizable,\n                            editableLayers: initialData.editableLayers\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-muted flex-1 overflow-auto relative flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__.Toolbar, {\n                                editor: editor,\n                                activeTool: activeTool,\n                                onChangeActiveTool: onChangeActiveTool\n                            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                                ref: containerRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                    ref: canvasRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__.Footer, {\n                                editor: editor\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Editor, \"qNoc/rOOcWOOKkG9qC9csQTkUY8=\", false, function() {\n    return [\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject,\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor,\n        _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator\n    ];\n});\n_c = Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx\n"));

/***/ })

});