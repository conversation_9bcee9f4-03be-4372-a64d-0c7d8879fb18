"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated, activeLayerId: externalActiveLayerId, onLayerActivation } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize editor first\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{\n            onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n        },\n        saveCallback: ()=>{\n            // Generate preview when canvas changes\n            generatePreview();\n        }\n    });\n    // Generate preview from canvas (defined after editor)\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            const dataUrl = editor.canvas.toDataURL({\n                format: \"png\",\n                quality: 0.9,\n                multiplier: 0.5\n            });\n            onPreviewGenerated(dataUrl);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Initialize canvas (same as main editor)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                initializeCanvas();\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Handle selection changes to notify parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleSelectionCreated = (e)=>{\n            const target = e.target;\n            if (target) {\n                const layerId = target.id;\n                const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n                if (layer) {\n                    onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(layerId);\n                }\n            }\n        };\n        const handleSelectionCleared = ()=>{\n            onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n        };\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (target && target.type === \"textbox\") {\n                const layerId = target.id;\n                const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n                if (layer && layer.type === \"text\") {\n                    const currentText = target.text || \"\";\n                    onCustomizationChange(layerId, currentText);\n                }\n            }\n        };\n        editor.canvas.on(\"selection:created\", handleSelectionCreated);\n        editor.canvas.on(\"selection:updated\", handleSelectionCreated);\n        editor.canvas.on(\"selection:cleared\", handleSelectionCleared);\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        return ()=>{\n            editor.canvas.off(\"selection:created\", handleSelectionCreated);\n            editor.canvas.off(\"selection:updated\", handleSelectionCreated);\n            editor.canvas.off(\"selection:cleared\", handleSelectionCleared);\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        onLayerActivation,\n        onCustomizationChange\n    ]);\n    // Apply customizations from sidebar to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        console.log(\"Applying customizations:\", customizations);\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            console.log(\"Processing layer \".concat(layer.id, \" (\").concat(layer.type, \"):\"), customValue);\n            const canvasObject = editor.canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) {\n                console.log(\"Canvas object not found for layer \".concat(layer.id));\n                return;\n            }\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                const textbox = canvasObject;\n                console.log('Current text: \"'.concat(textbox.text, '\", New text: \"').concat(customValue, '\"'));\n                if (customValue && textbox.text !== customValue) {\n                    console.log(\"Updating text for \".concat(layer.id, ' to: \"').concat(customValue, '\"'));\n                    textbox.set(\"text\", customValue);\n                    editor.canvas.renderAll();\n                    // Trigger save to update the canvas state\n                    if (editor.canvas.fire) {\n                        editor.canvas.fire(\"text:changed\", {\n                            target: textbox\n                        });\n                    }\n                }\n            } else if (layer.type === \"image\" && customValue) {\n                console.log(\"Replacing image for \".concat(layer.id, \" with: \").concat(customValue));\n                // Handle image replacement\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!editor.canvas) return;\n                    console.log(\"Original image object:\", canvasObject);\n                    console.log(\"New image loaded:\", img);\n                    // Get ALL the original object's properties to preserve exact positioning\n                    const originalProps = {\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        width: canvasObject.width,\n                        height: canvasObject.height,\n                        scaleX: canvasObject.scaleX,\n                        scaleY: canvasObject.scaleY,\n                        angle: canvasObject.angle,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        flipX: canvasObject.flipX,\n                        flipY: canvasObject.flipY,\n                        opacity: canvasObject.opacity,\n                        visible: canvasObject.visible,\n                        shadow: canvasObject.shadow,\n                        stroke: canvasObject.stroke,\n                        strokeWidth: canvasObject.strokeWidth,\n                        fill: canvasObject.fill,\n                        selectable: canvasObject.selectable,\n                        evented: canvasObject.evented,\n                        hasControls: canvasObject.hasControls,\n                        hasBorders: canvasObject.hasBorders,\n                        lockMovementX: canvasObject.lockMovementX,\n                        lockMovementY: canvasObject.lockMovementY,\n                        lockRotation: canvasObject.lockRotation,\n                        lockScalingX: canvasObject.lockScalingX,\n                        lockScalingY: canvasObject.lockScalingY,\n                        lockUniScaling: canvasObject.lockUniScaling\n                    };\n                    console.log(\"Preserving original properties:\", originalProps);\n                    // Calculate scale to fit within original bounds while maintaining aspect ratio\n                    const targetWidth = (originalProps.width || 100) * (originalProps.scaleX || 1);\n                    const targetHeight = (originalProps.height || 100) * (originalProps.scaleY || 1);\n                    const scaleX = targetWidth / img.width;\n                    const scaleY = targetHeight / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    // Apply all original properties to the new image\n                    img.set({\n                        ...originalProps,\n                        scaleX: scale,\n                        scaleY: scale\n                    });\n                    // Set the ID using a custom property\n                    img.id = layer.id;\n                    // Get the exact position in the layer stack\n                    const objectIndex = editor.canvas.getObjects().indexOf(canvasObject);\n                    console.log(\"Replacing image at index \".concat(objectIndex));\n                    // Remove old image and insert new one at exact same position\n                    editor.canvas.remove(canvasObject);\n                    editor.canvas.insertAt(img, objectIndex, false);\n                    // Select the new image to show it's been replaced\n                    editor.canvas.setActiveObject(img);\n                    editor.canvas.renderAll();\n                    console.log(\"Image replaced for \".concat(layer.id, \" at index \").concat(objectIndex), img);\n                }, {\n                    crossOrigin: \"anonymous\"\n                });\n            }\n        });\n    }, [\n        customizations,\n        editor,\n        templateData.editableLayers\n    ]);\n    // Ensure canvas objects have proper IDs when editor loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up canvas object IDs for editable layers:\", editableLayerIds);\n        // Wait a bit for the canvas to be fully loaded\n        setTimeout(()=>{\n            const allObjects = canvas.getObjects();\n            console.log(\"All canvas objects after load:\", allObjects.map((obj)=>({\n                    id: obj.id,\n                    type: obj.type,\n                    name: obj.name,\n                    text: obj.type === \"textbox\" ? obj.text : undefined\n                })));\n            // Try to match objects to editable layers by content or position\n            templateData.editableLayers.forEach((layer)=>{\n                let matchedObject = allObjects.find((obj)=>obj.id === layer.id);\n                if (!matchedObject) {\n                    if (layer.type === \"text\") {\n                        // Try to find by text content if no ID match\n                        matchedObject = allObjects.find((obj)=>obj.type === \"textbox\" && obj.text === layer.originalValue && !editableLayerIds.includes(obj.id));\n                    } else if (layer.type === \"image\") {\n                        // For images, try to find by type and position, or just by type if it's the nth image\n                        const imageObjects = allObjects.filter((obj)=>obj.type === \"image\" && !editableLayerIds.includes(obj.id));\n                        // Try to match by index (first editable image layer matches first unmatched image object)\n                        const imageLayerIndex = templateData.editableLayers.filter((l)=>l.type === \"image\").indexOf(layer);\n                        if (imageObjects[imageLayerIndex]) {\n                            matchedObject = imageObjects[imageLayerIndex];\n                        }\n                    }\n                }\n                if (matchedObject && !matchedObject.id) {\n                    console.log(\"Assigning ID \".concat(layer.id, \" to \").concat(layer.type, \" object:\"), matchedObject);\n                    matchedObject.id = layer.id;\n                }\n            });\n            canvas.renderAll();\n        }, 500);\n    }, [\n        editor,\n        templateData.editableLayers\n    ]);\n    // Function to select object by layer ID (called from parent)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        console.log(\"External active layer changed:\", externalActiveLayerId);\n        if (externalActiveLayerId) {\n            const allObjects = editor.canvas.getObjects();\n            console.log(\"All canvas objects:\", allObjects.map((obj)=>({\n                    id: obj.id,\n                    type: obj.type,\n                    name: obj.name\n                })));\n            const targetObject = allObjects.find((obj)=>obj.id === externalActiveLayerId);\n            console.log(\"Found target object:\", targetObject);\n            if (targetObject) {\n                console.log(\"Selecting object in canvas:\", targetObject.id);\n                editor.canvas.setActiveObject(targetObject);\n                editor.canvas.renderAll();\n            } else {\n                console.log(\"Target object not found for ID:\", externalActiveLayerId);\n            }\n        } else {\n            // Clear selection if no layer is active\n            console.log(\"Clearing canvas selection\");\n            editor.canvas.discardActiveObject();\n            editor.canvas.renderAll();\n        }\n    }, [\n        editor,\n        externalActiveLayerId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_5__.Toolbar, {\n                editor: editor,\n                activeTool: \"select\",\n                onChangeActiveTool: ()=>{}\n            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                ref: containerRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 355,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"xWIKbIYg0XVsBz7xgaDlw9unfJI=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});