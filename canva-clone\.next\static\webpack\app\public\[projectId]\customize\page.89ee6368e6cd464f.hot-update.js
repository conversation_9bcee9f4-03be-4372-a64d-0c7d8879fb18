"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [canvasInitialized, setCanvasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Canvas refs for Fabric.js\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fabricCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers\n                const editableLayers = JSON.parse(templateData.editableLayers);\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleTextChange = (layerId, value)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        // Create object URL for immediate preview\n        const imageUrl = URL.createObjectURL(file);\n        // Update customizations immediately\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: imageUrl\n            }));\n    // In a real implementation, you'd upload to your storage service here\n    // const uploadedUrl = await uploadToStorage(file);\n    // setCustomizations(prev => ({ ...prev, [layerId]: uploadedUrl }));\n    };\n    // Initialize Fabric.js canvas with better error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!template || !canvasRef.current) return;\n        // Clean up existing canvas\n        if (fabricCanvasRef.current) {\n            try {\n                fabricCanvasRef.current.dispose();\n            } catch (error) {\n                console.warn(\"Error disposing canvas:\", error);\n            }\n            fabricCanvasRef.current = null;\n        }\n        const initCanvas = async ()=>{\n            if (!canvasRef.current || !template) return;\n            try {\n                // Fix fabric.js textBaseline issue before creating canvas\n                fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Object.prototype.set({\n                    cornerColor: \"#FFF\",\n                    cornerStyle: \"circle\",\n                    borderColor: \"#3b82f6\",\n                    borderScaleFactor: 1.5,\n                    transparentCorners: false,\n                    borderOpacityWhenMoving: 1,\n                    cornerStrokeColor: \"#3b82f6\"\n                });\n                // Fix textBaseline issue\n                fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Text.prototype.set({\n                    textBaseline: \"middle\"\n                });\n                fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Textbox.prototype.set({\n                    textBaseline: \"middle\"\n                });\n                // Create canvas with proper error handling\n                const canvas = new fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Canvas(canvasRef.current, {\n                    width: template.width,\n                    height: template.height,\n                    selection: false,\n                    preserveObjectStacking: true\n                });\n                fabricCanvasRef.current = canvas;\n                // Load template JSON\n                if (template.json) {\n                    const templateJson = JSON.parse(template.json);\n                    await new Promise((resolve, reject)=>{\n                        canvas.loadFromJSON(templateJson, ()=>{\n                            try {\n                                canvas.renderAll();\n                                setCanvasInitialized(true);\n                                generatePreviewFromCanvas();\n                                resolve();\n                            } catch (error) {\n                                reject(error);\n                            }\n                        }, (error)=>{\n                            reject(error);\n                        });\n                    });\n                } else {\n                    setCanvasInitialized(true);\n                    generatePreviewFromCanvas();\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize canvas:\", error);\n                // Fallback to template thumbnail\n                if (template.thumbnailUrl) {\n                    setPreviewUrl(template.thumbnailUrl);\n                }\n            }\n        };\n        // Delay initialization to ensure DOM is ready\n        const timeoutId = setTimeout(initCanvas, 100);\n        return ()=>{\n            clearTimeout(timeoutId);\n            if (fabricCanvasRef.current) {\n                try {\n                    fabricCanvasRef.current.dispose();\n                } catch (error) {\n                    console.warn(\"Error disposing canvas in cleanup:\", error);\n                }\n                fabricCanvasRef.current = null;\n            }\n        };\n    }, [\n        template\n    ]);\n    // Generate preview from canvas\n    const generatePreviewFromCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!fabricCanvasRef.current) {\n            // Fallback to template thumbnail\n            if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n            return;\n        }\n        try {\n            const canvas = fabricCanvasRef.current;\n            const dataURL = canvas.toDataURL({\n                format: \"png\",\n                quality: 0.8,\n                multiplier: 1\n            });\n            setPreviewUrl(dataURL);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n            // Fallback to template thumbnail\n            if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n        }\n    }, [\n        template\n    ]);\n    // Apply customizations to canvas objects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!fabricCanvasRef.current || !canvasInitialized || !template) return;\n        try {\n            const canvas = fabricCanvasRef.current;\n            const objects = canvas.getObjects();\n            // Apply customizations to editable objects\n            template.editableLayers.forEach((layer)=>{\n                const customValue = customizations[layer.id];\n                if (!customValue) return;\n                // Find the canvas object by ID\n                const canvasObject = objects.find((obj)=>obj.id === layer.id);\n                if (!canvasObject) return;\n                if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                    // Apply text customization\n                    canvasObject.set(\"text\", customValue);\n                } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                    // Apply image customization\n                    fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Image.fromURL(customValue, (img)=>{\n                        if (!fabricCanvasRef.current) return;\n                        try {\n                            // Scale image to fit the object bounds\n                            const scaleX = canvasObject.width / img.width;\n                            const scaleY = canvasObject.height / img.height;\n                            const scale = Math.min(scaleX, scaleY);\n                            img.set({\n                                left: canvasObject.left,\n                                top: canvasObject.top,\n                                scaleX: scale,\n                                scaleY: scale,\n                                originX: canvasObject.originX,\n                                originY: canvasObject.originY,\n                                id: layer.id\n                            });\n                            // Replace the object\n                            fabricCanvasRef.current.remove(canvasObject);\n                            fabricCanvasRef.current.add(img);\n                            fabricCanvasRef.current.renderAll();\n                            generatePreviewFromCanvas();\n                        } catch (error) {\n                            console.error(\"Error applying image customization:\", error);\n                        }\n                    });\n                    return; // Skip the renderAll below since it's handled in the callback\n                }\n            });\n            canvas.renderAll();\n            generatePreviewFromCanvas();\n        } catch (error) {\n            console.error(\"Error applying customizations:\", error);\n        }\n    }, [\n        customizations,\n        canvasInitialized,\n        template,\n        generatePreviewFromCanvas\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Generate preview from current canvas state\n            generatePreviewFromCanvas();\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // In a real implementation, you'd get a download URL from the API\n            const link = document.createElement(\"a\");\n            link.href = (template === null || template === void 0 ? void 0 : template.thumbnailUrl) || \"\";\n            link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n            link.click();\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 323,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 332,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Customize Elements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: customizations[layer.id] || \"\",\n                                                                onChange: (e)=>handleTextChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 463,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 469,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 470,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center bg-gray-100 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-lg overflow-hidden flex items-center justify-center\",\n                                                style: {\n                                                    aspectRatio: \"\".concat(template.width, \"/\").concat(template.height),\n                                                    maxWidth: \"100%\",\n                                                    maxHeight: \"500px\",\n                                                    width: \"auto\",\n                                                    height: \"auto\"\n                                                },\n                                                children: previewUrl || template.thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: previewUrl || template.thumbnailUrl || \"\",\n                                                    alt: \"Template preview\",\n                                                    className: \"max-w-full max-h-full object-contain\",\n                                                    style: {\n                                                        width: \"auto\",\n                                                        height: \"auto\",\n                                                        maxWidth: \"100%\",\n                                                        maxHeight: \"100%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center bg-gray-50 w-full h-full min-h-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 348,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"w0bUFtRIeJ9lE84wprAeFbjL2rs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ })

});