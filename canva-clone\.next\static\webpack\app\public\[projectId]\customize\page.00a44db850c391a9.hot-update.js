"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeLayerId, setActiveLayerId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize editor with no-save callback since this is read-only customization\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{},\n        saveCallback: ()=>{}\n    });\n    // Initialize canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (retryCanvas) {\n                    setIsInitialized(true);\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        } else {\n            setIsInitialized(true);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Lock non-editable objects and setup customization mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up customization mode:\", {\n            editableLayerIds,\n            totalObjects: canvas.getObjects().length,\n            editableLayers: templateData.editableLayers\n        });\n        // Lock all objects except editable ones\n        canvas.getObjects().forEach((obj)=>{\n            console.log(\"Processing object:\", {\n                id: obj.id,\n                type: obj.type,\n                name: obj.name,\n                isEditable: editableLayerIds.includes(obj.id)\n            });\n            if (obj.name === \"clip\") {\n                // Keep workspace as is but make it non-selectable\n                obj.set({\n                    selectable: false,\n                    evented: false\n                });\n                return;\n            }\n            if (editableLayerIds.includes(obj.id)) {\n                console.log(\"Making object editable:\", obj.id, obj.type);\n                // Make editable objects selectable and editable\n                obj.set({\n                    selectable: true,\n                    hasControls: false,\n                    hasBorders: true,\n                    borderColor: \"#3b82f6\",\n                    borderScaleFactor: 2,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true,\n                    editable: obj.type === \"textbox\" ? true : false,\n                    hoverCursor: \"pointer\",\n                    moveCursor: \"pointer\"\n                });\n                // Add visual indicator for editable elements\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: \"rgba(59, 130, 246, 0.1)\"\n                    });\n                }\n            } else {\n                // Lock non-editable objects completely\n                obj.set({\n                    selectable: false,\n                    evented: false,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n            }\n        });\n        canvas.renderAll();\n    }, [\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue || customValue === layer.originalValue) return;\n            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                canvasObject.set(\"text\", customValue);\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        id: layer.id,\n                        selectable: true,\n                        hasControls: false,\n                        lockMovementX: true,\n                        lockMovementY: true,\n                        lockRotation: true,\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true\n                    });\n                    canvas.remove(canvasObject);\n                    canvas.add(img);\n                    canvas.renderAll();\n                    generatePreview();\n                });\n                return;\n            }\n        });\n        canvas.renderAll();\n        generatePreview();\n    }, [\n        customizations,\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Generate preview from canvas\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            // Create a temporary canvas with just the workspace content\n            const tempCanvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(null, {\n                width: workspace.width,\n                height: workspace.height\n            });\n            // Clone all objects except the workspace itself\n            const objectsToClone = editor.canvas.getObjects().filter((obj)=>obj.name !== \"clip\");\n            objectsToClone.forEach((obj)=>{\n                obj.clone((cloned)=>{\n                    // Adjust position relative to workspace\n                    cloned.set({\n                        left: (cloned.left || 0) - (workspace.left || 0),\n                        top: (cloned.top || 0) - (workspace.top || 0)\n                    });\n                    tempCanvas.add(cloned);\n                });\n            });\n            // Generate preview after a short delay to ensure all objects are added\n            setTimeout(()=>{\n                const dataUrl = tempCanvas.toDataURL({\n                    format: \"png\",\n                    quality: 0.9,\n                    multiplier: 1\n                });\n                onPreviewGenerated(dataUrl);\n                tempCanvas.dispose();\n            }, 100);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Handle text editing\n    const handleTextEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((layerId, value)=>{\n        onCustomizationChange(layerId, value);\n    }, [\n        onCustomizationChange\n    ]);\n    // Handle text editing events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (!layer || layer.type !== \"text\") return;\n            const currentText = target.text || \"\";\n            handleTextEdit(layerId, currentText);\n            generatePreview();\n        };\n        const handleDoubleClick = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer && layer.type === \"text\") {\n                // Enter editing mode\n                target.enterEditing();\n                target.selectAll();\n            }\n        };\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        editor.canvas.on(\"mouse:dblclick\", handleDoubleClick);\n        return ()=>{\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n            editor.canvas.off(\"mouse:dblclick\", handleDoubleClick);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        handleTextEdit,\n        generatePreview\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    ref: containerRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"EvjyI9KgWPO3k+XNXPU57W1ahZw=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});