"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeLayerId, setActiveLayerId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize editor with no-save callback since this is read-only customization\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{},\n        saveCallback: ()=>{}\n    });\n    // Initialize canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (retryCanvas) {\n                    setIsInitialized(true);\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        } else {\n            setIsInitialized(true);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Lock non-editable objects and setup customization mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up customization mode:\", {\n            editableLayerIds,\n            totalObjects: canvas.getObjects().length,\n            editableLayers: templateData.editableLayers\n        });\n        // Lock all objects except editable ones\n        canvas.getObjects().forEach((obj)=>{\n            console.log(\"Processing object:\", {\n                id: obj.id,\n                type: obj.type,\n                name: obj.name,\n                isEditable: editableLayerIds.includes(obj.id)\n            });\n            if (obj.name === \"clip\") {\n                // Keep workspace as is but make it non-selectable\n                obj.set({\n                    selectable: false,\n                    evented: false\n                });\n                return;\n            }\n            if (editableLayerIds.includes(obj.id)) {\n                console.log(\"Making object editable:\", obj.id, obj.type);\n                // Make editable objects selectable and editable\n                obj.set({\n                    selectable: true,\n                    hasControls: obj.type === \"textbox\" ? false : true,\n                    hasBorders: true,\n                    lockMovementX: obj.type === \"textbox\" ? true : false,\n                    lockMovementY: obj.type === \"textbox\" ? true : false,\n                    lockRotation: true,\n                    lockScalingX: obj.type === \"textbox\" ? true : false,\n                    lockScalingY: obj.type === \"textbox\" ? true : false,\n                    lockUniScaling: obj.type === \"textbox\" ? true : false,\n                    editable: obj.type === \"textbox\" ? true : false\n                });\n            } else {\n                // Lock non-editable objects completely\n                obj.set({\n                    selectable: false,\n                    evented: false,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n            }\n        });\n        canvas.renderAll();\n    }, [\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue || customValue === layer.originalValue) return;\n            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                canvasObject.set(\"text\", customValue);\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        id: layer.id,\n                        selectable: true,\n                        hasControls: false,\n                        lockMovementX: true,\n                        lockMovementY: true,\n                        lockRotation: true,\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true\n                    });\n                    canvas.remove(canvasObject);\n                    canvas.add(img);\n                    canvas.renderAll();\n                    generatePreview();\n                });\n                return;\n            }\n        });\n        canvas.renderAll();\n        generatePreview();\n    }, [\n        customizations,\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Generate preview from canvas\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            // Create a temporary canvas with just the workspace content\n            const tempCanvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(null, {\n                width: workspace.width,\n                height: workspace.height\n            });\n            // Clone all objects except the workspace itself\n            const objectsToClone = editor.canvas.getObjects().filter((obj)=>obj.name !== \"clip\");\n            objectsToClone.forEach((obj)=>{\n                obj.clone((cloned)=>{\n                    // Adjust position relative to workspace\n                    cloned.set({\n                        left: (cloned.left || 0) - (workspace.left || 0),\n                        top: (cloned.top || 0) - (workspace.top || 0)\n                    });\n                    tempCanvas.add(cloned);\n                });\n            });\n            // Generate preview after a short delay to ensure all objects are added\n            setTimeout(()=>{\n                const dataUrl = tempCanvas.toDataURL({\n                    format: \"png\",\n                    quality: 0.9,\n                    multiplier: 1\n                });\n                onPreviewGenerated(dataUrl);\n                tempCanvas.dispose();\n            }, 100);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Handle text editing\n    const handleTextEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((layerId, value)=>{\n        onCustomizationChange(layerId, value);\n    }, [\n        onCustomizationChange\n    ]);\n    // Handle text editing events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (!layer || layer.type !== \"text\") return;\n            const currentText = target.text || \"\";\n            handleTextEdit(layerId, currentText);\n            generatePreview();\n        };\n        const handleDoubleClick = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer && layer.type === \"text\") {\n                // Enter editing mode\n                target.enterEditing();\n                target.selectAll();\n            }\n        };\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        editor.canvas.on(\"mouse:dblclick\", handleDoubleClick);\n        return ()=>{\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n            editor.canvas.off(\"mouse:dblclick\", handleDoubleClick);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        handleTextEdit,\n        generatePreview\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    ref: containerRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"EvjyI9KgWPO3k+XNXPU57W1ahZw=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvY29tcG9uZW50cy9jdXN0b21pemF0aW9uLWVkaXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUVpRTtBQUNqQztBQUMrQjtBQUNGO0FBaUJ0RCxNQUFNTyxzQkFBc0I7UUFBQyxFQUNsQ0MsWUFBWSxFQUNaQyxjQUFjLEVBQ2RDLHFCQUFxQixFQUNyQkMsa0JBQWtCLEVBQ087O0lBQ3pCLE1BQU1DLFlBQVlYLDZDQUFNQSxDQUFvQjtJQUM1QyxNQUFNWSxlQUFlWiw2Q0FBTUEsQ0FBaUI7SUFDNUMsTUFBTSxDQUFDYSxlQUFlQyxpQkFBaUIsR0FBR2IsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDYyxlQUFlQyxpQkFBaUIsR0FBR2YsK0NBQVFBLENBQWdCO0lBRWxFLGdGQUFnRjtJQUNoRixNQUFNLEVBQUVnQixJQUFJLEVBQUVDLE1BQU0sRUFBRSxHQUFHZCw0RUFBU0EsQ0FBQztRQUNqQ2UsY0FBY1osYUFBYWEsSUFBSTtRQUMvQkMsY0FBY2QsYUFBYWUsS0FBSztRQUNoQ0MsZUFBZWhCLGFBQWFpQixNQUFNO1FBQ2xDQyx3QkFBd0IsS0FBTztRQUMvQkMsY0FBYyxLQUFPO0lBQ3ZCO0lBRUEsb0JBQW9CO0lBQ3BCM0IsZ0RBQVNBLENBQUM7UUFDUixNQUFNNEIsbUJBQW1CO1lBQ3ZCLElBQUksQ0FBQ2YsYUFBYWdCLE9BQU8sSUFBSSxDQUFDakIsVUFBVWlCLE9BQU8sRUFBRSxPQUFPO1lBRXhELE1BQU1DLFlBQVlqQixhQUFhZ0IsT0FBTztZQUN0QyxNQUFNRSxpQkFBaUJELFVBQVVFLFdBQVc7WUFDNUMsTUFBTUMsa0JBQWtCSCxVQUFVSSxZQUFZO1lBRTlDLElBQUlILG1CQUFtQixLQUFLRSxvQkFBb0IsR0FBRztnQkFDakQsT0FBTztZQUNUO1lBRUEsTUFBTUUsU0FBUyxJQUFJL0IsMENBQU1BLENBQUNnQyxNQUFNLENBQUN4QixVQUFVaUIsT0FBTyxFQUFFO2dCQUNsRFEsc0JBQXNCO2dCQUN0QkMsd0JBQXdCO1lBQzFCO1lBRUFwQixLQUFLO2dCQUNIcUIsZUFBZUo7Z0JBQ2ZLLGtCQUFrQlY7WUFDcEI7WUFFQSxPQUFPSztRQUNUO1FBRUEsTUFBTUEsU0FBU1A7UUFFZixJQUFJLENBQUNPLFFBQVE7WUFDWCxNQUFNTSxZQUFZQyxXQUFXO2dCQUMzQixNQUFNQyxjQUFjZjtnQkFDcEIsSUFBSWUsYUFBYTtvQkFDZjVCLGlCQUFpQjtnQkFDbkI7WUFDRixHQUFHO1lBRUgsT0FBTyxJQUFNNkIsYUFBYUg7UUFDNUIsT0FBTztZQUNMMUIsaUJBQWlCO1FBQ25CO1FBRUEsT0FBTztZQUNMLElBQUlvQixRQUFRO2dCQUNWQSxPQUFPVSxPQUFPO1lBQ2hCO1FBQ0Y7SUFDRixHQUFHO1FBQUMzQjtLQUFLO0lBRVQseURBQXlEO0lBQ3pEbEIsZ0RBQVNBLENBQUM7UUFDUixJQUFJLEVBQUNtQixtQkFBQUEsNkJBQUFBLE9BQVFnQixNQUFNLEtBQUksQ0FBQ3JCLGVBQWU7UUFFdkMsTUFBTXFCLFNBQVNoQixPQUFPZ0IsTUFBTTtRQUM1QixNQUFNVyxtQkFBbUJ0QyxhQUFhdUMsY0FBYyxDQUFDQyxHQUFHLENBQUNDLENBQUFBLFFBQVNBLE1BQU1DLEVBQUU7UUFFMUVDLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0M7WUFDNUNOO1lBQ0FPLGNBQWNsQixPQUFPbUIsVUFBVSxHQUFHQyxNQUFNO1lBQ3hDUixnQkFBZ0J2QyxhQUFhdUMsY0FBYztRQUM3QztRQUVBLHdDQUF3QztRQUN4Q1osT0FBT21CLFVBQVUsR0FBR0UsT0FBTyxDQUFDLENBQUNDO1lBQzNCTixRQUFRQyxHQUFHLENBQUMsc0JBQXNCO2dCQUNoQ0YsSUFBSU8sSUFBSVAsRUFBRTtnQkFDVlEsTUFBTUQsSUFBSUMsSUFBSTtnQkFDZEMsTUFBTUYsSUFBSUUsSUFBSTtnQkFDZEMsWUFBWWQsaUJBQWlCZSxRQUFRLENBQUNKLElBQUlQLEVBQUU7WUFDOUM7WUFFQSxJQUFJTyxJQUFJRSxJQUFJLEtBQUssUUFBUTtnQkFDdkIsa0RBQWtEO2dCQUNsREYsSUFBSUssR0FBRyxDQUFDO29CQUNOQyxZQUFZO29CQUNaQyxTQUFTO2dCQUNYO2dCQUNBO1lBQ0Y7WUFFQSxJQUFJbEIsaUJBQWlCZSxRQUFRLENBQUNKLElBQUlQLEVBQUUsR0FBRztnQkFDckNDLFFBQVFDLEdBQUcsQ0FBQywyQkFBMkJLLElBQUlQLEVBQUUsRUFBRU8sSUFBSUMsSUFBSTtnQkFDdkQsZ0RBQWdEO2dCQUNoREQsSUFBSUssR0FBRyxDQUFDO29CQUNOQyxZQUFZO29CQUNaRSxhQUFhUixJQUFJQyxJQUFJLEtBQUssWUFBWSxRQUFRO29CQUM5Q1EsWUFBWTtvQkFDWkMsZUFBZVYsSUFBSUMsSUFBSSxLQUFLLFlBQVksT0FBTztvQkFDL0NVLGVBQWVYLElBQUlDLElBQUksS0FBSyxZQUFZLE9BQU87b0JBQy9DVyxjQUFjO29CQUNkQyxjQUFjYixJQUFJQyxJQUFJLEtBQUssWUFBWSxPQUFPO29CQUM5Q2EsY0FBY2QsSUFBSUMsSUFBSSxLQUFLLFlBQVksT0FBTztvQkFDOUNjLGdCQUFnQmYsSUFBSUMsSUFBSSxLQUFLLFlBQVksT0FBTztvQkFDaERlLFVBQVVoQixJQUFJQyxJQUFJLEtBQUssWUFBWSxPQUFPO2dCQUM1QztZQUNGLE9BQU87Z0JBQ0wsdUNBQXVDO2dCQUN2Q0QsSUFBSUssR0FBRyxDQUFDO29CQUNOQyxZQUFZO29CQUNaQyxTQUFTO29CQUNURyxlQUFlO29CQUNmQyxlQUFlO29CQUNmQyxjQUFjO29CQUNkQyxjQUFjO29CQUNkQyxjQUFjO29CQUNkQyxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7UUFDRjtRQUVBckMsT0FBT3VDLFNBQVM7SUFDbEIsR0FBRztRQUFDdkQ7UUFBUUw7UUFBZU4sYUFBYXVDLGNBQWM7S0FBQztJQUV2RCxpQ0FBaUM7SUFDakMvQyxnREFBU0EsQ0FBQztRQUNSLElBQUksRUFBQ21CLG1CQUFBQSw2QkFBQUEsT0FBUWdCLE1BQU0sS0FBSSxDQUFDckIsZUFBZTtRQUV2QyxNQUFNcUIsU0FBU2hCLE9BQU9nQixNQUFNO1FBRTVCM0IsYUFBYXVDLGNBQWMsQ0FBQ1MsT0FBTyxDQUFDUCxDQUFBQTtZQUNsQyxNQUFNMEIsY0FBY2xFLGNBQWMsQ0FBQ3dDLE1BQU1DLEVBQUUsQ0FBQztZQUM1QyxJQUFJLENBQUN5QixlQUFlQSxnQkFBZ0IxQixNQUFNMkIsYUFBYSxFQUFFO1lBRXpELE1BQU1DLGVBQWUxQyxPQUFPbUIsVUFBVSxHQUFHd0IsSUFBSSxDQUFDLENBQUNyQixNQUFhQSxJQUFJUCxFQUFFLEtBQUtELE1BQU1DLEVBQUU7WUFDL0UsSUFBSSxDQUFDMkIsY0FBYztZQUVuQixJQUFJNUIsTUFBTVMsSUFBSSxLQUFLLFVBQVVtQixhQUFhbkIsSUFBSSxLQUFLLFdBQVc7Z0JBQzNEbUIsYUFBZ0NmLEdBQUcsQ0FBQyxRQUFRYTtZQUMvQyxPQUFPLElBQUkxQixNQUFNUyxJQUFJLEtBQUssV0FBV2lCLFlBQVlJLFVBQVUsQ0FBQyxVQUFVO2dCQUNwRTNFLDBDQUFNQSxDQUFDNEUsS0FBSyxDQUFDQyxPQUFPLENBQUNOLGFBQWEsQ0FBQ087b0JBQ2pDLElBQUksQ0FBQy9DLFFBQVE7b0JBRWIsdUNBQXVDO29CQUN2QyxNQUFNZ0QsU0FBU04sYUFBYXRELEtBQUssR0FBSTJELElBQUkzRCxLQUFLO29CQUM5QyxNQUFNNkQsU0FBU1AsYUFBYXBELE1BQU0sR0FBSXlELElBQUl6RCxNQUFNO29CQUNoRCxNQUFNNEQsUUFBUUMsS0FBS0MsR0FBRyxDQUFDSixRQUFRQztvQkFFL0JGLElBQUlwQixHQUFHLENBQUM7d0JBQ04wQixNQUFNWCxhQUFhVyxJQUFJO3dCQUN2QkMsS0FBS1osYUFBYVksR0FBRzt3QkFDckJOLFFBQVFFO3dCQUNSRCxRQUFRQzt3QkFDUkssU0FBU2IsYUFBYWEsT0FBTzt3QkFDN0JDLFNBQVNkLGFBQWFjLE9BQU87d0JBQzdCekMsSUFBSUQsTUFBTUMsRUFBRTt3QkFDWmEsWUFBWTt3QkFDWkUsYUFBYTt3QkFDYkUsZUFBZTt3QkFDZkMsZUFBZTt3QkFDZkMsY0FBYzt3QkFDZEMsY0FBYzt3QkFDZEMsY0FBYzt3QkFDZEMsZ0JBQWdCO29CQUNsQjtvQkFFQXJDLE9BQU95RCxNQUFNLENBQUNmO29CQUNkMUMsT0FBTzBELEdBQUcsQ0FBQ1g7b0JBQ1gvQyxPQUFPdUMsU0FBUztvQkFDaEJvQjtnQkFDRjtnQkFDQTtZQUNGO1FBQ0Y7UUFFQTNELE9BQU91QyxTQUFTO1FBQ2hCb0I7SUFDRixHQUFHO1FBQUNyRjtRQUFnQlU7UUFBUUw7UUFBZU4sYUFBYXVDLGNBQWM7S0FBQztJQUV2RSwrQkFBK0I7SUFDL0IsTUFBTStDLGtCQUFrQjNGLGtEQUFXQSxDQUFDO1FBQ2xDLElBQUksRUFBQ2dCLG1CQUFBQSw2QkFBQUEsT0FBUWdCLE1BQU0sR0FBRTtRQUVyQixJQUFJO1lBQ0YsTUFBTTRELFlBQVk1RSxPQUFPZ0IsTUFBTSxDQUFDbUIsVUFBVSxHQUFHd0IsSUFBSSxDQUFDLENBQUNyQixNQUFhQSxJQUFJRSxJQUFJLEtBQUs7WUFDN0UsSUFBSSxDQUFDb0MsV0FBVztZQUVoQiw0REFBNEQ7WUFDNUQsTUFBTUMsYUFBYSxJQUFJNUYsMENBQU1BLENBQUNnQyxNQUFNLENBQUMsTUFBTTtnQkFDekNiLE9BQU93RSxVQUFVeEUsS0FBSztnQkFDdEJFLFFBQVFzRSxVQUFVdEUsTUFBTTtZQUMxQjtZQUVBLGdEQUFnRDtZQUNoRCxNQUFNd0UsaUJBQWlCOUUsT0FBT2dCLE1BQU0sQ0FBQ21CLFVBQVUsR0FBRzRDLE1BQU0sQ0FBQyxDQUFDekMsTUFBYUEsSUFBSUUsSUFBSSxLQUFLO1lBRXBGc0MsZUFBZXpDLE9BQU8sQ0FBQyxDQUFDQztnQkFDdEJBLElBQUkwQyxLQUFLLENBQUMsQ0FBQ0M7b0JBQ1Qsd0NBQXdDO29CQUN4Q0EsT0FBT3RDLEdBQUcsQ0FBQzt3QkFDVDBCLE1BQU0sQ0FBQ1ksT0FBT1osSUFBSSxJQUFJLEtBQU1PLENBQUFBLFVBQVVQLElBQUksSUFBSTt3QkFDOUNDLEtBQUssQ0FBQ1csT0FBT1gsR0FBRyxJQUFJLEtBQU1NLENBQUFBLFVBQVVOLEdBQUcsSUFBSTtvQkFDN0M7b0JBQ0FPLFdBQVdILEdBQUcsQ0FBQ087Z0JBQ2pCO1lBQ0Y7WUFFQSx1RUFBdUU7WUFDdkUxRCxXQUFXO2dCQUNULE1BQU0yRCxVQUFVTCxXQUFXTSxTQUFTLENBQUM7b0JBQ25DQyxRQUFRO29CQUNSQyxTQUFTO29CQUNUQyxZQUFZO2dCQUNkO2dCQUNBOUYsbUJBQW1CMEY7Z0JBQ25CTCxXQUFXbkQsT0FBTztZQUNwQixHQUFHO1FBQ0wsRUFBRSxPQUFPNkQsT0FBTztZQUNkdkQsUUFBUXVELEtBQUssQ0FBQywrQkFBK0JBO1FBQy9DO0lBQ0YsR0FBRztRQUFDdkY7UUFBUVI7S0FBbUI7SUFFL0Isc0JBQXNCO0lBQ3RCLE1BQU1nRyxpQkFBaUJ4RyxrREFBV0EsQ0FBQyxDQUFDeUcsU0FBaUJDO1FBQ25Ebkcsc0JBQXNCa0csU0FBU0M7SUFDakMsR0FBRztRQUFDbkc7S0FBc0I7SUFFMUIsNkJBQTZCO0lBQzdCVixnREFBU0EsQ0FBQztRQUNSLElBQUksRUFBQ21CLG1CQUFBQSw2QkFBQUEsT0FBUWdCLE1BQU0sR0FBRTtRQUVyQixNQUFNMkUsb0JBQW9CLENBQUNDO1lBQ3pCLE1BQU1DLFNBQVNELEVBQUVDLE1BQU07WUFDdkIsSUFBSSxDQUFDQSxVQUFVQSxPQUFPdEQsSUFBSSxLQUFLLFdBQVc7WUFFMUMsTUFBTWtELFVBQVUsT0FBZ0IxRCxFQUFFO1lBQ2xDLE1BQU1ELFFBQVF6QyxhQUFhdUMsY0FBYyxDQUFDK0IsSUFBSSxDQUFDbUMsQ0FBQUEsSUFBS0EsRUFBRS9ELEVBQUUsS0FBSzBEO1lBQzdELElBQUksQ0FBQzNELFNBQVNBLE1BQU1TLElBQUksS0FBSyxRQUFRO1lBRXJDLE1BQU13RCxjQUFjLE9BQTJCQyxJQUFJLElBQUk7WUFDdkRSLGVBQWVDLFNBQVNNO1lBQ3hCcEI7UUFDRjtRQUVBLE1BQU1zQixvQkFBb0IsQ0FBQ0w7WUFDekIsTUFBTUMsU0FBU0QsRUFBRUMsTUFBTTtZQUN2QixJQUFJLENBQUNBLFVBQVVBLE9BQU90RCxJQUFJLEtBQUssV0FBVztZQUUxQyxNQUFNa0QsVUFBVSxPQUFnQjFELEVBQUU7WUFDbEMsTUFBTUQsUUFBUXpDLGFBQWF1QyxjQUFjLENBQUMrQixJQUFJLENBQUNtQyxDQUFBQSxJQUFLQSxFQUFFL0QsRUFBRSxLQUFLMEQ7WUFDN0QsSUFBSTNELFNBQVNBLE1BQU1TLElBQUksS0FBSyxRQUFRO2dCQUNsQyxxQkFBcUI7Z0JBQ3BCc0QsT0FBMEJLLFlBQVk7Z0JBQ3RDTCxPQUEwQk0sU0FBUztZQUN0QztRQUNGO1FBRUFuRyxPQUFPZ0IsTUFBTSxDQUFDb0YsRUFBRSxDQUFDLGdCQUFnQlQ7UUFDakMzRixPQUFPZ0IsTUFBTSxDQUFDb0YsRUFBRSxDQUFDLGtCQUFrQkg7UUFFbkMsT0FBTztZQUNMakcsT0FBT2dCLE1BQU0sQ0FBQ3FGLEdBQUcsQ0FBQyxnQkFBZ0JWO1lBQ2xDM0YsT0FBT2dCLE1BQU0sQ0FBQ3FGLEdBQUcsQ0FBQyxrQkFBa0JKO1FBQ3RDO0lBQ0YsR0FBRztRQUFDakc7UUFBUVgsYUFBYXVDLGNBQWM7UUFBRTREO1FBQWdCYjtLQUFnQjtJQUV6RSxxQkFDRSw4REFBQzJCO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7b0JBQW1CQyxLQUFLOUc7OEJBQ3JDLDRFQUFDc0I7d0JBQU93RixLQUFLL0c7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBR2pCLDhEQUFDTixzRUFBTUE7Z0JBQUNhLFFBQVFBOzs7Ozs7Ozs7Ozs7QUFHdEIsRUFBRTtHQTVSV1o7O1FBWWNGLHdFQUFTQTs7O0tBWnZCRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvZmVhdHVyZXMvZWRpdG9yL2NvbXBvbmVudHMvY3VzdG9taXphdGlvbi1lZGl0b3IudHN4Pzk0NGIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2sgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGZhYnJpYyB9IGZyb20gXCJmYWJyaWNcIjtcbmltcG9ydCB7IHVzZUVkaXRvciB9IGZyb20gXCJAL2ZlYXR1cmVzL2VkaXRvci9ob29rcy91c2UtZWRpdG9yXCI7XG5pbXBvcnQgeyBGb290ZXIgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvY29tcG9uZW50cy9mb290ZXJcIjtcbmltcG9ydCB7IEVkaXRhYmxlTGF5ZXIgfSBmcm9tIFwiQC90eXBlcy90ZW1wbGF0ZVwiO1xuXG5pbnRlcmZhY2UgQ3VzdG9taXphdGlvbkVkaXRvclByb3BzIHtcbiAgdGVtcGxhdGVEYXRhOiB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgd2lkdGg6IG51bWJlcjtcbiAgICBoZWlnaHQ6IG51bWJlcjtcbiAgICBqc29uOiBzdHJpbmc7XG4gICAgZWRpdGFibGVMYXllcnM6IEVkaXRhYmxlTGF5ZXJbXTtcbiAgfTtcbiAgY3VzdG9taXphdGlvbnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz47XG4gIG9uQ3VzdG9taXphdGlvbkNoYW5nZTogKGxheWVySWQ6IHN0cmluZywgdmFsdWU6IHN0cmluZykgPT4gdm9pZDtcbiAgb25QcmV2aWV3R2VuZXJhdGVkOiAoZGF0YVVybDogc3RyaW5nKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgY29uc3QgQ3VzdG9taXphdGlvbkVkaXRvciA9ICh7XG4gIHRlbXBsYXRlRGF0YSxcbiAgY3VzdG9taXphdGlvbnMsXG4gIG9uQ3VzdG9taXphdGlvbkNoYW5nZSxcbiAgb25QcmV2aWV3R2VuZXJhdGVkLFxufTogQ3VzdG9taXphdGlvbkVkaXRvclByb3BzKSA9PiB7XG4gIGNvbnN0IGNhbnZhc1JlZiA9IHVzZVJlZjxIVE1MQ2FudmFzRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IGNvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IFtpc0luaXRpYWxpemVkLCBzZXRJc0luaXRpYWxpemVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2FjdGl2ZUxheWVySWQsIHNldEFjdGl2ZUxheWVySWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gSW5pdGlhbGl6ZSBlZGl0b3Igd2l0aCBuby1zYXZlIGNhbGxiYWNrIHNpbmNlIHRoaXMgaXMgcmVhZC1vbmx5IGN1c3RvbWl6YXRpb25cbiAgY29uc3QgeyBpbml0LCBlZGl0b3IgfSA9IHVzZUVkaXRvcih7XG4gICAgZGVmYXVsdFN0YXRlOiB0ZW1wbGF0ZURhdGEuanNvbixcbiAgICBkZWZhdWx0V2lkdGg6IHRlbXBsYXRlRGF0YS53aWR0aCxcbiAgICBkZWZhdWx0SGVpZ2h0OiB0ZW1wbGF0ZURhdGEuaGVpZ2h0LFxuICAgIGNsZWFyU2VsZWN0aW9uQ2FsbGJhY2s6ICgpID0+IHt9LFxuICAgIHNhdmVDYWxsYmFjazogKCkgPT4ge30sIC8vIE5vIHNhdmluZyBpbiBjdXN0b21pemF0aW9uIG1vZGVcbiAgfSk7XG5cbiAgLy8gSW5pdGlhbGl6ZSBjYW52YXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpbml0aWFsaXplQ2FudmFzID0gKCkgPT4ge1xuICAgICAgaWYgKCFjb250YWluZXJSZWYuY3VycmVudCB8fCAhY2FudmFzUmVmLmN1cnJlbnQpIHJldHVybiBmYWxzZTtcblxuICAgICAgY29uc3QgY29udGFpbmVyID0gY29udGFpbmVyUmVmLmN1cnJlbnQ7XG4gICAgICBjb25zdCBjb250YWluZXJXaWR0aCA9IGNvbnRhaW5lci5vZmZzZXRXaWR0aDtcbiAgICAgIGNvbnN0IGNvbnRhaW5lckhlaWdodCA9IGNvbnRhaW5lci5vZmZzZXRIZWlnaHQ7XG5cbiAgICAgIGlmIChjb250YWluZXJXaWR0aCA9PT0gMCB8fCBjb250YWluZXJIZWlnaHQgPT09IDApIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBjYW52YXMgPSBuZXcgZmFicmljLkNhbnZhcyhjYW52YXNSZWYuY3VycmVudCwge1xuICAgICAgICBjb250cm9sc0Fib3ZlT3ZlcmxheTogdHJ1ZSxcbiAgICAgICAgcHJlc2VydmVPYmplY3RTdGFja2luZzogdHJ1ZSxcbiAgICAgIH0pO1xuXG4gICAgICBpbml0KHtcbiAgICAgICAgaW5pdGlhbENhbnZhczogY2FudmFzLFxuICAgICAgICBpbml0aWFsQ29udGFpbmVyOiBjb250YWluZXIsXG4gICAgICB9KTtcblxuICAgICAgcmV0dXJuIGNhbnZhcztcbiAgICB9O1xuXG4gICAgY29uc3QgY2FudmFzID0gaW5pdGlhbGl6ZUNhbnZhcygpO1xuXG4gICAgaWYgKCFjYW52YXMpIHtcbiAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBjb25zdCByZXRyeUNhbnZhcyA9IGluaXRpYWxpemVDYW52YXMoKTtcbiAgICAgICAgaWYgKHJldHJ5Q2FudmFzKSB7XG4gICAgICAgICAgc2V0SXNJbml0aWFsaXplZCh0cnVlKTtcbiAgICAgICAgfVxuICAgICAgfSwgMTAwKTtcblxuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRJc0luaXRpYWxpemVkKHRydWUpO1xuICAgIH1cblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAoY2FudmFzKSB7XG4gICAgICAgIGNhbnZhcy5kaXNwb3NlKCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW2luaXRdKTtcblxuICAvLyBMb2NrIG5vbi1lZGl0YWJsZSBvYmplY3RzIGFuZCBzZXR1cCBjdXN0b21pemF0aW9uIG1vZGVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWVkaXRvcj8uY2FudmFzIHx8ICFpc0luaXRpYWxpemVkKSByZXR1cm47XG5cbiAgICBjb25zdCBjYW52YXMgPSBlZGl0b3IuY2FudmFzO1xuICAgIGNvbnN0IGVkaXRhYmxlTGF5ZXJJZHMgPSB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMubWFwKGxheWVyID0+IGxheWVyLmlkKTtcblxuICAgIGNvbnNvbGUubG9nKCdTZXR0aW5nIHVwIGN1c3RvbWl6YXRpb24gbW9kZTonLCB7XG4gICAgICBlZGl0YWJsZUxheWVySWRzLFxuICAgICAgdG90YWxPYmplY3RzOiBjYW52YXMuZ2V0T2JqZWN0cygpLmxlbmd0aCxcbiAgICAgIGVkaXRhYmxlTGF5ZXJzOiB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnNcbiAgICB9KTtcblxuICAgIC8vIExvY2sgYWxsIG9iamVjdHMgZXhjZXB0IGVkaXRhYmxlIG9uZXNcbiAgICBjYW52YXMuZ2V0T2JqZWN0cygpLmZvckVhY2goKG9iajogYW55KSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnUHJvY2Vzc2luZyBvYmplY3Q6Jywge1xuICAgICAgICBpZDogb2JqLmlkLFxuICAgICAgICB0eXBlOiBvYmoudHlwZSxcbiAgICAgICAgbmFtZTogb2JqLm5hbWUsXG4gICAgICAgIGlzRWRpdGFibGU6IGVkaXRhYmxlTGF5ZXJJZHMuaW5jbHVkZXMob2JqLmlkKVxuICAgICAgfSk7XG5cbiAgICAgIGlmIChvYmoubmFtZSA9PT0gXCJjbGlwXCIpIHtcbiAgICAgICAgLy8gS2VlcCB3b3Jrc3BhY2UgYXMgaXMgYnV0IG1ha2UgaXQgbm9uLXNlbGVjdGFibGVcbiAgICAgICAgb2JqLnNldCh7XG4gICAgICAgICAgc2VsZWN0YWJsZTogZmFsc2UsXG4gICAgICAgICAgZXZlbnRlZDogZmFsc2UsXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGlmIChlZGl0YWJsZUxheWVySWRzLmluY2x1ZGVzKG9iai5pZCkpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ01ha2luZyBvYmplY3QgZWRpdGFibGU6Jywgb2JqLmlkLCBvYmoudHlwZSk7XG4gICAgICAgIC8vIE1ha2UgZWRpdGFibGUgb2JqZWN0cyBzZWxlY3RhYmxlIGFuZCBlZGl0YWJsZVxuICAgICAgICBvYmouc2V0KHtcbiAgICAgICAgICBzZWxlY3RhYmxlOiB0cnVlLFxuICAgICAgICAgIGhhc0NvbnRyb2xzOiBvYmoudHlwZSA9PT0gJ3RleHRib3gnID8gZmFsc2UgOiB0cnVlLCAvLyBUZXh0IGRvZXNuJ3QgbmVlZCByZXNpemUgY29udHJvbHNcbiAgICAgICAgICBoYXNCb3JkZXJzOiB0cnVlLFxuICAgICAgICAgIGxvY2tNb3ZlbWVudFg6IG9iai50eXBlID09PSAndGV4dGJveCcgPyB0cnVlIDogZmFsc2UsIC8vIFRleHQgY2FuJ3QgYmUgbW92ZWRcbiAgICAgICAgICBsb2NrTW92ZW1lbnRZOiBvYmoudHlwZSA9PT0gJ3RleHRib3gnID8gdHJ1ZSA6IGZhbHNlLFxuICAgICAgICAgIGxvY2tSb3RhdGlvbjogdHJ1ZSxcbiAgICAgICAgICBsb2NrU2NhbGluZ1g6IG9iai50eXBlID09PSAndGV4dGJveCcgPyB0cnVlIDogZmFsc2UsXG4gICAgICAgICAgbG9ja1NjYWxpbmdZOiBvYmoudHlwZSA9PT0gJ3RleHRib3gnID8gdHJ1ZSA6IGZhbHNlLFxuICAgICAgICAgIGxvY2tVbmlTY2FsaW5nOiBvYmoudHlwZSA9PT0gJ3RleHRib3gnID8gdHJ1ZSA6IGZhbHNlLFxuICAgICAgICAgIGVkaXRhYmxlOiBvYmoudHlwZSA9PT0gJ3RleHRib3gnID8gdHJ1ZSA6IGZhbHNlLCAvLyBFbmFibGUgdGV4dCBlZGl0aW5nXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gTG9jayBub24tZWRpdGFibGUgb2JqZWN0cyBjb21wbGV0ZWx5XG4gICAgICAgIG9iai5zZXQoe1xuICAgICAgICAgIHNlbGVjdGFibGU6IGZhbHNlLFxuICAgICAgICAgIGV2ZW50ZWQ6IGZhbHNlLFxuICAgICAgICAgIGxvY2tNb3ZlbWVudFg6IHRydWUsXG4gICAgICAgICAgbG9ja01vdmVtZW50WTogdHJ1ZSxcbiAgICAgICAgICBsb2NrUm90YXRpb246IHRydWUsXG4gICAgICAgICAgbG9ja1NjYWxpbmdYOiB0cnVlLFxuICAgICAgICAgIGxvY2tTY2FsaW5nWTogdHJ1ZSxcbiAgICAgICAgICBsb2NrVW5pU2NhbGluZzogdHJ1ZSxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBjYW52YXMucmVuZGVyQWxsKCk7XG4gIH0sIFtlZGl0b3IsIGlzSW5pdGlhbGl6ZWQsIHRlbXBsYXRlRGF0YS5lZGl0YWJsZUxheWVyc10pO1xuXG4gIC8vIEFwcGx5IGN1c3RvbWl6YXRpb25zIHRvIGNhbnZhc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZWRpdG9yPy5jYW52YXMgfHwgIWlzSW5pdGlhbGl6ZWQpIHJldHVybjtcblxuICAgIGNvbnN0IGNhbnZhcyA9IGVkaXRvci5jYW52YXM7XG5cbiAgICB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMuZm9yRWFjaChsYXllciA9PiB7XG4gICAgICBjb25zdCBjdXN0b21WYWx1ZSA9IGN1c3RvbWl6YXRpb25zW2xheWVyLmlkXTtcbiAgICAgIGlmICghY3VzdG9tVmFsdWUgfHwgY3VzdG9tVmFsdWUgPT09IGxheWVyLm9yaWdpbmFsVmFsdWUpIHJldHVybjtcblxuICAgICAgY29uc3QgY2FudmFzT2JqZWN0ID0gY2FudmFzLmdldE9iamVjdHMoKS5maW5kKChvYmo6IGFueSkgPT4gb2JqLmlkID09PSBsYXllci5pZCk7XG4gICAgICBpZiAoIWNhbnZhc09iamVjdCkgcmV0dXJuO1xuXG4gICAgICBpZiAobGF5ZXIudHlwZSA9PT0gJ3RleHQnICYmIGNhbnZhc09iamVjdC50eXBlID09PSAndGV4dGJveCcpIHtcbiAgICAgICAgKGNhbnZhc09iamVjdCBhcyBmYWJyaWMuVGV4dGJveCkuc2V0KCd0ZXh0JywgY3VzdG9tVmFsdWUpO1xuICAgICAgfSBlbHNlIGlmIChsYXllci50eXBlID09PSAnaW1hZ2UnICYmIGN1c3RvbVZhbHVlLnN0YXJ0c1dpdGgoJ2Jsb2I6JykpIHtcbiAgICAgICAgZmFicmljLkltYWdlLmZyb21VUkwoY3VzdG9tVmFsdWUsIChpbWcpID0+IHtcbiAgICAgICAgICBpZiAoIWNhbnZhcykgcmV0dXJuO1xuXG4gICAgICAgICAgLy8gU2NhbGUgaW1hZ2UgdG8gZml0IHRoZSBvYmplY3QgYm91bmRzXG4gICAgICAgICAgY29uc3Qgc2NhbGVYID0gY2FudmFzT2JqZWN0LndpZHRoISAvIGltZy53aWR0aCE7XG4gICAgICAgICAgY29uc3Qgc2NhbGVZID0gY2FudmFzT2JqZWN0LmhlaWdodCEgLyBpbWcuaGVpZ2h0ITtcbiAgICAgICAgICBjb25zdCBzY2FsZSA9IE1hdGgubWluKHNjYWxlWCwgc2NhbGVZKTtcblxuICAgICAgICAgIGltZy5zZXQoe1xuICAgICAgICAgICAgbGVmdDogY2FudmFzT2JqZWN0LmxlZnQsXG4gICAgICAgICAgICB0b3A6IGNhbnZhc09iamVjdC50b3AsXG4gICAgICAgICAgICBzY2FsZVg6IHNjYWxlLFxuICAgICAgICAgICAgc2NhbGVZOiBzY2FsZSxcbiAgICAgICAgICAgIG9yaWdpblg6IGNhbnZhc09iamVjdC5vcmlnaW5YLFxuICAgICAgICAgICAgb3JpZ2luWTogY2FudmFzT2JqZWN0Lm9yaWdpblksXG4gICAgICAgICAgICBpZDogbGF5ZXIuaWQsXG4gICAgICAgICAgICBzZWxlY3RhYmxlOiB0cnVlLFxuICAgICAgICAgICAgaGFzQ29udHJvbHM6IGZhbHNlLFxuICAgICAgICAgICAgbG9ja01vdmVtZW50WDogdHJ1ZSxcbiAgICAgICAgICAgIGxvY2tNb3ZlbWVudFk6IHRydWUsXG4gICAgICAgICAgICBsb2NrUm90YXRpb246IHRydWUsXG4gICAgICAgICAgICBsb2NrU2NhbGluZ1g6IHRydWUsXG4gICAgICAgICAgICBsb2NrU2NhbGluZ1k6IHRydWUsXG4gICAgICAgICAgICBsb2NrVW5pU2NhbGluZzogdHJ1ZSxcbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIGNhbnZhcy5yZW1vdmUoY2FudmFzT2JqZWN0KTtcbiAgICAgICAgICBjYW52YXMuYWRkKGltZyk7XG4gICAgICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xuICAgICAgICAgIGdlbmVyYXRlUHJldmlldygpO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgY2FudmFzLnJlbmRlckFsbCgpO1xuICAgIGdlbmVyYXRlUHJldmlldygpO1xuICB9LCBbY3VzdG9taXphdGlvbnMsIGVkaXRvciwgaXNJbml0aWFsaXplZCwgdGVtcGxhdGVEYXRhLmVkaXRhYmxlTGF5ZXJzXSk7XG5cbiAgLy8gR2VuZXJhdGUgcHJldmlldyBmcm9tIGNhbnZhc1xuICBjb25zdCBnZW5lcmF0ZVByZXZpZXcgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFlZGl0b3I/LmNhbnZhcykgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHdvcmtzcGFjZSA9IGVkaXRvci5jYW52YXMuZ2V0T2JqZWN0cygpLmZpbmQoKG9iajogYW55KSA9PiBvYmoubmFtZSA9PT0gXCJjbGlwXCIpO1xuICAgICAgaWYgKCF3b3Jrc3BhY2UpIHJldHVybjtcblxuICAgICAgLy8gQ3JlYXRlIGEgdGVtcG9yYXJ5IGNhbnZhcyB3aXRoIGp1c3QgdGhlIHdvcmtzcGFjZSBjb250ZW50XG4gICAgICBjb25zdCB0ZW1wQ2FudmFzID0gbmV3IGZhYnJpYy5DYW52YXMobnVsbCwge1xuICAgICAgICB3aWR0aDogd29ya3NwYWNlLndpZHRoLFxuICAgICAgICBoZWlnaHQ6IHdvcmtzcGFjZS5oZWlnaHQsXG4gICAgICB9KTtcblxuICAgICAgLy8gQ2xvbmUgYWxsIG9iamVjdHMgZXhjZXB0IHRoZSB3b3Jrc3BhY2UgaXRzZWxmXG4gICAgICBjb25zdCBvYmplY3RzVG9DbG9uZSA9IGVkaXRvci5jYW52YXMuZ2V0T2JqZWN0cygpLmZpbHRlcigob2JqOiBhbnkpID0+IG9iai5uYW1lICE9PSBcImNsaXBcIik7XG4gICAgICBcbiAgICAgIG9iamVjdHNUb0Nsb25lLmZvckVhY2goKG9iaikgPT4ge1xuICAgICAgICBvYmouY2xvbmUoKGNsb25lZDogZmFicmljLk9iamVjdCkgPT4ge1xuICAgICAgICAgIC8vIEFkanVzdCBwb3NpdGlvbiByZWxhdGl2ZSB0byB3b3Jrc3BhY2VcbiAgICAgICAgICBjbG9uZWQuc2V0KHtcbiAgICAgICAgICAgIGxlZnQ6IChjbG9uZWQubGVmdCB8fCAwKSAtICh3b3Jrc3BhY2UubGVmdCB8fCAwKSxcbiAgICAgICAgICAgIHRvcDogKGNsb25lZC50b3AgfHwgMCkgLSAod29ya3NwYWNlLnRvcCB8fCAwKSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgICB0ZW1wQ2FudmFzLmFkZChjbG9uZWQpO1xuICAgICAgICB9KTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBHZW5lcmF0ZSBwcmV2aWV3IGFmdGVyIGEgc2hvcnQgZGVsYXkgdG8gZW5zdXJlIGFsbCBvYmplY3RzIGFyZSBhZGRlZFxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGNvbnN0IGRhdGFVcmwgPSB0ZW1wQ2FudmFzLnRvRGF0YVVSTCh7XG4gICAgICAgICAgZm9ybWF0OiAncG5nJyxcbiAgICAgICAgICBxdWFsaXR5OiAwLjksXG4gICAgICAgICAgbXVsdGlwbGllcjogMSxcbiAgICAgICAgfSk7XG4gICAgICAgIG9uUHJldmlld0dlbmVyYXRlZChkYXRhVXJsKTtcbiAgICAgICAgdGVtcENhbnZhcy5kaXNwb3NlKCk7XG4gICAgICB9LCAxMDApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2VuZXJhdGUgcHJldmlldzonLCBlcnJvcik7XG4gICAgfVxuICB9LCBbZWRpdG9yLCBvblByZXZpZXdHZW5lcmF0ZWRdKTtcblxuICAvLyBIYW5kbGUgdGV4dCBlZGl0aW5nXG4gIGNvbnN0IGhhbmRsZVRleHRFZGl0ID0gdXNlQ2FsbGJhY2soKGxheWVySWQ6IHN0cmluZywgdmFsdWU6IHN0cmluZykgPT4ge1xuICAgIG9uQ3VzdG9taXphdGlvbkNoYW5nZShsYXllcklkLCB2YWx1ZSk7XG4gIH0sIFtvbkN1c3RvbWl6YXRpb25DaGFuZ2VdKTtcblxuICAvLyBIYW5kbGUgdGV4dCBlZGl0aW5nIGV2ZW50c1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZWRpdG9yPy5jYW52YXMpIHJldHVybjtcblxuICAgIGNvbnN0IGhhbmRsZVRleHRDaGFuZ2VkID0gKGU6IGZhYnJpYy5JRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0O1xuICAgICAgaWYgKCF0YXJnZXQgfHwgdGFyZ2V0LnR5cGUgIT09ICd0ZXh0Ym94JykgcmV0dXJuO1xuXG4gICAgICBjb25zdCBsYXllcklkID0gKHRhcmdldCBhcyBhbnkpLmlkO1xuICAgICAgY29uc3QgbGF5ZXIgPSB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMuZmluZChsID0+IGwuaWQgPT09IGxheWVySWQpO1xuICAgICAgaWYgKCFsYXllciB8fCBsYXllci50eXBlICE9PSAndGV4dCcpIHJldHVybjtcblxuICAgICAgY29uc3QgY3VycmVudFRleHQgPSAodGFyZ2V0IGFzIGZhYnJpYy5UZXh0Ym94KS50ZXh0IHx8ICcnO1xuICAgICAgaGFuZGxlVGV4dEVkaXQobGF5ZXJJZCwgY3VycmVudFRleHQpO1xuICAgICAgZ2VuZXJhdGVQcmV2aWV3KCk7XG4gICAgfTtcblxuICAgIGNvbnN0IGhhbmRsZURvdWJsZUNsaWNrID0gKGU6IGZhYnJpYy5JRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0O1xuICAgICAgaWYgKCF0YXJnZXQgfHwgdGFyZ2V0LnR5cGUgIT09ICd0ZXh0Ym94JykgcmV0dXJuO1xuXG4gICAgICBjb25zdCBsYXllcklkID0gKHRhcmdldCBhcyBhbnkpLmlkO1xuICAgICAgY29uc3QgbGF5ZXIgPSB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMuZmluZChsID0+IGwuaWQgPT09IGxheWVySWQpO1xuICAgICAgaWYgKGxheWVyICYmIGxheWVyLnR5cGUgPT09ICd0ZXh0Jykge1xuICAgICAgICAvLyBFbnRlciBlZGl0aW5nIG1vZGVcbiAgICAgICAgKHRhcmdldCBhcyBmYWJyaWMuVGV4dGJveCkuZW50ZXJFZGl0aW5nKCk7XG4gICAgICAgICh0YXJnZXQgYXMgZmFicmljLlRleHRib3gpLnNlbGVjdEFsbCgpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBlZGl0b3IuY2FudmFzLm9uKCd0ZXh0OmNoYW5nZWQnLCBoYW5kbGVUZXh0Q2hhbmdlZCk7XG4gICAgZWRpdG9yLmNhbnZhcy5vbignbW91c2U6ZGJsY2xpY2snLCBoYW5kbGVEb3VibGVDbGljayk7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZWRpdG9yLmNhbnZhcy5vZmYoJ3RleHQ6Y2hhbmdlZCcsIGhhbmRsZVRleHRDaGFuZ2VkKTtcbiAgICAgIGVkaXRvci5jYW52YXMub2ZmKCdtb3VzZTpkYmxjbGljaycsIGhhbmRsZURvdWJsZUNsaWNrKTtcbiAgICB9O1xuICB9LCBbZWRpdG9yLCB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMsIGhhbmRsZVRleHRFZGl0LCBnZW5lcmF0ZVByZXZpZXddKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggZmxleC1jb2wgYmctbXV0ZWRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiIHJlZj17Y29udGFpbmVyUmVmfT5cbiAgICAgICAgICA8Y2FudmFzIHJlZj17Y2FudmFzUmVmfSAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgPEZvb3RlciBlZGl0b3I9e2VkaXRvcn0gLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsImZhYnJpYyIsInVzZUVkaXRvciIsIkZvb3RlciIsIkN1c3RvbWl6YXRpb25FZGl0b3IiLCJ0ZW1wbGF0ZURhdGEiLCJjdXN0b21pemF0aW9ucyIsIm9uQ3VzdG9taXphdGlvbkNoYW5nZSIsIm9uUHJldmlld0dlbmVyYXRlZCIsImNhbnZhc1JlZiIsImNvbnRhaW5lclJlZiIsImlzSW5pdGlhbGl6ZWQiLCJzZXRJc0luaXRpYWxpemVkIiwiYWN0aXZlTGF5ZXJJZCIsInNldEFjdGl2ZUxheWVySWQiLCJpbml0IiwiZWRpdG9yIiwiZGVmYXVsdFN0YXRlIiwianNvbiIsImRlZmF1bHRXaWR0aCIsIndpZHRoIiwiZGVmYXVsdEhlaWdodCIsImhlaWdodCIsImNsZWFyU2VsZWN0aW9uQ2FsbGJhY2siLCJzYXZlQ2FsbGJhY2siLCJpbml0aWFsaXplQ2FudmFzIiwiY3VycmVudCIsImNvbnRhaW5lciIsImNvbnRhaW5lcldpZHRoIiwib2Zmc2V0V2lkdGgiLCJjb250YWluZXJIZWlnaHQiLCJvZmZzZXRIZWlnaHQiLCJjYW52YXMiLCJDYW52YXMiLCJjb250cm9sc0Fib3ZlT3ZlcmxheSIsInByZXNlcnZlT2JqZWN0U3RhY2tpbmciLCJpbml0aWFsQ2FudmFzIiwiaW5pdGlhbENvbnRhaW5lciIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJyZXRyeUNhbnZhcyIsImNsZWFyVGltZW91dCIsImRpc3Bvc2UiLCJlZGl0YWJsZUxheWVySWRzIiwiZWRpdGFibGVMYXllcnMiLCJtYXAiLCJsYXllciIsImlkIiwiY29uc29sZSIsImxvZyIsInRvdGFsT2JqZWN0cyIsImdldE9iamVjdHMiLCJsZW5ndGgiLCJmb3JFYWNoIiwib2JqIiwidHlwZSIsIm5hbWUiLCJpc0VkaXRhYmxlIiwiaW5jbHVkZXMiLCJzZXQiLCJzZWxlY3RhYmxlIiwiZXZlbnRlZCIsImhhc0NvbnRyb2xzIiwiaGFzQm9yZGVycyIsImxvY2tNb3ZlbWVudFgiLCJsb2NrTW92ZW1lbnRZIiwibG9ja1JvdGF0aW9uIiwibG9ja1NjYWxpbmdYIiwibG9ja1NjYWxpbmdZIiwibG9ja1VuaVNjYWxpbmciLCJlZGl0YWJsZSIsInJlbmRlckFsbCIsImN1c3RvbVZhbHVlIiwib3JpZ2luYWxWYWx1ZSIsImNhbnZhc09iamVjdCIsImZpbmQiLCJzdGFydHNXaXRoIiwiSW1hZ2UiLCJmcm9tVVJMIiwiaW1nIiwic2NhbGVYIiwic2NhbGVZIiwic2NhbGUiLCJNYXRoIiwibWluIiwibGVmdCIsInRvcCIsIm9yaWdpblgiLCJvcmlnaW5ZIiwicmVtb3ZlIiwiYWRkIiwiZ2VuZXJhdGVQcmV2aWV3Iiwid29ya3NwYWNlIiwidGVtcENhbnZhcyIsIm9iamVjdHNUb0Nsb25lIiwiZmlsdGVyIiwiY2xvbmUiLCJjbG9uZWQiLCJkYXRhVXJsIiwidG9EYXRhVVJMIiwiZm9ybWF0IiwicXVhbGl0eSIsIm11bHRpcGxpZXIiLCJlcnJvciIsImhhbmRsZVRleHRFZGl0IiwibGF5ZXJJZCIsInZhbHVlIiwiaGFuZGxlVGV4dENoYW5nZWQiLCJlIiwidGFyZ2V0IiwibCIsImN1cnJlbnRUZXh0IiwidGV4dCIsImhhbmRsZURvdWJsZUNsaWNrIiwiZW50ZXJFZGl0aW5nIiwic2VsZWN0QWxsIiwib24iLCJvZmYiLCJkaXYiLCJjbGFzc05hbWUiLCJyZWYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});