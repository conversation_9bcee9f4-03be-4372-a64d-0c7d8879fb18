"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated, activeLayerId: externalActiveLayerId, onLayerActivation } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize editor first\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{\n            onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n        },\n        saveCallback: ()=>{\n            // Generate preview when canvas changes\n            generatePreview();\n        }\n    });\n    // Generate preview from canvas (defined after editor)\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            const dataUrl = editor.canvas.toDataURL({\n                format: \"png\",\n                quality: 0.9,\n                multiplier: 0.5\n            });\n            onPreviewGenerated(dataUrl);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Initialize canvas (same as main editor)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                initializeCanvas();\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Handle selection changes to notify parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleSelectionCreated = (e)=>{\n            const target = e.target;\n            if (target) {\n                const layerId = target.id;\n                const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n                if (layer) {\n                    onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(layerId);\n                }\n            }\n        };\n        const handleSelectionCleared = ()=>{\n            onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n        };\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (target && target.type === \"textbox\") {\n                const layerId = target.id;\n                const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n                if (layer && layer.type === \"text\") {\n                    const currentText = target.text || \"\";\n                    onCustomizationChange(layerId, currentText);\n                }\n            }\n        };\n        editor.canvas.on(\"selection:created\", handleSelectionCreated);\n        editor.canvas.on(\"selection:updated\", handleSelectionCreated);\n        editor.canvas.on(\"selection:cleared\", handleSelectionCleared);\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        return ()=>{\n            editor.canvas.off(\"selection:created\", handleSelectionCreated);\n            editor.canvas.off(\"selection:updated\", handleSelectionCreated);\n            editor.canvas.off(\"selection:cleared\", handleSelectionCleared);\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        onLayerActivation,\n        onCustomizationChange\n    ]);\n    // Apply customizations from sidebar to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue) return;\n            const canvasObject = editor.canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                const textbox = canvasObject;\n                if (textbox.text !== customValue) {\n                    textbox.set(\"text\", customValue);\n                    editor.canvas.renderAll();\n                }\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                // Handle image replacement\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!editor.canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY\n                    });\n                    // Set the ID using a custom property\n                    img.id = layer.id;\n                    editor.canvas.remove(canvasObject);\n                    editor.canvas.add(img);\n                    editor.canvas.renderAll();\n                });\n            }\n        });\n    }, [\n        customizations,\n        editor,\n        templateData.editableLayers\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_5__.Toolbar, {\n                editor: editor,\n                activeTool: \"select\",\n                onChangeActiveTool: ()=>{}\n            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                ref: containerRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"d3tIc0q3A8WK7iwYVx9glHupeBc=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});