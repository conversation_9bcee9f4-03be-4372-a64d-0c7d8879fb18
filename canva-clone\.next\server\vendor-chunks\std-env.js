"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/std-env";
exports.ids = ["vendor-chunks/std-env"];
exports.modules = {

/***/ "(ssr)/./node_modules/std-env/dist/index.mjs":
/*!*********************************************!*\
  !*** ./node_modules/std-env/dist/index.mjs ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   env: () => (/* binding */ s),\n/* harmony export */   hasTTY: () => (/* binding */ R),\n/* harmony export */   hasWindow: () => (/* binding */ U),\n/* harmony export */   isBun: () => (/* binding */ L),\n/* harmony export */   isCI: () => (/* binding */ T),\n/* harmony export */   isColorSupported: () => (/* binding */ Y),\n/* harmony export */   isDebug: () => (/* binding */ h),\n/* harmony export */   isDeno: () => (/* binding */ D),\n/* harmony export */   isDevelopment: () => (/* binding */ v),\n/* harmony export */   isEdgeLight: () => (/* binding */ N),\n/* harmony export */   isFastly: () => (/* binding */ O),\n/* harmony export */   isLagon: () => (/* binding */ b),\n/* harmony export */   isLinux: () => (/* binding */ M),\n/* harmony export */   isMacOS: () => (/* binding */ V),\n/* harmony export */   isMinimal: () => (/* binding */ m),\n/* harmony export */   isNetlify: () => (/* binding */ S),\n/* harmony export */   isNode: () => (/* binding */ A),\n/* harmony export */   isProduction: () => (/* binding */ f),\n/* harmony export */   isTest: () => (/* binding */ C),\n/* harmony export */   isWindows: () => (/* binding */ a),\n/* harmony export */   isWorkerd: () => (/* binding */ u),\n/* harmony export */   nodeENV: () => (/* binding */ t),\n/* harmony export */   nodeMajorVersion: () => (/* binding */ y),\n/* harmony export */   nodeVersion: () => (/* binding */ _),\n/* harmony export */   platform: () => (/* binding */ I),\n/* harmony export */   process: () => (/* binding */ w),\n/* harmony export */   provider: () => (/* binding */ d),\n/* harmony export */   providerInfo: () => (/* binding */ l),\n/* harmony export */   runtime: () => (/* binding */ K),\n/* harmony export */   runtimeInfo: () => (/* binding */ P)\n/* harmony export */ });\nconst r=Object.create(null),E=e=>globalThis.process?.env||/* unsupported import.meta.env */ undefined||globalThis.Deno?.env.toObject()||globalThis.__env__||(e?r:globalThis),s=new Proxy(r,{get(e,o){return E()[o]??r[o]},has(e,o){const i=E();return o in i||o in r},set(e,o,i){const g=E(!0);return g[o]=i,!0},deleteProperty(e,o){if(!o)return!1;const i=E(!0);return delete i[o],!0},ownKeys(){const e=E(!0);return Object.keys(e)}}),t=typeof process<\"u\"&&process.env&&\"development\"||\"\",p=[[\"APPVEYOR\"],[\"AWS_AMPLIFY\",\"AWS_APP_ID\",{ci:!0}],[\"AZURE_PIPELINES\",\"SYSTEM_TEAMFOUNDATIONCOLLECTIONURI\"],[\"AZURE_STATIC\",\"INPUT_AZURE_STATIC_WEB_APPS_API_TOKEN\"],[\"APPCIRCLE\",\"AC_APPCIRCLE\"],[\"BAMBOO\",\"bamboo_planKey\"],[\"BITBUCKET\",\"BITBUCKET_COMMIT\"],[\"BITRISE\",\"BITRISE_IO\"],[\"BUDDY\",\"BUDDY_WORKSPACE_ID\"],[\"BUILDKITE\"],[\"CIRCLE\",\"CIRCLECI\"],[\"CIRRUS\",\"CIRRUS_CI\"],[\"CLOUDFLARE_PAGES\",\"CF_PAGES\",{ci:!0}],[\"CODEBUILD\",\"CODEBUILD_BUILD_ARN\"],[\"CODEFRESH\",\"CF_BUILD_ID\"],[\"DRONE\"],[\"DRONE\",\"DRONE_BUILD_EVENT\"],[\"DSARI\"],[\"GITHUB_ACTIONS\"],[\"GITLAB\",\"GITLAB_CI\"],[\"GITLAB\",\"CI_MERGE_REQUEST_ID\"],[\"GOCD\",\"GO_PIPELINE_LABEL\"],[\"LAYERCI\"],[\"HUDSON\",\"HUDSON_URL\"],[\"JENKINS\",\"JENKINS_URL\"],[\"MAGNUM\"],[\"NETLIFY\"],[\"NETLIFY\",\"NETLIFY_LOCAL\",{ci:!1}],[\"NEVERCODE\"],[\"RENDER\"],[\"SAIL\",\"SAILCI\"],[\"SEMAPHORE\"],[\"SCREWDRIVER\"],[\"SHIPPABLE\"],[\"SOLANO\",\"TDDIUM\"],[\"STRIDER\"],[\"TEAMCITY\",\"TEAMCITY_VERSION\"],[\"TRAVIS\"],[\"VERCEL\",\"NOW_BUILDER\"],[\"VERCEL\",\"VERCEL\",{ci:!1}],[\"VERCEL\",\"VERCEL_ENV\",{ci:!1}],[\"APPCENTER\",\"APPCENTER_BUILD_ID\"],[\"CODESANDBOX\",\"CODESANDBOX_SSE\",{ci:!1}],[\"STACKBLITZ\"],[\"STORMKIT\"],[\"CLEAVR\"],[\"ZEABUR\"],[\"CODESPHERE\",\"CODESPHERE_APP_ID\",{ci:!0}],[\"RAILWAY\",\"RAILWAY_PROJECT_ID\"],[\"RAILWAY\",\"RAILWAY_SERVICE_ID\"]];function B(){if(globalThis.process?.env)for(const e of p){const o=e[1]||e[0];if(globalThis.process?.env[o])return{name:e[0].toLowerCase(),...e[2]}}return globalThis.process?.env?.SHELL===\"/bin/jsh\"&&globalThis.process?.versions?.webcontainer?{name:\"stackblitz\",ci:!1}:{name:\"\",ci:!1}}const l=B(),d=l.name;function n(e){return e?e!==\"false\":!1}const I=globalThis.process?.platform||\"\",T=n(s.CI)||l.ci!==!1,R=n(globalThis.process?.stdout&&globalThis.process?.stdout.isTTY),U=typeof window<\"u\",h=n(s.DEBUG),C=t===\"test\"||n(s.TEST),f=t===\"production\",v=t===\"dev\"||t===\"development\",m=n(s.MINIMAL)||T||C||!R,a=/^win/i.test(I),M=/^linux/i.test(I),V=/^darwin/i.test(I),Y=!n(s.NO_COLOR)&&(n(s.FORCE_COLOR)||(R||a)&&s.TERM!==\"dumb\"||T),_=(globalThis.process?.versions?.node||\"\").replace(/^v/,\"\")||null,y=Number(_?.split(\".\")[0])||null,W=globalThis.process||Object.create(null),c={versions:{}},w=new Proxy(W,{get(e,o){if(o===\"env\")return s;if(o in e)return e[o];if(o in c)return c[o]}}),A=globalThis.process?.release?.name===\"node\",L=!!globalThis.Bun||!!globalThis.process?.versions?.bun,D=!!globalThis.Deno,O=!!globalThis.fastly,S=!!globalThis.Netlify,N=!!globalThis.EdgeRuntime,u=globalThis.navigator?.userAgent===\"Cloudflare-Workers\",b=!!globalThis.__lagon__,F=[[S,\"netlify\"],[N,\"edge-light\"],[u,\"workerd\"],[O,\"fastly\"],[D,\"deno\"],[L,\"bun\"],[A,\"node\"],[b,\"lagon\"]];function G(){const e=F.find(o=>o[0]);if(e)return{name:e[1]}}const P=G(),K=P?.name||\"\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/std-env/dist/index.mjs\n");

/***/ })

};
;