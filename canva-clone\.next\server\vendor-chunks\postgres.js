"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/postgres";
exports.ids = ["vendor-chunks/postgres"];
exports.modules = {

/***/ "(rsc)/./node_modules/postgres/src/bytes.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/bytes.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst size = 256\nlet buffer = Buffer.allocUnsafe(size)\n\nconst messages = 'BCcDdEFfHPpQSX'.split('').reduce((acc, x) => {\n  const v = x.charCodeAt(0)\n  acc[x] = () => {\n    buffer[0] = v\n    b.i = 5\n    return b\n  }\n  return acc\n}, {})\n\nconst b = Object.assign(reset, messages, {\n  N: String.fromCharCode(0),\n  i: 0,\n  inc(x) {\n    b.i += x\n    return b\n  },\n  str(x) {\n    const length = Buffer.byteLength(x)\n    fit(length)\n    b.i += buffer.write(x, b.i, length, 'utf8')\n    return b\n  },\n  i16(x) {\n    fit(2)\n    buffer.writeUInt16BE(x, b.i)\n    b.i += 2\n    return b\n  },\n  i32(x, i) {\n    if (i || i === 0) {\n      buffer.writeUInt32BE(x, i)\n      return b\n    }\n    fit(4)\n    buffer.writeUInt32BE(x, b.i)\n    b.i += 4\n    return b\n  },\n  z(x) {\n    fit(x)\n    buffer.fill(0, b.i, b.i + x)\n    b.i += x\n    return b\n  },\n  raw(x) {\n    buffer = Buffer.concat([buffer.subarray(0, b.i), x])\n    b.i = buffer.length\n    return b\n  },\n  end(at = 1) {\n    buffer.writeUInt32BE(b.i - at, at)\n    const out = buffer.subarray(0, b.i)\n    b.i = 0\n    buffer = Buffer.allocUnsafe(size)\n    return out\n  }\n})\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (b);\n\nfunction fit(x) {\n  if (buffer.length - b.i < x) {\n    const prev = buffer\n        , length = prev.length\n\n    buffer = Buffer.allocUnsafe(length + (length >> 1) + x)\n    prev.copy(buffer)\n  }\n}\n\nfunction reset() {\n  b.i = 0\n  return b\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/bytes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/connection.js":
/*!*************************************************!*\
  !*** ./node_modules/postgres/src/connection.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var net__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! net */ \"net\");\n/* harmony import */ var tls__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tls */ \"tls\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var perf_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! perf_hooks */ \"perf_hooks\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/postgres/src/types.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/postgres/src/errors.js\");\n/* harmony import */ var _result_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./result.js */ \"(rsc)/./node_modules/postgres/src/result.js\");\n/* harmony import */ var _queue_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./queue.js */ \"(rsc)/./node_modules/postgres/src/queue.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./query.js */ \"(rsc)/./node_modules/postgres/src/query.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./bytes.js */ \"(rsc)/./node_modules/postgres/src/bytes.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Connection);\n\nlet uid = 1\n\nconst Sync = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().S().end()\n    , Flush = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().H().end()\n    , SSLRequest = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().i32(8).i32(80877103).end(8)\n    , ExecuteUnnamed = Buffer.concat([(0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().E().str(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i32(0).end(), Sync])\n    , DescribeUnnamed = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().D().str('S').str(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end()\n    , noop = () => { /* noop */ }\n\nconst retryRoutines = new Set([\n  'FetchPreparedStatement',\n  'RevalidateCachedQuery',\n  'transformAssignedExpr'\n])\n\nconst errorFields = {\n  83  : 'severity_local',    // S\n  86  : 'severity',          // V\n  67  : 'code',              // C\n  77  : 'message',           // M\n  68  : 'detail',            // D\n  72  : 'hint',              // H\n  80  : 'position',          // P\n  112 : 'internal_position', // p\n  113 : 'internal_query',    // q\n  87  : 'where',             // W\n  115 : 'schema_name',       // s\n  116 : 'table_name',        // t\n  99  : 'column_name',       // c\n  100 : 'data type_name',    // d\n  110 : 'constraint_name',   // n\n  70  : 'file',              // F\n  76  : 'line',              // L\n  82  : 'routine'            // R\n}\n\nfunction Connection(options, queues = {}, { onopen = noop, onend = noop, onclose = noop } = {}) {\n  const {\n    ssl,\n    max,\n    user,\n    host,\n    port,\n    database,\n    parsers,\n    transform,\n    onnotice,\n    onnotify,\n    onparameter,\n    max_pipeline,\n    keep_alive,\n    backoff,\n    target_session_attrs\n  } = options\n\n  const sent = (0,_queue_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])()\n      , id = uid++\n      , backend = { pid: null, secret: null }\n      , idleTimer = timer(end, options.idle_timeout)\n      , lifeTimer = timer(end, options.max_lifetime)\n      , connectTimer = timer(connectTimedOut, options.connect_timeout)\n\n  let socket = null\n    , cancelMessage\n    , result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]()\n    , incoming = Buffer.alloc(0)\n    , needsTypes = options.fetch_types\n    , backendParameters = {}\n    , statements = {}\n    , statementId = Math.random().toString(36).slice(2)\n    , statementCount = 1\n    , closedDate = 0\n    , remaining = 0\n    , hostIndex = 0\n    , retries = 0\n    , length = 0\n    , delay = 0\n    , rows = 0\n    , serverSignature = null\n    , nextWriteTimer = null\n    , terminated = false\n    , incomings = null\n    , results = null\n    , initial = null\n    , ending = null\n    , stream = null\n    , chunk = null\n    , ended = null\n    , nonce = null\n    , query = null\n    , final = null\n\n  const connection = {\n    queue: queues.closed,\n    idleTimer,\n    connect(query) {\n      initial = query\n      reconnect()\n    },\n    terminate,\n    execute,\n    cancel,\n    end,\n    count: 0,\n    id\n  }\n\n  queues.closed && queues.closed.push(connection)\n\n  return connection\n\n  async function createSocket() {\n    let x\n    try {\n      x = options.socket\n        ? (await Promise.resolve(options.socket(options)))\n        : new net__WEBPACK_IMPORTED_MODULE_0__.Socket()\n    } catch (e) {\n      error(e)\n      return\n    }\n    x.on('error', error)\n    x.on('close', closed)\n    x.on('drain', drain)\n    return x\n  }\n\n  async function cancel({ pid, secret }, resolve, reject) {\n    try {\n      cancelMessage = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().i32(16).i32(80877102).i32(pid).i32(secret).end(16)\n      await connect()\n      socket.once('error', reject)\n      socket.once('close', resolve)\n    } catch (error) {\n      reject(error)\n    }\n  }\n\n  function execute(q) {\n    if (terminated)\n      return queryError(q, _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_DESTROYED', options))\n\n    if (q.cancelled)\n      return\n\n    try {\n      q.state = backend\n      query\n        ? sent.push(q)\n        : (query = q, query.active = true)\n\n      build(q)\n      return write(toBuffer(q))\n        && !q.describeFirst\n        && !q.cursorFn\n        && sent.length < max_pipeline\n        && (!q.options.onexecute || q.options.onexecute(connection))\n    } catch (error) {\n      sent.length === 0 && write(Sync)\n      errored(error)\n      return true\n    }\n  }\n\n  function toBuffer(q) {\n    if (q.parameters.length >= 65534)\n      throw _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('MAX_PARAMETERS_EXCEEDED', 'Max number of parameters (65534) exceeded')\n\n    return q.options.simple\n      ? (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().Q().str(q.statement.string + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end()\n      : q.describeFirst\n        ? Buffer.concat([describe(q), Flush])\n        : q.prepare\n          ? q.prepared\n            ? prepared(q)\n            : Buffer.concat([describe(q), prepared(q)])\n          : unnamed(q)\n  }\n\n  function describe(q) {\n    return Buffer.concat([\n      Parse(q.statement.string, q.parameters, q.statement.types, q.statement.name),\n      Describe('S', q.statement.name)\n    ])\n  }\n\n  function prepared(q) {\n    return Buffer.concat([\n      Bind(q.parameters, q.statement.types, q.statement.name, q.cursorName),\n      q.cursorFn\n        ? Execute('', q.cursorRows)\n        : ExecuteUnnamed\n    ])\n  }\n\n  function unnamed(q) {\n    return Buffer.concat([\n      Parse(q.statement.string, q.parameters, q.statement.types),\n      DescribeUnnamed,\n      prepared(q)\n    ])\n  }\n\n  function build(q) {\n    const parameters = []\n        , types = []\n\n    const string = (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.stringify)(q, q.strings[0], q.args[0], parameters, types, options)\n\n    !q.tagged && q.args.forEach(x => (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.handleValue)(x, parameters, types, options))\n\n    q.prepare = options.prepare && ('prepare' in q.options ? q.options.prepare : true)\n    q.string = string\n    q.signature = q.prepare && types + string\n    q.onlyDescribe && (delete statements[q.signature])\n    q.parameters = q.parameters || parameters\n    q.prepared = q.prepare && q.signature in statements\n    q.describeFirst = q.onlyDescribe || (parameters.length && !q.prepared)\n    q.statement = q.prepared\n      ? statements[q.signature]\n      : { string, types, name: q.prepare ? statementId + statementCount++ : '' }\n\n    typeof options.debug === 'function' && options.debug(id, string, parameters, types)\n  }\n\n  function write(x, fn) {\n    chunk = chunk ? Buffer.concat([chunk, x]) : Buffer.from(x)\n    if (fn || chunk.length >= 1024)\n      return nextWrite(fn)\n    nextWriteTimer === null && (nextWriteTimer = setImmediate(nextWrite))\n    return true\n  }\n\n  function nextWrite(fn) {\n    const x = socket.write(chunk, fn)\n    nextWriteTimer !== null && clearImmediate(nextWriteTimer)\n    chunk = nextWriteTimer = null\n    return x\n  }\n\n  function connectTimedOut() {\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECT_TIMEOUT', options, socket))\n    socket.destroy()\n  }\n\n  async function secure() {\n    write(SSLRequest)\n    const canSSL = await new Promise(r => socket.once('data', x => r(x[0] === 83))) // S\n\n    if (!canSSL && ssl === 'prefer')\n      return connected()\n\n    socket.removeAllListeners()\n    socket = tls__WEBPACK_IMPORTED_MODULE_1__.connect({\n      socket,\n      servername: net__WEBPACK_IMPORTED_MODULE_0__.isIP(socket.host) ? undefined : socket.host,\n      ...(ssl === 'require' || ssl === 'allow' || ssl === 'prefer'\n        ? { rejectUnauthorized: false }\n        : ssl === 'verify-full'\n          ? {}\n          : typeof ssl === 'object'\n            ? ssl\n            : {}\n      )\n    })\n    socket.on('secureConnect', connected)\n    socket.on('error', error)\n    socket.on('close', closed)\n    socket.on('drain', drain)\n  }\n\n  /* c8 ignore next 3 */\n  function drain() {\n    !query && onopen(connection)\n  }\n\n  function data(x) {\n    if (incomings) {\n      incomings.push(x)\n      remaining -= x.length\n      if (remaining > 0)\n        return\n    }\n\n    incoming = incomings\n      ? Buffer.concat(incomings, length - remaining)\n      : incoming.length === 0\n        ? x\n        : Buffer.concat([incoming, x], incoming.length + x.length)\n\n    while (incoming.length > 4) {\n      length = incoming.readUInt32BE(1)\n      if (length >= incoming.length) {\n        remaining = length - incoming.length\n        incomings = [incoming]\n        break\n      }\n\n      try {\n        handle(incoming.subarray(0, length + 1))\n      } catch (e) {\n        query && (query.cursorFn || query.describeFirst) && write(Sync)\n        errored(e)\n      }\n      incoming = incoming.subarray(length + 1)\n      remaining = 0\n      incomings = null\n    }\n  }\n\n  async function connect() {\n    terminated = false\n    backendParameters = {}\n    socket || (socket = await createSocket())\n\n    if (!socket)\n      return\n\n    connectTimer.start()\n\n    if (options.socket)\n      return ssl ? secure() : connected()\n\n    socket.on('connect', ssl ? secure : connected)\n\n    if (options.path)\n      return socket.connect(options.path)\n\n    socket.ssl = ssl\n    socket.connect(port[hostIndex], host[hostIndex])\n    socket.host = host[hostIndex]\n    socket.port = port[hostIndex]\n\n    hostIndex = (hostIndex + 1) % port.length\n  }\n\n  function reconnect() {\n    setTimeout(connect, closedDate ? closedDate + delay - perf_hooks__WEBPACK_IMPORTED_MODULE_4__.performance.now() : 0)\n  }\n\n  function connected() {\n    try {\n      statements = {}\n      needsTypes = options.fetch_types\n      statementId = Math.random().toString(36).slice(2)\n      statementCount = 1\n      lifeTimer.start()\n      socket.on('data', data)\n      keep_alive && socket.setKeepAlive && socket.setKeepAlive(true, 1000 * keep_alive)\n      const s = StartupMessage()\n      write(s)\n    } catch (err) {\n      error(err)\n    }\n  }\n\n  function error(err) {\n    if (connection.queue === queues.connecting && options.host[retries + 1])\n      return\n\n    errored(err)\n    while (sent.length)\n      queryError(sent.shift(), err)\n  }\n\n  function errored(err) {\n    stream && (stream.destroy(err), stream = null)\n    query && queryError(query, err)\n    initial && (queryError(initial, err), initial = null)\n  }\n\n  function queryError(query, err) {\n    if (query.reserve)\n      return query.reject(err)\n\n    if (!err || typeof err !== 'object')\n      err = new Error(err)\n\n    'query' in err || 'parameters' in err || Object.defineProperties(err, {\n      stack: { value: err.stack + query.origin.replace(/.*\\n/, '\\n'), enumerable: options.debug },\n      query: { value: query.string, enumerable: options.debug },\n      parameters: { value: query.parameters, enumerable: options.debug },\n      args: { value: query.args, enumerable: options.debug },\n      types: { value: query.statement && query.statement.types, enumerable: options.debug }\n    })\n    query.reject(err)\n  }\n\n  function end() {\n    return ending || (\n      !connection.reserved && onend(connection),\n      !connection.reserved && !initial && !query && sent.length === 0\n        ? (terminate(), new Promise(r => socket && socket.readyState !== 'closed' ? socket.once('close', r) : r()))\n        : ending = new Promise(r => ended = r)\n    )\n  }\n\n  function terminate() {\n    terminated = true\n    if (stream || query || initial || sent.length)\n      error(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_DESTROYED', options))\n\n    clearImmediate(nextWriteTimer)\n    if (socket) {\n      socket.removeListener('data', data)\n      socket.removeListener('connect', connected)\n      socket.readyState === 'open' && socket.end((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().X().end())\n    }\n    ended && (ended(), ending = ended = null)\n  }\n\n  async function closed(hadError) {\n    incoming = Buffer.alloc(0)\n    remaining = 0\n    incomings = null\n    clearImmediate(nextWriteTimer)\n    socket.removeListener('data', data)\n    socket.removeListener('connect', connected)\n    idleTimer.cancel()\n    lifeTimer.cancel()\n    connectTimer.cancel()\n\n    socket.removeAllListeners()\n    socket = null\n\n    if (initial)\n      return reconnect()\n\n    !hadError && (query || sent.length) && error(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_CLOSED', options, socket))\n    closedDate = perf_hooks__WEBPACK_IMPORTED_MODULE_4__.performance.now()\n    hadError && options.shared.retries++\n    delay = (typeof backoff === 'function' ? backoff(options.shared.retries) : backoff) * 1000\n    onclose(connection, _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_CLOSED', options, socket))\n  }\n\n  /* Handlers */\n  function handle(xs, x = xs[0]) {\n    (\n      x === 68 ? DataRow :                   // D\n      x === 100 ? CopyData :                 // d\n      x === 65 ? NotificationResponse :      // A\n      x === 83 ? ParameterStatus :           // S\n      x === 90 ? ReadyForQuery :             // Z\n      x === 67 ? CommandComplete :           // C\n      x === 50 ? BindComplete :              // 2\n      x === 49 ? ParseComplete :             // 1\n      x === 116 ? ParameterDescription :     // t\n      x === 84 ? RowDescription :            // T\n      x === 82 ? Authentication :            // R\n      x === 110 ? NoData :                   // n\n      x === 75 ? BackendKeyData :            // K\n      x === 69 ? ErrorResponse :             // E\n      x === 115 ? PortalSuspended :          // s\n      x === 51 ? CloseComplete :             // 3\n      x === 71 ? CopyInResponse :            // G\n      x === 78 ? NoticeResponse :            // N\n      x === 72 ? CopyOutResponse :           // H\n      x === 99 ? CopyDone :                  // c\n      x === 73 ? EmptyQueryResponse :        // I\n      x === 86 ? FunctionCallResponse :      // V\n      x === 118 ? NegotiateProtocolVersion : // v\n      x === 87 ? CopyBothResponse :          // W\n      /* c8 ignore next */\n      UnknownMessage\n    )(xs)\n  }\n\n  function DataRow(x) {\n    let index = 7\n    let length\n    let column\n    let value\n\n    const row = query.isRaw ? new Array(query.statement.columns.length) : {}\n    for (let i = 0; i < query.statement.columns.length; i++) {\n      column = query.statement.columns[i]\n      length = x.readInt32BE(index)\n      index += 4\n\n      value = length === -1\n        ? null\n        : query.isRaw === true\n          ? x.subarray(index, index += length)\n          : column.parser === undefined\n            ? x.toString('utf8', index, index += length)\n            : column.parser.array === true\n              ? column.parser(x.toString('utf8', index + 1, index += length))\n              : column.parser(x.toString('utf8', index, index += length))\n\n      query.isRaw\n        ? (row[i] = query.isRaw === true\n          ? value\n          : transform.value.from ? transform.value.from(value, column) : value)\n        : (row[column.name] = transform.value.from ? transform.value.from(value, column) : value)\n    }\n\n    query.forEachFn\n      ? query.forEachFn(transform.row.from ? transform.row.from(row) : row, result)\n      : (result[rows++] = transform.row.from ? transform.row.from(row) : row)\n  }\n\n  function ParameterStatus(x) {\n    const [k, v] = x.toString('utf8', 5, x.length - 1).split(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N)\n    backendParameters[k] = v\n    if (options.parameters[k] !== v) {\n      options.parameters[k] = v\n      onparameter && onparameter(k, v)\n    }\n  }\n\n  function ReadyForQuery(x) {\n    query && query.options.simple && query.resolve(results || result)\n    query = results = null\n    result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]()\n    connectTimer.cancel()\n\n    if (initial) {\n      if (target_session_attrs) {\n        if (!backendParameters.in_hot_standby || !backendParameters.default_transaction_read_only)\n          return fetchState()\n        else if (tryNext(target_session_attrs, backendParameters))\n          return terminate()\n      }\n\n      if (needsTypes) {\n        initial.reserve && (initial = null)\n        return fetchArrayTypes()\n      }\n\n      initial && !initial.reserve && execute(initial)\n      options.shared.retries = retries = 0\n      initial = null\n      return\n    }\n\n    while (sent.length && (query = sent.shift()) && (query.active = true, query.cancelled))\n      Connection(options).cancel(query.state, query.cancelled.resolve, query.cancelled.reject)\n\n    if (query)\n      return // Consider opening if able and sent.length < 50\n\n    connection.reserved\n      ? !connection.reserved.release && x[5] === 73 // I\n        ? ending\n          ? terminate()\n          : (connection.reserved = null, onopen(connection))\n        : connection.reserved()\n      : ending\n        ? terminate()\n        : onopen(connection)\n  }\n\n  function CommandComplete(x) {\n    rows = 0\n\n    for (let i = x.length - 1; i > 0; i--) {\n      if (x[i] === 32 && x[i + 1] < 58 && result.count === null)\n        result.count = +x.toString('utf8', i + 1, x.length - 1)\n      if (x[i - 1] >= 65) {\n        result.command = x.toString('utf8', 5, i)\n        result.state = backend\n        break\n      }\n    }\n\n    final && (final(), final = null)\n\n    if (result.command === 'BEGIN' && max !== 1 && !connection.reserved)\n      return errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('UNSAFE_TRANSACTION', 'Only use sql.begin, sql.reserved or max: 1'))\n\n    if (query.options.simple)\n      return BindComplete()\n\n    if (query.cursorFn) {\n      result.count && query.cursorFn(result)\n      write(Sync)\n    }\n\n    query.resolve(result)\n  }\n\n  function ParseComplete() {\n    query.parsing = false\n  }\n\n  function BindComplete() {\n    !result.statement && (result.statement = query.statement)\n    result.columns = query.statement.columns\n  }\n\n  function ParameterDescription(x) {\n    const length = x.readUInt16BE(5)\n\n    for (let i = 0; i < length; ++i)\n      !query.statement.types[i] && (query.statement.types[i] = x.readUInt32BE(7 + i * 4))\n\n    query.prepare && (statements[query.signature] = query.statement)\n    query.describeFirst && !query.onlyDescribe && (write(prepared(query)), query.describeFirst = false)\n  }\n\n  function RowDescription(x) {\n    if (result.command) {\n      results = results || [result]\n      results.push(result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]())\n      result.count = null\n      query.statement.columns = null\n    }\n\n    const length = x.readUInt16BE(5)\n    let index = 7\n    let start\n\n    query.statement.columns = Array(length)\n\n    for (let i = 0; i < length; ++i) {\n      start = index\n      while (x[index++] !== 0);\n      const table = x.readUInt32BE(index)\n      const number = x.readUInt16BE(index + 4)\n      const type = x.readUInt32BE(index + 6)\n      query.statement.columns[i] = {\n        name: transform.column.from\n          ? transform.column.from(x.toString('utf8', start, index - 1))\n          : x.toString('utf8', start, index - 1),\n        parser: parsers[type],\n        table,\n        number,\n        type\n      }\n      index += 18\n    }\n\n    result.statement = query.statement\n    if (query.onlyDescribe)\n      return (query.resolve(query.statement), write(Sync))\n  }\n\n  async function Authentication(x, type = x.readUInt32BE(5)) {\n    (\n      type === 3 ? AuthenticationCleartextPassword :\n      type === 5 ? AuthenticationMD5Password :\n      type === 10 ? SASL :\n      type === 11 ? SASLContinue :\n      type === 12 ? SASLFinal :\n      type !== 0 ? UnknownAuth :\n      noop\n    )(x, type)\n  }\n\n  /* c8 ignore next 5 */\n  async function AuthenticationCleartextPassword() {\n    const payload = await Pass()\n    write(\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str(payload).z(1).end()\n    )\n  }\n\n  async function AuthenticationMD5Password(x) {\n    const payload = 'md5' + (\n      await md5(\n        Buffer.concat([\n          Buffer.from(await md5((await Pass()) + user)),\n          x.subarray(9)\n        ])\n      )\n    )\n    write(\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str(payload).z(1).end()\n    )\n  }\n\n  async function SASL() {\n    nonce = (await crypto__WEBPACK_IMPORTED_MODULE_2__.randomBytes(18)).toString('base64')\n    ;(0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str('SCRAM-SHA-256' + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N)\n    const i = _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i\n    write(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].inc(4).str('n,,n=*,r=' + nonce).i32(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i - i - 4, i).end())\n  }\n\n  async function SASLContinue(x) {\n    const res = x.toString('utf8', 9).split(',').reduce((acc, x) => (acc[x[0]] = x.slice(2), acc), {})\n\n    const saltedPassword = await crypto__WEBPACK_IMPORTED_MODULE_2__.pbkdf2Sync(\n      await Pass(),\n      Buffer.from(res.s, 'base64'),\n      parseInt(res.i), 32,\n      'sha256'\n    )\n\n    const clientKey = await hmac(saltedPassword, 'Client Key')\n\n    const auth = 'n=*,r=' + nonce + ','\n               + 'r=' + res.r + ',s=' + res.s + ',i=' + res.i\n               + ',c=biws,r=' + res.r\n\n    serverSignature = (await hmac(await hmac(saltedPassword, 'Server Key'), auth)).toString('base64')\n\n    const payload = 'c=biws,r=' + res.r + ',p=' + xor(\n      clientKey, Buffer.from(await hmac(await sha256(clientKey), auth))\n    ).toString('base64')\n\n    write(\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str(payload).end()\n    )\n  }\n\n  function SASLFinal(x) {\n    if (x.toString('utf8', 9).split(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N, 1)[0].slice(2) === serverSignature)\n      return\n    /* c8 ignore next 5 */\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('SASL_SIGNATURE_MISMATCH', 'The server did not return the correct signature'))\n    socket.destroy()\n  }\n\n  function Pass() {\n    return Promise.resolve(typeof options.pass === 'function'\n      ? options.pass()\n      : options.pass\n    )\n  }\n\n  function NoData() {\n    result.statement = query.statement\n    result.statement.columns = []\n    if (query.onlyDescribe)\n      return (query.resolve(query.statement), write(Sync))\n  }\n\n  function BackendKeyData(x) {\n    backend.pid = x.readUInt32BE(5)\n    backend.secret = x.readUInt32BE(9)\n  }\n\n  async function fetchArrayTypes() {\n    needsTypes = false\n    const types = await new _query_js__WEBPACK_IMPORTED_MODULE_9__.Query([`\n      select b.oid, b.typarray\n      from pg_catalog.pg_type a\n      left join pg_catalog.pg_type b on b.oid = a.typelem\n      where a.typcategory = 'A'\n      group by b.oid, b.typarray\n      order by b.oid\n    `], [], execute)\n    types.forEach(({ oid, typarray }) => addArrayType(oid, typarray))\n  }\n\n  function addArrayType(oid, typarray) {\n    if (!!options.parsers[typarray] && !!options.serializers[typarray]) return\n    const parser = options.parsers[oid]\n    options.shared.typeArrayMap[oid] = typarray\n    options.parsers[typarray] = (xs) => (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.arrayParser)(xs, parser, typarray)\n    options.parsers[typarray].array = true\n    options.serializers[typarray] = (xs) => (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.arraySerializer)(xs, options.serializers[oid], options, typarray)\n  }\n\n  function tryNext(x, xs) {\n    return (\n      (x === 'read-write' && xs.default_transaction_read_only === 'on') ||\n      (x === 'read-only' && xs.default_transaction_read_only === 'off') ||\n      (x === 'primary' && xs.in_hot_standby === 'on') ||\n      (x === 'standby' && xs.in_hot_standby === 'off') ||\n      (x === 'prefer-standby' && xs.in_hot_standby === 'off' && options.host[retries])\n    )\n  }\n\n  function fetchState() {\n    const query = new _query_js__WEBPACK_IMPORTED_MODULE_9__.Query([`\n      show transaction_read_only;\n      select pg_catalog.pg_is_in_recovery()\n    `], [], execute, null, { simple: true })\n    query.resolve = ([[a], [b]]) => {\n      backendParameters.default_transaction_read_only = a.transaction_read_only\n      backendParameters.in_hot_standby = b.pg_is_in_recovery ? 'on' : 'off'\n    }\n    query.execute()\n  }\n\n  function ErrorResponse(x) {\n    query && (query.cursorFn || query.describeFirst) && write(Sync)\n    const error = _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.postgres(parseError(x))\n    query && query.retried\n      ? errored(query.retried)\n      : query && query.prepared && retryRoutines.has(error.routine)\n        ? retry(query, error)\n        : errored(error)\n  }\n\n  function retry(q, error) {\n    delete statements[q.signature]\n    q.retried = error\n    execute(q)\n  }\n\n  function NotificationResponse(x) {\n    if (!onnotify)\n      return\n\n    let index = 9\n    while (x[index++] !== 0);\n    onnotify(\n      x.toString('utf8', 9, index - 1),\n      x.toString('utf8', index, x.length - 1)\n    )\n  }\n\n  async function PortalSuspended() {\n    try {\n      const x = await Promise.resolve(query.cursorFn(result))\n      rows = 0\n      x === _query_js__WEBPACK_IMPORTED_MODULE_9__.CLOSE\n        ? write(Close(query.portal))\n        : (result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"](), write(Execute('', query.cursorRows)))\n    } catch (err) {\n      write(Sync)\n      query.reject(err)\n    }\n  }\n\n  function CloseComplete() {\n    result.count && query.cursorFn(result)\n    query.resolve(result)\n  }\n\n  function CopyInResponse() {\n    stream = new stream__WEBPACK_IMPORTED_MODULE_3__.Writable({\n      autoDestroy: true,\n      write(chunk, encoding, callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().d().raw(chunk).end(), callback)\n      },\n      destroy(error, callback) {\n        callback(error)\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().f().str(error + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end())\n        stream = null\n      },\n      final(callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().c().end())\n        final = callback\n      }\n    })\n    query.resolve(stream)\n  }\n\n  function CopyOutResponse() {\n    stream = new stream__WEBPACK_IMPORTED_MODULE_3__.Readable({\n      read() { socket.resume() }\n    })\n    query.resolve(stream)\n  }\n\n  /* c8 ignore next 3 */\n  function CopyBothResponse() {\n    stream = new stream__WEBPACK_IMPORTED_MODULE_3__.Duplex({\n      autoDestroy: true,\n      read() { socket.resume() },\n      /* c8 ignore next 11 */\n      write(chunk, encoding, callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().d().raw(chunk).end(), callback)\n      },\n      destroy(error, callback) {\n        callback(error)\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().f().str(error + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end())\n        stream = null\n      },\n      final(callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().c().end())\n        final = callback\n      }\n    })\n    query.resolve(stream)\n  }\n\n  function CopyData(x) {\n    stream && (stream.push(x.subarray(5)) || socket.pause())\n  }\n\n  function CopyDone() {\n    stream && stream.push(null)\n    stream = null\n  }\n\n  function NoticeResponse(x) {\n    onnotice\n      ? onnotice(parseError(x))\n      : console.log(parseError(x)) // eslint-disable-line\n\n  }\n\n  /* c8 ignore next 3 */\n  function EmptyQueryResponse() {\n    /* noop */\n  }\n\n  /* c8 ignore next 3 */\n  function FunctionCallResponse() {\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.notSupported('FunctionCallResponse'))\n  }\n\n  /* c8 ignore next 3 */\n  function NegotiateProtocolVersion() {\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.notSupported('NegotiateProtocolVersion'))\n  }\n\n  /* c8 ignore next 3 */\n  function UnknownMessage(x) {\n    console.error('Postgres.js : Unknown Message:', x[0]) // eslint-disable-line\n  }\n\n  /* c8 ignore next 3 */\n  function UnknownAuth(x, type) {\n    console.error('Postgres.js : Unknown Auth:', type) // eslint-disable-line\n  }\n\n  /* Messages */\n  function Bind(parameters, types, statement = '', portal = '') {\n    let prev\n      , type\n\n    ;(0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().B().str(portal + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).str(statement + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i16(0).i16(parameters.length)\n\n    parameters.forEach((x, i) => {\n      if (x === null)\n        return _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i32(0xFFFFFFFF)\n\n      type = types[i]\n      parameters[i] = x = type in options.serializers\n        ? options.serializers[type](x)\n        : '' + x\n\n      prev = _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i\n      _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].inc(4).str(x).i32(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i - prev - 4, prev)\n    })\n\n    _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i16(0)\n\n    return _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].end()\n  }\n\n  function Parse(str, parameters, types, name = '') {\n    (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().P().str(name + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).str(str + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i16(parameters.length)\n    parameters.forEach((x, i) => _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i32(types[i] || 0))\n    return _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].end()\n  }\n\n  function Describe(x, name = '') {\n    return (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().D().str(x).str(name + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end()\n  }\n\n  function Execute(portal = '', rows = 0) {\n    return Buffer.concat([\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().E().str(portal + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i32(rows).end(),\n      Flush\n    ])\n  }\n\n  function Close(portal = '') {\n    return Buffer.concat([\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().C().str('P').str(portal + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end(),\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().S().end()\n    ])\n  }\n\n  function StartupMessage() {\n    return cancelMessage || (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().inc(4).i16(3).z(2).str(\n      Object.entries(Object.assign({\n        user,\n        database,\n        client_encoding: 'UTF8'\n      },\n        options.connection\n      )).filter(([, v]) => v).map(([k, v]) => k + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N + v).join(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N)\n    ).z(2).end(0)\n  }\n\n}\n\nfunction parseError(x) {\n  const error = {}\n  let start = 5\n  for (let i = 5; i < x.length - 1; i++) {\n    if (x[i] === 0) {\n      error[errorFields[x[start]]] = x.toString('utf8', start + 1, i)\n      start = i + 1\n    }\n  }\n  return error\n}\n\nfunction md5(x) {\n  return crypto__WEBPACK_IMPORTED_MODULE_2__.createHash('md5').update(x).digest('hex')\n}\n\nfunction hmac(key, x) {\n  return crypto__WEBPACK_IMPORTED_MODULE_2__.createHmac('sha256', key).update(x).digest()\n}\n\nfunction sha256(x) {\n  return crypto__WEBPACK_IMPORTED_MODULE_2__.createHash('sha256').update(x).digest()\n}\n\nfunction xor(a, b) {\n  const length = Math.max(a.length, b.length)\n  const buffer = Buffer.allocUnsafe(length)\n  for (let i = 0; i < length; i++)\n    buffer[i] = a[i] ^ b[i]\n  return buffer\n}\n\nfunction timer(fn, seconds) {\n  seconds = typeof seconds === 'function' ? seconds() : seconds\n  if (!seconds)\n    return { cancel: noop, start: noop }\n\n  let timer\n  return {\n    cancel() {\n      timer && (clearTimeout(timer), timer = null)\n    },\n    start() {\n      timer && clearTimeout(timer)\n      timer = setTimeout(done, seconds * 1000, arguments)\n    }\n  }\n\n  function done(args) {\n    fn.apply(null, args)\n    timer = null\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/connection.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/errors.js":
/*!*********************************************!*\
  !*** ./node_modules/postgres/src/errors.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Errors: () => (/* binding */ Errors),\n/* harmony export */   PostgresError: () => (/* binding */ PostgresError)\n/* harmony export */ });\nclass PostgresError extends Error {\n  constructor(x) {\n    super(x.message)\n    this.name = this.constructor.name\n    Object.assign(this, x)\n  }\n}\n\nconst Errors = {\n  connection,\n  postgres,\n  generic,\n  notSupported\n}\n\nfunction connection(x, options, socket) {\n  const { host, port } = socket || options\n  const error = Object.assign(\n    new Error(('write ' + x + ' ' + (options.path || (host + ':' + port)))),\n    {\n      code: x,\n      errno: x,\n      address: options.path || host\n    }, options.path ? {} : { port: port }\n  )\n  Error.captureStackTrace(error, connection)\n  return error\n}\n\nfunction postgres(x) {\n  const error = new PostgresError(x)\n  Error.captureStackTrace(error, postgres)\n  return error\n}\n\nfunction generic(code, message) {\n  const error = Object.assign(new Error(code + ': ' + message), { code })\n  Error.captureStackTrace(error, generic)\n  return error\n}\n\n/* c8 ignore next 10 */\nfunction notSupported(x) {\n  const error = Object.assign(\n    new Error(x + ' (B) is not supported'),\n    {\n      code: 'MESSAGE_NOT_SUPPORTED',\n      name: x\n    }\n  )\n  Error.captureStackTrace(error, notSupported)\n  return error\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/postgres/src/types.js\");\n/* harmony import */ var _connection_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./connection.js */ \"(rsc)/./node_modules/postgres/src/connection.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./query.js */ \"(rsc)/./node_modules/postgres/src/query.js\");\n/* harmony import */ var _queue_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queue.js */ \"(rsc)/./node_modules/postgres/src/queue.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/postgres/src/errors.js\");\n/* harmony import */ var _subscribe_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./subscribe.js */ \"(rsc)/./node_modules/postgres/src/subscribe.js\");\n/* harmony import */ var _large_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./large.js */ \"(rsc)/./node_modules/postgres/src/large.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nObject.assign(Postgres, {\n  PostgresError: _errors_js__WEBPACK_IMPORTED_MODULE_6__.PostgresError,\n  toPascal: _types_js__WEBPACK_IMPORTED_MODULE_2__.toPascal,\n  pascal: _types_js__WEBPACK_IMPORTED_MODULE_2__.pascal,\n  toCamel: _types_js__WEBPACK_IMPORTED_MODULE_2__.toCamel,\n  camel: _types_js__WEBPACK_IMPORTED_MODULE_2__.camel,\n  toKebab: _types_js__WEBPACK_IMPORTED_MODULE_2__.toKebab,\n  kebab: _types_js__WEBPACK_IMPORTED_MODULE_2__.kebab,\n  fromPascal: _types_js__WEBPACK_IMPORTED_MODULE_2__.fromPascal,\n  fromCamel: _types_js__WEBPACK_IMPORTED_MODULE_2__.fromCamel,\n  fromKebab: _types_js__WEBPACK_IMPORTED_MODULE_2__.fromKebab,\n  BigInt: {\n    to: 20,\n    from: [20],\n    parse: x => BigInt(x), // eslint-disable-line\n    serialize: x => x.toString()\n  }\n})\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Postgres);\n\nfunction Postgres(a, b) {\n  const options = parseOptions(a, b)\n      , subscribe = options.no_subscribe || (0,_subscribe_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(Postgres, { ...options })\n\n  let ending = false\n\n  const queries = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , connecting = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , reserved = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , closed = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , ended = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , open = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , busy = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , full = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , queues = { connecting, reserved, closed, ended, open, busy, full }\n\n  const connections = [...Array(options.max)].map(() => (0,_connection_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, queues, { onopen, onend, onclose }))\n\n  const sql = Sql(handler)\n\n  Object.assign(sql, {\n    get parameters() { return options.parameters },\n    largeObject: _large_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"].bind(null, sql),\n    subscribe,\n    CLOSE: _query_js__WEBPACK_IMPORTED_MODULE_4__.CLOSE,\n    END: _query_js__WEBPACK_IMPORTED_MODULE_4__.CLOSE,\n    PostgresError: _errors_js__WEBPACK_IMPORTED_MODULE_6__.PostgresError,\n    options,\n    reserve,\n    listen,\n    begin,\n    close,\n    end\n  })\n\n  return sql\n\n  function Sql(handler) {\n    handler.debug = options.debug\n\n    Object.entries(options.types).reduce((acc, [name, type]) => {\n      acc[name] = (x) => new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(x, type.to)\n      return acc\n    }, typed)\n\n    Object.assign(sql, {\n      types: typed,\n      typed,\n      unsafe,\n      notify,\n      array,\n      json,\n      file\n    })\n\n    return sql\n\n    function typed(value, type) {\n      return new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(value, type)\n    }\n\n    function sql(strings, ...args) {\n      const query = strings && Array.isArray(strings.raw)\n        ? new _query_js__WEBPACK_IMPORTED_MODULE_4__.Query(strings, args, handler, cancel)\n        : typeof strings === 'string' && !args.length\n          ? new _types_js__WEBPACK_IMPORTED_MODULE_2__.Identifier(options.transform.column.to ? options.transform.column.to(strings) : strings)\n          : new _types_js__WEBPACK_IMPORTED_MODULE_2__.Builder(strings, args)\n      return query\n    }\n\n    function unsafe(string, args = [], options = {}) {\n      arguments.length === 2 && !Array.isArray(args) && (options = args, args = [])\n      const query = new _query_js__WEBPACK_IMPORTED_MODULE_4__.Query([string], args, handler, cancel, {\n        prepare: false,\n        ...options,\n        simple: 'simple' in options ? options.simple : args.length === 0\n      })\n      return query\n    }\n\n    function file(path, args = [], options = {}) {\n      arguments.length === 2 && !Array.isArray(args) && (options = args, args = [])\n      const query = new _query_js__WEBPACK_IMPORTED_MODULE_4__.Query([], args, (query) => {\n        fs__WEBPACK_IMPORTED_MODULE_1__.readFile(path, 'utf8', (err, string) => {\n          if (err)\n            return query.reject(err)\n\n          query.strings = [string]\n          handler(query)\n        })\n      }, cancel, {\n        ...options,\n        simple: 'simple' in options ? options.simple : args.length === 0\n      })\n      return query\n    }\n  }\n\n  async function listen(name, fn, onlisten) {\n    const listener = { fn, onlisten }\n\n    const sql = listen.sql || (listen.sql = Postgres({\n      ...options,\n      max: 1,\n      idle_timeout: null,\n      max_lifetime: null,\n      fetch_types: false,\n      onclose() {\n        Object.entries(listen.channels).forEach(([name, { listeners }]) => {\n          delete listen.channels[name]\n          Promise.all(listeners.map(l => listen(name, l.fn, l.onlisten).catch(() => { /* noop */ })))\n        })\n      },\n      onnotify(c, x) {\n        c in listen.channels && listen.channels[c].listeners.forEach(l => l.fn(x))\n      }\n    }))\n\n    const channels = listen.channels || (listen.channels = {})\n        , exists = name in channels\n\n    if (exists) {\n      channels[name].listeners.push(listener)\n      const result = await channels[name].result\n      listener.onlisten && listener.onlisten()\n      return { state: result.state, unlisten }\n    }\n\n    channels[name] = { result: sql`listen ${\n      sql.unsafe('\"' + name.replace(/\"/g, '\"\"') + '\"')\n    }`, listeners: [listener] }\n    const result = await channels[name].result\n    listener.onlisten && listener.onlisten()\n    return { state: result.state, unlisten }\n\n    async function unlisten() {\n      if (name in channels === false)\n        return\n\n      channels[name].listeners = channels[name].listeners.filter(x => x !== listener)\n      if (channels[name].listeners.length)\n        return\n\n      delete channels[name]\n      return sql`unlisten ${\n        sql.unsafe('\"' + name.replace(/\"/g, '\"\"') + '\"')\n      }`\n    }\n  }\n\n  async function notify(channel, payload) {\n    return await sql`select pg_notify(${ channel }, ${ '' + payload })`\n  }\n\n  async function reserve() {\n    const queue = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n    const c = open.length\n      ? open.shift()\n      : await new Promise((resolve, reject) => {\n        const query = { reserve: resolve, reject }\n        queries.push(query)\n        closed.length && connect(closed.shift(), query)\n      })\n\n    move(c, reserved)\n    c.reserved = () => queue.length\n      ? c.execute(queue.shift())\n      : move(c, reserved)\n    c.reserved.release = true\n\n    const sql = Sql(handler)\n    sql.release = () => {\n      c.reserved = null\n      onopen(c)\n    }\n\n    return sql\n\n    function handler(q) {\n      c.queue === full\n        ? queue.push(q)\n        : c.execute(q) || move(c, full)\n    }\n  }\n\n  async function begin(options, fn) {\n    !fn && (fn = options, options = '')\n    const queries = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n    let savepoints = 0\n      , connection\n      , prepare = null\n\n    try {\n      await sql.unsafe('begin ' + options.replace(/[^a-z ]/ig, ''), [], { onexecute }).execute()\n      return await Promise.race([\n        scope(connection, fn),\n        new Promise((_, reject) => connection.onclose = reject)\n      ])\n    } catch (error) {\n      throw error\n    }\n\n    async function scope(c, fn, name) {\n      const sql = Sql(handler)\n      sql.savepoint = savepoint\n      sql.prepare = x => prepare = x.replace(/[^a-z0-9$-_. ]/gi)\n      let uncaughtError\n        , result\n\n      name && await sql`savepoint ${ sql(name) }`\n      try {\n        result = await new Promise((resolve, reject) => {\n          const x = fn(sql)\n          Promise.resolve(Array.isArray(x) ? Promise.all(x) : x).then(resolve, reject)\n        })\n\n        if (uncaughtError)\n          throw uncaughtError\n      } catch (e) {\n        await (name\n          ? sql`rollback to ${ sql(name) }`\n          : sql`rollback`\n        )\n        throw e instanceof _errors_js__WEBPACK_IMPORTED_MODULE_6__.PostgresError && e.code === '25P02' && uncaughtError || e\n      }\n\n      if (!name) {\n        prepare\n          ? await sql`prepare transaction '${ sql.unsafe(prepare) }'`\n          : await sql`commit`\n      }\n\n      return result\n\n      function savepoint(name, fn) {\n        if (name && Array.isArray(name.raw))\n          return savepoint(sql => sql.apply(sql, arguments))\n\n        arguments.length === 1 && (fn = name, name = null)\n        return scope(c, fn, 's' + savepoints++ + (name ? '_' + name : ''))\n      }\n\n      function handler(q) {\n        q.catch(e => uncaughtError || (uncaughtError = e))\n        c.queue === full\n          ? queries.push(q)\n          : c.execute(q) || move(c, full)\n      }\n    }\n\n    function onexecute(c) {\n      connection = c\n      move(c, reserved)\n      c.reserved = () => queries.length\n        ? c.execute(queries.shift())\n        : move(c, reserved)\n    }\n  }\n\n  function move(c, queue) {\n    c.queue.remove(c)\n    queue.push(c)\n    c.queue = queue\n    queue === open\n      ? c.idleTimer.start()\n      : c.idleTimer.cancel()\n    return c\n  }\n\n  function json(x) {\n    return new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(x, 3802)\n  }\n\n  function array(x, type) {\n    if (!Array.isArray(x))\n      return array(Array.from(arguments))\n\n    return new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(x, type || (x.length ? (0,_types_js__WEBPACK_IMPORTED_MODULE_2__.inferType)(x) || 25 : 0), options.shared.typeArrayMap)\n  }\n\n  function handler(query) {\n    if (ending)\n      return query.reject(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_ENDED', options, options))\n\n    if (open.length)\n      return go(open.shift(), query)\n\n    if (closed.length)\n      return connect(closed.shift(), query)\n\n    busy.length\n      ? go(busy.shift(), query)\n      : queries.push(query)\n  }\n\n  function go(c, query) {\n    return c.execute(query)\n      ? move(c, busy)\n      : move(c, full)\n  }\n\n  function cancel(query) {\n    return new Promise((resolve, reject) => {\n      query.state\n        ? query.active\n          ? (0,_connection_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options).cancel(query.state, resolve, reject)\n          : query.cancelled = { resolve, reject }\n        : (\n          queries.remove(query),\n          query.cancelled = true,\n          query.reject(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('57014', 'canceling statement due to user request')),\n          resolve()\n        )\n    })\n  }\n\n  async function end({ timeout = null } = {}) {\n    if (ending)\n      return ending\n\n    await 1\n    let timer\n    return ending = Promise.race([\n      new Promise(r => timeout !== null && (timer = setTimeout(destroy, timeout * 1000, r))),\n      Promise.all(connections.map(c => c.end()).concat(\n        listen.sql ? listen.sql.end({ timeout: 0 }) : [],\n        subscribe.sql ? subscribe.sql.end({ timeout: 0 }) : []\n      ))\n    ]).then(() => clearTimeout(timer))\n  }\n\n  async function close() {\n    await Promise.all(connections.map(c => c.end()))\n  }\n\n  async function destroy(resolve) {\n    await Promise.all(connections.map(c => c.terminate()))\n    while (queries.length)\n      queries.shift().reject(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_DESTROYED', options))\n    resolve()\n  }\n\n  function connect(c, query) {\n    move(c, connecting)\n    c.connect(query)\n    return c\n  }\n\n  function onend(c) {\n    move(c, ended)\n  }\n\n  function onopen(c) {\n    if (queries.length === 0)\n      return move(c, open)\n\n    let max = Math.ceil(queries.length / (connecting.length + 1))\n      , ready = true\n\n    while (ready && queries.length && max-- > 0) {\n      const query = queries.shift()\n      if (query.reserve)\n        return query.reserve(c)\n\n      ready = c.execute(query)\n    }\n\n    ready\n      ? move(c, busy)\n      : move(c, full)\n  }\n\n  function onclose(c, e) {\n    move(c, closed)\n    c.reserved = null\n    c.onclose && (c.onclose(e), c.onclose = null)\n    options.onclose && options.onclose(c.id)\n    queries.length && connect(c, queries.shift())\n  }\n}\n\nfunction parseOptions(a, b) {\n  if (a && a.shared)\n    return a\n\n  const env = process.env // eslint-disable-line\n      , o = (!a || typeof a === 'string' ? b : a) || {}\n      , { url, multihost } = parseUrl(a)\n      , query = [...url.searchParams].reduce((a, [b, c]) => (a[b] = c, a), {})\n      , host = o.hostname || o.host || multihost || url.hostname || env.PGHOST || 'localhost'\n      , port = o.port || url.port || env.PGPORT || 5432\n      , user = o.user || o.username || url.username || env.PGUSERNAME || env.PGUSER || osUsername()\n\n  o.no_prepare && (o.prepare = false)\n  query.sslmode && (query.ssl = query.sslmode, delete query.sslmode)\n  'timeout' in o && (console.log('The timeout option is deprecated, use idle_timeout instead'), o.idle_timeout = o.timeout) // eslint-disable-line\n  query.sslrootcert === 'system' && (query.ssl = 'verify-full')\n\n  const ints = ['idle_timeout', 'connect_timeout', 'max_lifetime', 'max_pipeline', 'backoff', 'keep_alive']\n  const defaults = {\n    max             : 10,\n    ssl             : false,\n    idle_timeout    : null,\n    connect_timeout : 30,\n    max_lifetime    : max_lifetime,\n    max_pipeline    : 100,\n    backoff         : backoff,\n    keep_alive      : 60,\n    prepare         : true,\n    debug           : false,\n    fetch_types     : true,\n    publications    : 'alltables',\n    target_session_attrs: null\n  }\n\n  return {\n    host            : Array.isArray(host) ? host : host.split(',').map(x => x.split(':')[0]),\n    port            : Array.isArray(port) ? port : host.split(',').map(x => parseInt(x.split(':')[1] || port)),\n    path            : o.path || host.indexOf('/') > -1 && host + '/.s.PGSQL.' + port,\n    database        : o.database || o.db || (url.pathname || '').slice(1) || env.PGDATABASE || user,\n    user            : user,\n    pass            : o.pass || o.password || url.password || env.PGPASSWORD || '',\n    ...Object.entries(defaults).reduce(\n      (acc, [k, d]) => {\n        const value = k in o ? o[k] : k in query\n          ? (query[k] === 'disable' || query[k] === 'false' ? false : query[k])\n          : env['PG' + k.toUpperCase()] || d\n        acc[k] = typeof value === 'string' && ints.includes(k)\n          ? +value\n          : value\n        return acc\n      },\n      {}\n    ),\n    connection      : {\n      application_name: env.PGAPPNAME || 'postgres.js',\n      ...o.connection,\n      ...Object.entries(query).reduce((acc, [k, v]) => (k in defaults || (acc[k] = v), acc), {})\n    },\n    types           : o.types || {},\n    target_session_attrs: tsa(o, url, env),\n    onnotice        : o.onnotice,\n    onnotify        : o.onnotify,\n    onclose         : o.onclose,\n    onparameter     : o.onparameter,\n    socket          : o.socket,\n    transform       : parseTransform(o.transform || { undefined: undefined }),\n    parameters      : {},\n    shared          : { retries: 0, typeArrayMap: {} },\n    ...(0,_types_js__WEBPACK_IMPORTED_MODULE_2__.mergeUserTypes)(o.types)\n  }\n}\n\nfunction tsa(o, url, env) {\n  const x = o.target_session_attrs || url.searchParams.get('target_session_attrs') || env.PGTARGETSESSIONATTRS\n  if (!x || ['read-write', 'read-only', 'primary', 'standby', 'prefer-standby'].includes(x))\n    return x\n\n  throw new Error('target_session_attrs ' + x + ' is not supported')\n}\n\nfunction backoff(retries) {\n  return (0.5 + Math.random() / 2) * Math.min(3 ** retries / 100, 20)\n}\n\nfunction max_lifetime() {\n  return 60 * (30 + Math.random() * 30)\n}\n\nfunction parseTransform(x) {\n  return {\n    undefined: x.undefined,\n    column: {\n      from: typeof x.column === 'function' ? x.column : x.column && x.column.from,\n      to: x.column && x.column.to\n    },\n    value: {\n      from: typeof x.value === 'function' ? x.value : x.value && x.value.from,\n      to: x.value && x.value.to\n    },\n    row: {\n      from: typeof x.row === 'function' ? x.row : x.row && x.row.from,\n      to: x.row && x.row.to\n    }\n  }\n}\n\nfunction parseUrl(url) {\n  if (!url || typeof url !== 'string')\n    return { url: { searchParams: new Map() } }\n\n  let host = url\n  host = host.slice(host.indexOf('://') + 3).split(/[?/]/)[0]\n  host = decodeURIComponent(host.slice(host.indexOf('@') + 1))\n\n  const urlObj = new URL(url.replace(host, host.split(',')[0]))\n\n  return {\n    url: {\n      username: decodeURIComponent(urlObj.username),\n      password: decodeURIComponent(urlObj.password),\n      host: urlObj.host,\n      hostname: urlObj.hostname,\n      port: urlObj.port,\n      pathname: urlObj.pathname,\n      searchParams: urlObj.searchParams\n    },\n    multihost: host.indexOf(',') > -1 && host\n  }\n}\n\nfunction osUsername() {\n  try {\n    return os__WEBPACK_IMPORTED_MODULE_0__.userInfo().username // eslint-disable-line\n  } catch (_) {\n    return process.env.USERNAME || process.env.USER || process.env.LOGNAME  // eslint-disable-line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/large.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/large.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ largeObject)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n\n\nfunction largeObject(sql, oid, mode = 0x00020000 | 0x00040000) {\n  return new Promise(async(resolve, reject) => {\n    await sql.begin(async sql => {\n      let finish\n      !oid && ([{ oid }] = await sql`select lo_creat(-1) as oid`)\n      const [{ fd }] = await sql`select lo_open(${ oid }, ${ mode }) as fd`\n\n      const lo = {\n        writable,\n        readable,\n        close     : () => sql`select lo_close(${ fd })`.then(finish),\n        tell      : () => sql`select lo_tell64(${ fd })`,\n        read      : (x) => sql`select loread(${ fd }, ${ x }) as data`,\n        write     : (x) => sql`select lowrite(${ fd }, ${ x })`,\n        truncate  : (x) => sql`select lo_truncate64(${ fd }, ${ x })`,\n        seek      : (x, whence = 0) => sql`select lo_lseek64(${ fd }, ${ x }, ${ whence })`,\n        size      : () => sql`\n          select\n            lo_lseek64(${ fd }, location, 0) as position,\n            seek.size\n          from (\n            select\n              lo_lseek64($1, 0, 2) as size,\n              tell.location\n            from (select lo_tell64($1) as location) tell\n          ) seek\n        `\n      }\n\n      resolve(lo)\n\n      return new Promise(async r => finish = r)\n\n      async function readable({\n        highWaterMark = 2048 * 8,\n        start = 0,\n        end = Infinity\n      } = {}) {\n        let max = end - start\n        start && await lo.seek(start)\n        return new stream__WEBPACK_IMPORTED_MODULE_0__.Readable({\n          highWaterMark,\n          async read(size) {\n            const l = size > max ? size - max : size\n            max -= size\n            const [{ data }] = await lo.read(l)\n            this.push(data)\n            if (data.length < size)\n              this.push(null)\n          }\n        })\n      }\n\n      async function writable({\n        highWaterMark = 2048 * 8,\n        start = 0\n      } = {}) {\n        start && await lo.seek(start)\n        return new stream__WEBPACK_IMPORTED_MODULE_0__.Writable({\n          highWaterMark,\n          write(chunk, encoding, callback) {\n            lo.write(chunk).then(() => callback(), callback)\n          }\n        })\n      }\n    }).catch(reject)\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL2xhcmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJCOztBQUVaO0FBQ2Y7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLEtBQUs7QUFDdkIsZUFBZSxJQUFJLGdDQUFnQyxLQUFLLEtBQUssTUFBTTs7QUFFbkU7QUFDQTtBQUNBO0FBQ0EsaURBQWlELElBQUk7QUFDckQsa0RBQWtELElBQUk7QUFDdEQsZ0RBQWdELElBQUksS0FBSyxHQUFHO0FBQzVELGlEQUFpRCxJQUFJLEtBQUssR0FBRztBQUM3RCx1REFBdUQsSUFBSSxLQUFLLEdBQUc7QUFDbkUsZ0VBQWdFLElBQUksS0FBSyxHQUFHLEtBQUssUUFBUTtBQUN6RjtBQUNBO0FBQ0EsMEJBQTBCLElBQUk7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxJQUFJO0FBQ1o7QUFDQTtBQUNBLG1CQUFtQiw0Q0FBZTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixNQUFNO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsSUFBSTtBQUNaO0FBQ0EsbUJBQW1CLDRDQUFlO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL2xhcmdlLmpzP2IxODMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFN0cmVhbSBmcm9tICdzdHJlYW0nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGxhcmdlT2JqZWN0KHNxbCwgb2lkLCBtb2RlID0gMHgwMDAyMDAwMCB8IDB4MDAwNDAwMDApIHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKGFzeW5jKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIGF3YWl0IHNxbC5iZWdpbihhc3luYyBzcWwgPT4ge1xuICAgICAgbGV0IGZpbmlzaFxuICAgICAgIW9pZCAmJiAoW3sgb2lkIH1dID0gYXdhaXQgc3FsYHNlbGVjdCBsb19jcmVhdCgtMSkgYXMgb2lkYClcbiAgICAgIGNvbnN0IFt7IGZkIH1dID0gYXdhaXQgc3FsYHNlbGVjdCBsb19vcGVuKCR7IG9pZCB9LCAkeyBtb2RlIH0pIGFzIGZkYFxuXG4gICAgICBjb25zdCBsbyA9IHtcbiAgICAgICAgd3JpdGFibGUsXG4gICAgICAgIHJlYWRhYmxlLFxuICAgICAgICBjbG9zZSAgICAgOiAoKSA9PiBzcWxgc2VsZWN0IGxvX2Nsb3NlKCR7IGZkIH0pYC50aGVuKGZpbmlzaCksXG4gICAgICAgIHRlbGwgICAgICA6ICgpID0+IHNxbGBzZWxlY3QgbG9fdGVsbDY0KCR7IGZkIH0pYCxcbiAgICAgICAgcmVhZCAgICAgIDogKHgpID0+IHNxbGBzZWxlY3QgbG9yZWFkKCR7IGZkIH0sICR7IHggfSkgYXMgZGF0YWAsXG4gICAgICAgIHdyaXRlICAgICA6ICh4KSA9PiBzcWxgc2VsZWN0IGxvd3JpdGUoJHsgZmQgfSwgJHsgeCB9KWAsXG4gICAgICAgIHRydW5jYXRlICA6ICh4KSA9PiBzcWxgc2VsZWN0IGxvX3RydW5jYXRlNjQoJHsgZmQgfSwgJHsgeCB9KWAsXG4gICAgICAgIHNlZWsgICAgICA6ICh4LCB3aGVuY2UgPSAwKSA9PiBzcWxgc2VsZWN0IGxvX2xzZWVrNjQoJHsgZmQgfSwgJHsgeCB9LCAkeyB3aGVuY2UgfSlgLFxuICAgICAgICBzaXplICAgICAgOiAoKSA9PiBzcWxgXG4gICAgICAgICAgc2VsZWN0XG4gICAgICAgICAgICBsb19sc2VlazY0KCR7IGZkIH0sIGxvY2F0aW9uLCAwKSBhcyBwb3NpdGlvbixcbiAgICAgICAgICAgIHNlZWsuc2l6ZVxuICAgICAgICAgIGZyb20gKFxuICAgICAgICAgICAgc2VsZWN0XG4gICAgICAgICAgICAgIGxvX2xzZWVrNjQoJDEsIDAsIDIpIGFzIHNpemUsXG4gICAgICAgICAgICAgIHRlbGwubG9jYXRpb25cbiAgICAgICAgICAgIGZyb20gKHNlbGVjdCBsb190ZWxsNjQoJDEpIGFzIGxvY2F0aW9uKSB0ZWxsXG4gICAgICAgICAgKSBzZWVrXG4gICAgICAgIGBcbiAgICAgIH1cblxuICAgICAgcmVzb2x2ZShsbylcblxuICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGFzeW5jIHIgPT4gZmluaXNoID0gcilcblxuICAgICAgYXN5bmMgZnVuY3Rpb24gcmVhZGFibGUoe1xuICAgICAgICBoaWdoV2F0ZXJNYXJrID0gMjA0OCAqIDgsXG4gICAgICAgIHN0YXJ0ID0gMCxcbiAgICAgICAgZW5kID0gSW5maW5pdHlcbiAgICAgIH0gPSB7fSkge1xuICAgICAgICBsZXQgbWF4ID0gZW5kIC0gc3RhcnRcbiAgICAgICAgc3RhcnQgJiYgYXdhaXQgbG8uc2VlayhzdGFydClcbiAgICAgICAgcmV0dXJuIG5ldyBTdHJlYW0uUmVhZGFibGUoe1xuICAgICAgICAgIGhpZ2hXYXRlck1hcmssXG4gICAgICAgICAgYXN5bmMgcmVhZChzaXplKSB7XG4gICAgICAgICAgICBjb25zdCBsID0gc2l6ZSA+IG1heCA/IHNpemUgLSBtYXggOiBzaXplXG4gICAgICAgICAgICBtYXggLT0gc2l6ZVxuICAgICAgICAgICAgY29uc3QgW3sgZGF0YSB9XSA9IGF3YWl0IGxvLnJlYWQobClcbiAgICAgICAgICAgIHRoaXMucHVzaChkYXRhKVxuICAgICAgICAgICAgaWYgKGRhdGEubGVuZ3RoIDwgc2l6ZSlcbiAgICAgICAgICAgICAgdGhpcy5wdXNoKG51bGwpXG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgfVxuXG4gICAgICBhc3luYyBmdW5jdGlvbiB3cml0YWJsZSh7XG4gICAgICAgIGhpZ2hXYXRlck1hcmsgPSAyMDQ4ICogOCxcbiAgICAgICAgc3RhcnQgPSAwXG4gICAgICB9ID0ge30pIHtcbiAgICAgICAgc3RhcnQgJiYgYXdhaXQgbG8uc2VlayhzdGFydClcbiAgICAgICAgcmV0dXJuIG5ldyBTdHJlYW0uV3JpdGFibGUoe1xuICAgICAgICAgIGhpZ2hXYXRlck1hcmssXG4gICAgICAgICAgd3JpdGUoY2h1bmssIGVuY29kaW5nLCBjYWxsYmFjaykge1xuICAgICAgICAgICAgbG8ud3JpdGUoY2h1bmspLnRoZW4oKCkgPT4gY2FsbGJhY2soKSwgY2FsbGJhY2spXG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgfVxuICAgIH0pLmNhdGNoKHJlamVjdClcbiAgfSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/large.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/query.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/query.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLOSE: () => (/* binding */ CLOSE),\n/* harmony export */   Query: () => (/* binding */ Query)\n/* harmony export */ });\nconst originCache = new Map()\n    , originStackCache = new Map()\n    , originError = Symbol('OriginError')\n\nconst CLOSE = {}\nclass Query extends Promise {\n  constructor(strings, args, handler, canceller, options = {}) {\n    let resolve\n      , reject\n\n    super((a, b) => {\n      resolve = a\n      reject = b\n    })\n\n    this.tagged = Array.isArray(strings.raw)\n    this.strings = strings\n    this.args = args\n    this.handler = handler\n    this.canceller = canceller\n    this.options = options\n\n    this.state = null\n    this.statement = null\n\n    this.resolve = x => (this.active = false, resolve(x))\n    this.reject = x => (this.active = false, reject(x))\n\n    this.active = false\n    this.cancelled = null\n    this.executed = false\n    this.signature = ''\n\n    this[originError] = this.handler.debug\n      ? new Error()\n      : this.tagged && cachedError(this.strings)\n  }\n\n  get origin() {\n    return (this.handler.debug\n      ? this[originError].stack\n      : this.tagged && originStackCache.has(this.strings)\n        ? originStackCache.get(this.strings)\n        : originStackCache.set(this.strings, this[originError].stack).get(this.strings)\n    ) || ''\n  }\n\n  static get [Symbol.species]() {\n    return Promise\n  }\n\n  cancel() {\n    return this.canceller && (this.canceller(this), this.canceller = null)\n  }\n\n  simple() {\n    this.options.simple = true\n    this.options.prepare = false\n    return this\n  }\n\n  async readable() {\n    this.simple()\n    this.streaming = true\n    return this\n  }\n\n  async writable() {\n    this.simple()\n    this.streaming = true\n    return this\n  }\n\n  cursor(rows = 1, fn) {\n    this.options.simple = false\n    if (typeof rows === 'function') {\n      fn = rows\n      rows = 1\n    }\n\n    this.cursorRows = rows\n\n    if (typeof fn === 'function')\n      return (this.cursorFn = fn, this)\n\n    let prev\n    return {\n      [Symbol.asyncIterator]: () => ({\n        next: () => {\n          if (this.executed && !this.active)\n            return { done: true }\n\n          prev && prev()\n          const promise = new Promise((resolve, reject) => {\n            this.cursorFn = value => {\n              resolve({ value, done: false })\n              return new Promise(r => prev = r)\n            }\n            this.resolve = () => (this.active = false, resolve({ done: true }))\n            this.reject = x => (this.active = false, reject(x))\n          })\n          this.execute()\n          return promise\n        },\n        return() {\n          prev && prev(CLOSE)\n          return { done: true }\n        }\n      })\n    }\n  }\n\n  describe() {\n    this.options.simple = false\n    this.onlyDescribe = this.options.prepare = true\n    return this\n  }\n\n  stream() {\n    throw new Error('.stream has been renamed to .forEach')\n  }\n\n  forEach(fn) {\n    this.forEachFn = fn\n    this.handle()\n    return this\n  }\n\n  raw() {\n    this.isRaw = true\n    return this\n  }\n\n  values() {\n    this.isRaw = 'values'\n    return this\n  }\n\n  async handle() {\n    !this.executed && (this.executed = true) && await 1 && this.handler(this)\n  }\n\n  execute() {\n    this.handle()\n    return this\n  }\n\n  then() {\n    this.handle()\n    return super.then.apply(this, arguments)\n  }\n\n  catch() {\n    this.handle()\n    return super.catch.apply(this, arguments)\n  }\n\n  finally() {\n    this.handle()\n    return super.finally.apply(this, arguments)\n  }\n}\n\nfunction cachedError(xs) {\n  if (originCache.has(xs))\n    return originCache.get(xs)\n\n  const x = Error.stackTraceLimit\n  Error.stackTraceLimit = 4\n  originCache.set(xs, new Error())\n  Error.stackTraceLimit = x\n  return originCache.get(xs)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/query.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/queue.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/queue.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Queue);\n\nfunction Queue(initial = []) {\n  let xs = initial.slice()\n  let index = 0\n\n  return {\n    get length() {\n      return xs.length - index\n    },\n    remove: (x) => {\n      const index = xs.indexOf(x)\n      return index === -1\n        ? null\n        : (xs.splice(index, 1), x)\n    },\n    push: (x) => (xs.push(x), x),\n    shift: () => {\n      const out = xs[index++]\n\n      if (index === xs.length) {\n        index = 0\n        xs = []\n      } else {\n        xs[index - 1] = undefined\n      }\n\n      return out\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL3F1ZXVlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxLQUFLOztBQUVwQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9wb3N0Z3Jlcy9zcmMvcXVldWUuanM/Njg1YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBRdWV1ZVxuXG5mdW5jdGlvbiBRdWV1ZShpbml0aWFsID0gW10pIHtcbiAgbGV0IHhzID0gaW5pdGlhbC5zbGljZSgpXG4gIGxldCBpbmRleCA9IDBcblxuICByZXR1cm4ge1xuICAgIGdldCBsZW5ndGgoKSB7XG4gICAgICByZXR1cm4geHMubGVuZ3RoIC0gaW5kZXhcbiAgICB9LFxuICAgIHJlbW92ZTogKHgpID0+IHtcbiAgICAgIGNvbnN0IGluZGV4ID0geHMuaW5kZXhPZih4KVxuICAgICAgcmV0dXJuIGluZGV4ID09PSAtMVxuICAgICAgICA/IG51bGxcbiAgICAgICAgOiAoeHMuc3BsaWNlKGluZGV4LCAxKSwgeClcbiAgICB9LFxuICAgIHB1c2g6ICh4KSA9PiAoeHMucHVzaCh4KSwgeCksXG4gICAgc2hpZnQ6ICgpID0+IHtcbiAgICAgIGNvbnN0IG91dCA9IHhzW2luZGV4KytdXG5cbiAgICAgIGlmIChpbmRleCA9PT0geHMubGVuZ3RoKSB7XG4gICAgICAgIGluZGV4ID0gMFxuICAgICAgICB4cyA9IFtdXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB4c1tpbmRleCAtIDFdID0gdW5kZWZpbmVkXG4gICAgICB9XG5cbiAgICAgIHJldHVybiBvdXRcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/result.js":
/*!*********************************************!*\
  !*** ./node_modules/postgres/src/result.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Result)\n/* harmony export */ });\nclass Result extends Array {\n  constructor() {\n    super()\n    Object.defineProperties(this, {\n      count: { value: null, writable: true },\n      state: { value: null, writable: true },\n      command: { value: null, writable: true },\n      columns: { value: null, writable: true },\n      statement: { value: null, writable: true }\n    })\n  }\n\n  static get [Symbol.species]() {\n    return Array\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL3Jlc3VsdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxlQUFlLDZCQUE2QjtBQUM1QyxlQUFlLDZCQUE2QjtBQUM1QyxpQkFBaUIsNkJBQTZCO0FBQzlDLGlCQUFpQiw2QkFBNkI7QUFDOUMsbUJBQW1CO0FBQ25CLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL3Jlc3VsdC5qcz84NjBmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGNsYXNzIFJlc3VsdCBleHRlbmRzIEFycmF5IHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgc3VwZXIoKVxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKHRoaXMsIHtcbiAgICAgIGNvdW50OiB7IHZhbHVlOiBudWxsLCB3cml0YWJsZTogdHJ1ZSB9LFxuICAgICAgc3RhdGU6IHsgdmFsdWU6IG51bGwsIHdyaXRhYmxlOiB0cnVlIH0sXG4gICAgICBjb21tYW5kOiB7IHZhbHVlOiBudWxsLCB3cml0YWJsZTogdHJ1ZSB9LFxuICAgICAgY29sdW1uczogeyB2YWx1ZTogbnVsbCwgd3JpdGFibGU6IHRydWUgfSxcbiAgICAgIHN0YXRlbWVudDogeyB2YWx1ZTogbnVsbCwgd3JpdGFibGU6IHRydWUgfVxuICAgIH0pXG4gIH1cblxuICBzdGF0aWMgZ2V0IFtTeW1ib2wuc3BlY2llc10oKSB7XG4gICAgcmV0dXJuIEFycmF5XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/result.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/subscribe.js":
/*!************************************************!*\
  !*** ./node_modules/postgres/src/subscribe.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Subscribe)\n/* harmony export */ });\nconst noop = () => { /* noop */ }\n\nfunction Subscribe(postgres, options) {\n  const subscribers = new Map()\n      , slot = 'postgresjs_' + Math.random().toString(36).slice(2)\n      , state = {}\n\n  let connection\n    , stream\n    , ended = false\n\n  const sql = subscribe.sql = postgres({\n    ...options,\n    transform: { column: {}, value: {}, row: {} },\n    max: 1,\n    fetch_types: false,\n    idle_timeout: null,\n    max_lifetime: null,\n    connection: {\n      ...options.connection,\n      replication: 'database'\n    },\n    onclose: async function() {\n      if (ended)\n        return\n      stream = null\n      state.pid = state.secret = undefined\n      connected(await init(sql, slot, options.publications))\n      subscribers.forEach(event => event.forEach(({ onsubscribe }) => onsubscribe()))\n    },\n    no_subscribe: true\n  })\n\n  const end = sql.end\n      , close = sql.close\n\n  sql.end = async() => {\n    ended = true\n    stream && (await new Promise(r => (stream.once('close', r), stream.end())))\n    return end()\n  }\n\n  sql.close = async() => {\n    stream && (await new Promise(r => (stream.once('close', r), stream.end())))\n    return close()\n  }\n\n  return subscribe\n\n  async function subscribe(event, fn, onsubscribe = noop, onerror = noop) {\n    event = parseEvent(event)\n\n    if (!connection)\n      connection = init(sql, slot, options.publications)\n\n    const subscriber = { fn, onsubscribe }\n    const fns = subscribers.has(event)\n      ? subscribers.get(event).add(subscriber)\n      : subscribers.set(event, new Set([subscriber])).get(event)\n\n    const unsubscribe = () => {\n      fns.delete(subscriber)\n      fns.size === 0 && subscribers.delete(event)\n    }\n\n    return connection.then(x => {\n      connected(x)\n      onsubscribe()\n      stream && stream.on('error', onerror)\n      return { unsubscribe, state, sql }\n    })\n  }\n\n  function connected(x) {\n    stream = x.stream\n    state.pid = x.state.pid\n    state.secret = x.state.secret\n  }\n\n  async function init(sql, slot, publications) {\n    if (!publications)\n      throw new Error('Missing publication names')\n\n    const xs = await sql.unsafe(\n      `CREATE_REPLICATION_SLOT ${ slot } TEMPORARY LOGICAL pgoutput NOEXPORT_SNAPSHOT`\n    )\n\n    const [x] = xs\n\n    const stream = await sql.unsafe(\n      `START_REPLICATION SLOT ${ slot } LOGICAL ${\n        x.consistent_point\n      } (proto_version '1', publication_names '${ publications }')`\n    ).writable()\n\n    const state = {\n      lsn: Buffer.concat(x.consistent_point.split('/').map(x => Buffer.from(('00000000' + x).slice(-8), 'hex')))\n    }\n\n    stream.on('data', data)\n    stream.on('error', error)\n    stream.on('close', sql.close)\n\n    return { stream, state: xs.state }\n\n    function error(e) {\n      console.error('Unexpected error during logical streaming - reconnecting', e) // eslint-disable-line\n    }\n\n    function data(x) {\n      if (x[0] === 0x77) {\n        parse(x.subarray(25), state, sql.options.parsers, handle, options.transform)\n      } else if (x[0] === 0x6b && x[17]) {\n        state.lsn = x.subarray(1, 9)\n        pong()\n      }\n    }\n\n    function handle(a, b) {\n      const path = b.relation.schema + '.' + b.relation.table\n      call('*', a, b)\n      call('*:' + path, a, b)\n      b.relation.keys.length && call('*:' + path + '=' + b.relation.keys.map(x => a[x.name]), a, b)\n      call(b.command, a, b)\n      call(b.command + ':' + path, a, b)\n      b.relation.keys.length && call(b.command + ':' + path + '=' + b.relation.keys.map(x => a[x.name]), a, b)\n    }\n\n    function pong() {\n      const x = Buffer.alloc(34)\n      x[0] = 'r'.charCodeAt(0)\n      x.fill(state.lsn, 1)\n      x.writeBigInt64BE(BigInt(Date.now() - Date.UTC(2000, 0, 1)) * BigInt(1000), 25)\n      stream.write(x)\n    }\n  }\n\n  function call(x, a, b) {\n    subscribers.has(x) && subscribers.get(x).forEach(({ fn }) => fn(a, b, x))\n  }\n}\n\nfunction Time(x) {\n  return new Date(Date.UTC(2000, 0, 1) + Number(x / BigInt(1000)))\n}\n\nfunction parse(x, state, parsers, handle, transform) {\n  const char = (acc, [k, v]) => (acc[k.charCodeAt(0)] = v, acc)\n\n  Object.entries({\n    R: x => {  // Relation\n      let i = 1\n      const r = state[x.readUInt32BE(i)] = {\n        schema: x.toString('utf8', i += 4, i = x.indexOf(0, i)) || 'pg_catalog',\n        table: x.toString('utf8', i + 1, i = x.indexOf(0, i + 1)),\n        columns: Array(x.readUInt16BE(i += 2)),\n        keys: []\n      }\n      i += 2\n\n      let columnIndex = 0\n        , column\n\n      while (i < x.length) {\n        column = r.columns[columnIndex++] = {\n          key: x[i++],\n          name: transform.column.from\n            ? transform.column.from(x.toString('utf8', i, i = x.indexOf(0, i)))\n            : x.toString('utf8', i, i = x.indexOf(0, i)),\n          type: x.readUInt32BE(i += 1),\n          parser: parsers[x.readUInt32BE(i)],\n          atttypmod: x.readUInt32BE(i += 4)\n        }\n\n        column.key && r.keys.push(column)\n        i += 4\n      }\n    },\n    Y: () => { /* noop */ }, // Type\n    O: () => { /* noop */ }, // Origin\n    B: x => { // Begin\n      state.date = Time(x.readBigInt64BE(9))\n      state.lsn = x.subarray(1, 9)\n    },\n    I: x => { // Insert\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      const { row } = tuples(x, relation.columns, i += 7, transform)\n\n      handle(row, {\n        command: 'insert',\n        relation\n      })\n    },\n    D: x => { // Delete\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      i += 4\n      const key = x[i] === 75\n      handle(key || x[i] === 79\n        ? tuples(x, relation.columns, i += 3, transform).row\n        : null\n      , {\n        command: 'delete',\n        relation,\n        key\n      })\n    },\n    U: x => { // Update\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      i += 4\n      const key = x[i] === 75\n      const xs = key || x[i] === 79\n        ? tuples(x, relation.columns, i += 3, transform)\n        : null\n\n      xs && (i = xs.i)\n\n      const { row } = tuples(x, relation.columns, i + 3, transform)\n\n      handle(row, {\n        command: 'update',\n        relation,\n        key,\n        old: xs && xs.row\n      })\n    },\n    T: () => { /* noop */ }, // Truncate,\n    C: () => { /* noop */ }  // Commit\n  }).reduce(char, {})[x[0]](x)\n}\n\nfunction tuples(x, columns, xi, transform) {\n  let type\n    , column\n    , value\n\n  const row = transform.raw ? new Array(columns.length) : {}\n  for (let i = 0; i < columns.length; i++) {\n    type = x[xi++]\n    column = columns[i]\n    value = type === 110 // n\n      ? null\n      : type === 117 // u\n        ? undefined\n        : column.parser === undefined\n          ? x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi))\n          : column.parser.array === true\n            ? column.parser(x.toString('utf8', xi + 5, xi += 4 + x.readUInt32BE(xi)))\n            : column.parser(x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi)))\n\n    transform.raw\n      ? (row[i] = transform.raw === true\n        ? value\n        : transform.value.from ? transform.value.from(value, column) : value)\n      : (row[column.name] = transform.value.from\n        ? transform.value.from(value, column)\n        : value\n      )\n  }\n\n  return { i: xi, row: transform.row.from ? transform.row.from(row) : row }\n}\n\nfunction parseEvent(x) {\n  const xs = x.match(/^(\\*|insert|update|delete)?:?([^.]+?\\.?[^=]+)?=?(.+)?/i) || []\n\n  if (!xs)\n    throw new Error('Malformed subscribe pattern: ' + x)\n\n  const [, command, path, key] = xs\n\n  return (command || '*')\n       + (path ? ':' + (path.indexOf('.') === -1 ? 'public.' + path : path) : '')\n       + (key ? '=' + key : '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/subscribe.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/types.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/types.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Builder: () => (/* binding */ Builder),\n/* harmony export */   END: () => (/* binding */ END),\n/* harmony export */   Identifier: () => (/* binding */ Identifier),\n/* harmony export */   Parameter: () => (/* binding */ Parameter),\n/* harmony export */   arrayParser: () => (/* binding */ arrayParser),\n/* harmony export */   arraySerializer: () => (/* binding */ arraySerializer),\n/* harmony export */   camel: () => (/* binding */ camel),\n/* harmony export */   escapeIdentifier: () => (/* binding */ escapeIdentifier),\n/* harmony export */   fromCamel: () => (/* binding */ fromCamel),\n/* harmony export */   fromKebab: () => (/* binding */ fromKebab),\n/* harmony export */   fromPascal: () => (/* binding */ fromPascal),\n/* harmony export */   handleValue: () => (/* binding */ handleValue),\n/* harmony export */   inferType: () => (/* binding */ inferType),\n/* harmony export */   kebab: () => (/* binding */ kebab),\n/* harmony export */   mergeUserTypes: () => (/* binding */ mergeUserTypes),\n/* harmony export */   parsers: () => (/* binding */ parsers),\n/* harmony export */   pascal: () => (/* binding */ pascal),\n/* harmony export */   serializers: () => (/* binding */ serializers),\n/* harmony export */   stringify: () => (/* binding */ stringify),\n/* harmony export */   toCamel: () => (/* binding */ toCamel),\n/* harmony export */   toKebab: () => (/* binding */ toKebab),\n/* harmony export */   toPascal: () => (/* binding */ toPascal),\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./query.js */ \"(rsc)/./node_modules/postgres/src/query.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/postgres/src/errors.js\");\n\n\n\nconst types = {\n  string: {\n    to: 25,\n    from: null,             // defaults to string\n    serialize: x => '' + x\n  },\n  number: {\n    to: 0,\n    from: [21, 23, 26, 700, 701],\n    serialize: x => '' + x,\n    parse: x => +x\n  },\n  json: {\n    to: 114,\n    from: [114, 3802],\n    serialize: x => JSON.stringify(x),\n    parse: x => JSON.parse(x)\n  },\n  boolean: {\n    to: 16,\n    from: 16,\n    serialize: x => x === true ? 't' : 'f',\n    parse: x => x === 't'\n  },\n  date: {\n    to: 1184,\n    from: [1082, 1114, 1184],\n    serialize: x => (x instanceof Date ? x : new Date(x)).toISOString(),\n    parse: x => new Date(x)\n  },\n  bytea: {\n    to: 17,\n    from: 17,\n    serialize: x => '\\\\x' + Buffer.from(x).toString('hex'),\n    parse: x => Buffer.from(x.slice(2), 'hex')\n  }\n}\n\nclass NotTagged { then() { notTagged() } catch() { notTagged() } finally() { notTagged() }}\n\nclass Identifier extends NotTagged {\n  constructor(value) {\n    super()\n    this.value = escapeIdentifier(value)\n  }\n}\n\nclass Parameter extends NotTagged {\n  constructor(value, type, array) {\n    super()\n    this.value = value\n    this.type = type\n    this.array = array\n  }\n}\n\nclass Builder extends NotTagged {\n  constructor(first, rest) {\n    super()\n    this.first = first\n    this.rest = rest\n  }\n\n  build(before, parameters, types, options) {\n    const keyword = builders.map(([x, fn]) => ({ fn, i: before.search(x) })).sort((a, b) => a.i - b.i).pop()\n    return keyword.i === -1\n      ? escapeIdentifiers(this.first, options)\n      : keyword.fn(this.first, this.rest, parameters, types, options)\n  }\n}\n\nfunction handleValue(x, parameters, types, options) {\n  let value = x instanceof Parameter ? x.value : x\n  if (value === undefined) {\n    x instanceof Parameter\n      ? x.value = options.transform.undefined\n      : value = x = options.transform.undefined\n\n    if (value === undefined)\n      throw _errors_js__WEBPACK_IMPORTED_MODULE_1__.Errors.generic('UNDEFINED_VALUE', 'Undefined values are not allowed')\n  }\n\n  return '$' + (types.push(\n    x instanceof Parameter\n      ? (parameters.push(x.value), x.array\n        ? x.array[x.type || inferType(x.value)] || x.type || firstIsString(x.value)\n        : x.type\n      )\n      : (parameters.push(x), inferType(x))\n  ))\n}\n\nconst defaultHandlers = typeHandlers(types)\n\nfunction stringify(q, string, value, parameters, types, options) { // eslint-disable-line\n  for (let i = 1; i < q.strings.length; i++) {\n    string += (stringifyValue(string, value, parameters, types, options)) + q.strings[i]\n    value = q.args[i]\n  }\n\n  return string\n}\n\nfunction stringifyValue(string, value, parameters, types, o) {\n  return (\n    value instanceof Builder ? value.build(string, parameters, types, o) :\n    value instanceof _query_js__WEBPACK_IMPORTED_MODULE_0__.Query ? fragment(value, parameters, types, o) :\n    value instanceof Identifier ? value.value :\n    value && value[0] instanceof _query_js__WEBPACK_IMPORTED_MODULE_0__.Query ? value.reduce((acc, x) => acc + ' ' + fragment(x, parameters, types, o), '') :\n    handleValue(value, parameters, types, o)\n  )\n}\n\nfunction fragment(q, parameters, types, options) {\n  q.fragment = true\n  return stringify(q, q.strings[0], q.args[0], parameters, types, options)\n}\n\nfunction valuesBuilder(first, parameters, types, columns, options) {\n  return first.map(row =>\n    '(' + columns.map(column =>\n      stringifyValue('values', row[column], parameters, types, options)\n    ).join(',') + ')'\n  ).join(',')\n}\n\nfunction values(first, rest, parameters, types, options) {\n  const multi = Array.isArray(first[0])\n  const columns = rest.length ? rest.flat() : Object.keys(multi ? first[0] : first)\n  return valuesBuilder(multi ? first : [first], parameters, types, columns, options)\n}\n\nfunction select(first, rest, parameters, types, options) {\n  typeof first === 'string' && (first = [first].concat(rest))\n  if (Array.isArray(first))\n    return escapeIdentifiers(first, options)\n\n  let value\n  const columns = rest.length ? rest.flat() : Object.keys(first)\n  return columns.map(x => {\n    value = first[x]\n    return (\n      value instanceof _query_js__WEBPACK_IMPORTED_MODULE_0__.Query ? fragment(value, parameters, types, options) :\n      value instanceof Identifier ? value.value :\n      handleValue(value, parameters, types, options)\n    ) + ' as ' + escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x)\n  }).join(',')\n}\n\nconst builders = Object.entries({\n  values,\n  in: (...xs) => {\n    const x = values(...xs)\n    return x === '()' ? '(null)' : x\n  },\n  select,\n  as: select,\n  returning: select,\n  '\\\\(': select,\n\n  update(first, rest, parameters, types, options) {\n    return (rest.length ? rest.flat() : Object.keys(first)).map(x =>\n      escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x) +\n      '=' + stringifyValue('values', first[x], parameters, types, options)\n    )\n  },\n\n  insert(first, rest, parameters, types, options) {\n    const columns = rest.length ? rest.flat() : Object.keys(Array.isArray(first) ? first[0] : first)\n    return '(' + escapeIdentifiers(columns, options) + ')values' +\n    valuesBuilder(Array.isArray(first) ? first : [first], parameters, types, columns, options)\n  }\n}).map(([x, fn]) => ([new RegExp('((?:^|[\\\\s(])' + x + '(?:$|[\\\\s(]))(?![\\\\s\\\\S]*\\\\1)', 'i'), fn]))\n\nfunction notTagged() {\n  throw _errors_js__WEBPACK_IMPORTED_MODULE_1__.Errors.generic('NOT_TAGGED_CALL', 'Query not called as a tagged template literal')\n}\n\nconst serializers = defaultHandlers.serializers\nconst parsers = defaultHandlers.parsers\n\nconst END = {}\n\nfunction firstIsString(x) {\n  if (Array.isArray(x))\n    return firstIsString(x[0])\n  return typeof x === 'string' ? 1009 : 0\n}\n\nconst mergeUserTypes = function(types) {\n  const user = typeHandlers(types || {})\n  return {\n    serializers: Object.assign({}, serializers, user.serializers),\n    parsers: Object.assign({}, parsers, user.parsers)\n  }\n}\n\nfunction typeHandlers(types) {\n  return Object.keys(types).reduce((acc, k) => {\n    types[k].from && [].concat(types[k].from).forEach(x => acc.parsers[x] = types[k].parse)\n    if (types[k].serialize) {\n      acc.serializers[types[k].to] = types[k].serialize\n      types[k].from && [].concat(types[k].from).forEach(x => acc.serializers[x] = types[k].serialize)\n    }\n    return acc\n  }, { parsers: {}, serializers: {} })\n}\n\nfunction escapeIdentifiers(xs, { transform: { column } }) {\n  return xs.map(x => escapeIdentifier(column.to ? column.to(x) : x)).join(',')\n}\n\nconst escapeIdentifier = function escape(str) {\n  return '\"' + str.replace(/\"/g, '\"\"').replace(/\\./g, '\".\"') + '\"'\n}\n\nconst inferType = function inferType(x) {\n  return (\n    x instanceof Parameter ? x.type :\n    x instanceof Date ? 1184 :\n    x instanceof Uint8Array ? 17 :\n    (x === true || x === false) ? 16 :\n    typeof x === 'bigint' ? 20 :\n    Array.isArray(x) ? inferType(x[0]) :\n    0\n  )\n}\n\nconst escapeBackslash = /\\\\/g\nconst escapeQuote = /\"/g\n\nfunction arrayEscape(x) {\n  return x\n    .replace(escapeBackslash, '\\\\\\\\')\n    .replace(escapeQuote, '\\\\\"')\n}\n\nconst arraySerializer = function arraySerializer(xs, serializer, options, typarray) {\n  if (Array.isArray(xs) === false)\n    return xs\n\n  if (!xs.length)\n    return '{}'\n\n  const first = xs[0]\n  // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter\n  const delimiter = typarray === 1020 ? ';' : ','\n\n  if (Array.isArray(first) && !first.type)\n    return '{' + xs.map(x => arraySerializer(x, serializer, options, typarray)).join(delimiter) + '}'\n\n  return '{' + xs.map(x => {\n    if (x === undefined) {\n      x = options.transform.undefined\n      if (x === undefined)\n        throw _errors_js__WEBPACK_IMPORTED_MODULE_1__.Errors.generic('UNDEFINED_VALUE', 'Undefined values are not allowed')\n    }\n\n    return x === null\n      ? 'null'\n      : '\"' + arrayEscape(serializer ? serializer(x.type ? x.value : x) : '' + x) + '\"'\n  }).join(delimiter) + '}'\n}\n\nconst arrayParserState = {\n  i: 0,\n  char: null,\n  str: '',\n  quoted: false,\n  last: 0\n}\n\nconst arrayParser = function arrayParser(x, parser, typarray) {\n  arrayParserState.i = arrayParserState.last = 0\n  return arrayParserLoop(arrayParserState, x, parser, typarray)\n}\n\nfunction arrayParserLoop(s, x, parser, typarray) {\n  const xs = []\n  // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter\n  const delimiter = typarray === 1020 ? ';' : ','\n  for (; s.i < x.length; s.i++) {\n    s.char = x[s.i]\n    if (s.quoted) {\n      if (s.char === '\\\\') {\n        s.str += x[++s.i]\n      } else if (s.char === '\"') {\n        xs.push(parser ? parser(s.str) : s.str)\n        s.str = ''\n        s.quoted = x[s.i + 1] === '\"'\n        s.last = s.i + 2\n      } else {\n        s.str += s.char\n      }\n    } else if (s.char === '\"') {\n      s.quoted = true\n    } else if (s.char === '{') {\n      s.last = ++s.i\n      xs.push(arrayParserLoop(s, x, parser, typarray))\n    } else if (s.char === '}') {\n      s.quoted = false\n      s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i))\n      s.last = s.i + 1\n      break\n    } else if (s.char === delimiter && s.p !== '}' && s.p !== '\"') {\n      xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i))\n      s.last = s.i + 1\n    }\n    s.p = s.char\n  }\n  s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i + 1)) : x.slice(s.last, s.i + 1))\n  return xs\n}\n\nconst toCamel = x => {\n  let str = x[0]\n  for (let i = 1; i < x.length; i++)\n    str += x[i] === '_' ? x[++i].toUpperCase() : x[i]\n  return str\n}\n\nconst toPascal = x => {\n  let str = x[0].toUpperCase()\n  for (let i = 1; i < x.length; i++)\n    str += x[i] === '_' ? x[++i].toUpperCase() : x[i]\n  return str\n}\n\nconst toKebab = x => x.replace(/_/g, '-')\n\nconst fromCamel = x => x.replace(/([A-Z])/g, '_$1').toLowerCase()\nconst fromPascal = x => (x.slice(0, 1) + x.slice(1).replace(/([A-Z])/g, '_$1')).toLowerCase()\nconst fromKebab = x => x.replace(/-/g, '_')\n\nfunction createJsonTransform(fn) {\n  return function jsonTransform(x, column) {\n    return typeof x === 'object' && x !== null && (column.type === 114 || column.type === 3802)\n      ? Array.isArray(x)\n        ? x.map(x => jsonTransform(x, column))\n        : Object.entries(x).reduce((acc, [k, v]) => Object.assign(acc, { [fn(k)]: jsonTransform(v, column) }), {})\n      : x\n  }\n}\n\ntoCamel.column = { from: toCamel }\ntoCamel.value = { from: createJsonTransform(toCamel) }\nfromCamel.column = { to: fromCamel }\n\nconst camel = { ...toCamel }\ncamel.column.to = fromCamel\n\ntoPascal.column = { from: toPascal }\ntoPascal.value = { from: createJsonTransform(toPascal) }\nfromPascal.column = { to: fromPascal }\n\nconst pascal = { ...toPascal }\npascal.column.to = fromPascal\n\ntoKebab.column = { from: toKebab }\ntoKebab.value = { from: createJsonTransform(toKebab) }\nfromKebab.column = { to: fromKebab }\n\nconst kebab = { ...toKebab }\nkebab.column.to = fromKebab\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/types.js\n");

/***/ })

};
;