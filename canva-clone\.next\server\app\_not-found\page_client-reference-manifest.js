globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/(dashboard)/banner.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/banner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/create-project-modal.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/create-project-modal.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/projects-section.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/projects-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/public-gallery-banner.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/public-gallery-banner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/templates-section.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/templates-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/sidebar-routes.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/sidebar-routes.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/features/auth/components/user-button.tsx":{"*":{"id":"(ssr)/./src/features/auth/components/user-button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-auth/react.js":{"*":{"id":"(ssr)/./node_modules/next-auth/react.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/modals.tsx":{"*":{"id":"(ssr)/./src/components/modals.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers.tsx":{"*":{"id":"(ssr)/./src/components/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/sonner.tsx":{"*":{"id":"(ssr)/./src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/features/subscriptions/components/subscription-alert.tsx":{"*":{"id":"(ssr)/./src/features/subscriptions/components/subscription-alert.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/editor/[projectId]/page.tsx":{"*":{"id":"(ssr)/./src/app/editor/[projectId]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/public/page.tsx":{"*":{"id":"(ssr)/./src/app/public/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\(dashboard)\\banner.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/banner.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\(dashboard)\\create-project-modal.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/create-project-modal.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\(dashboard)\\projects-section.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/projects-section.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\(dashboard)\\public-gallery-banner.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/public-gallery-banner.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\(dashboard)\\templates-section.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/templates-section.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\(dashboard)\\\\logo.tsx\",\"import\":\"Space_Grotesk\",\"arguments\":[{\"weight\":[\"700\"],\"subsets\":[\"latin\"]}],\"variableName\":\"font\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\(dashboard)\\\\logo.tsx\",\"import\":\"Space_Grotesk\",\"arguments\":[{\"weight\":[\"700\"],\"subsets\":[\"latin\"]}],\"variableName\":\"font\"}","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\(dashboard)\\sidebar-routes.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/sidebar-routes.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\features\\auth\\components\\user-button.tsx":{"id":"(app-pages-browser)/./src/features/auth/components/user-button.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next-auth\\react.js":{"id":"(app-pages-browser)/./node_modules/next-auth/react.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\components\\modals.tsx":{"id":"(app-pages-browser)/./src/components/modals.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\components\\providers.tsx":{"id":"(app-pages-browser)/./src/components/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\components\\ui\\sonner.tsx":{"id":"(app-pages-browser)/./src/components/ui/sonner.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\features\\subscriptions\\components\\subscription-alert.tsx":{"id":"(app-pages-browser)/./src/features/subscriptions/components/subscription-alert.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\editor\\[projectId]\\page.tsx":{"id":"(app-pages-browser)/./src/app/editor/[projectId]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\public\\page.tsx":{"id":"(app-pages-browser)/./src/app/public/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\":[],"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\(dashboard)\\page":[],"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\(dashboard)\\layout":["static/css/app/(dashboard)/layout.css"],"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Downloads\\f\\canva-clone\\src\\app\\_not-found\\page":[]}}