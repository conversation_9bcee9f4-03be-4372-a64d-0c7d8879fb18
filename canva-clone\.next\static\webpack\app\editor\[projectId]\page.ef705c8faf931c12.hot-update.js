"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_utils_thumbnail__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/utils/thumbnail */ \"(app-pages-browser)/./src/features/editor/utils/thumbnail.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        try {\n            const data = JSON.parse(json);\n            // Validate canvas dimensions before loading\n            const canvasWidth = canvas.getWidth();\n            const canvasHeight = canvas.getHeight();\n            if (canvasWidth <= 0 || canvasHeight <= 0) {\n                console.warn(\"Cannot load JSON: Canvas has invalid dimensions\", {\n                    canvasWidth,\n                    canvasHeight\n                });\n                return;\n            }\n            canvas.loadFromJSON(data, ()=>{\n                // Ensure all objects have valid dimensions after loading\n                canvas.getObjects().forEach((obj)=>{\n                    if (obj.width === 0 || obj.height === 0) {\n                        console.warn(\"Object has zero dimensions after loading:\", obj);\n                        // Set minimum dimensions to prevent rendering errors\n                        if (obj.width === 0) obj.set(\"width\", 1);\n                        if (obj.height === 0) obj.set(\"height\", 1);\n                    }\n                });\n                autoZoom();\n            });\n        } catch (error) {\n            console.error(\"Error loading JSON:\", error);\n        }\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        generateThumbnail: (options)=>{\n            return (0,_features_editor_utils_thumbnail__WEBPACK_IMPORTED_MODULE_5__.generateThumbnail)(canvas, options);\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_7__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_8__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_9__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_6__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        // Fix fabric.js textBaseline issue\n        // @ts-ignore - textBaseline is a valid fabric.js property\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Text.prototype.set({\n            textBaseline: \"middle\"\n        });\n        // @ts-ignore - textBaseline is a valid fabric.js property\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox.prototype.set({\n            textBaseline: \"middle\"\n        });\n        // Override fabric.js render methods to prevent zero dimension errors\n        const originalRender = fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.render;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.render = function(ctx) {\n            if (this.width === 0 || this.height === 0) {\n                console.warn(\"Skipping render for object with zero dimensions:\", this);\n                return;\n            }\n            return originalRender.call(this, ctx);\n        };\n        // Ensure we have valid dimensions\n        const containerWidth = initialContainer.offsetWidth || 800;\n        const containerHeight = initialContainer.offsetHeight || 600;\n        const workspaceWidth = initialWidth.current || 900;\n        const workspaceHeight = initialHeight.current || 1200;\n        // Validate dimensions\n        if (workspaceWidth <= 0 || workspaceHeight <= 0) {\n            console.error(\"Invalid workspace dimensions:\", {\n                workspaceWidth,\n                workspaceHeight\n            });\n            return;\n        }\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: workspaceWidth,\n            height: workspaceHeight,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(containerWidth);\n        initialCanvas.setHeight(containerHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ })

});