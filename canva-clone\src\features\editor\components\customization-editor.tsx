"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { fabric } from "fabric";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Footer } from "@/features/editor/components/footer";
import { EditableLayer } from "@/types/template";

interface CustomizationEditorProps {
  templateData: {
    id: string;
    name: string;
    width: number;
    height: number;
    json: string;
    editableLayers: EditableLayer[];
  };
  customizations: Record<string, string>;
  onCustomizationChange: (layerId: string, value: string) => void;
  onPreviewGenerated: (dataUrl: string) => void;
}

export const CustomizationEditor = ({
  templateData,
  customizations,
  onCustomizationChange,
  onPreviewGenerated,
}: CustomizationEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize editor with no-save callback since this is read-only customization
  const { init, editor } = useEditor({
    defaultState: templateData.json,
    defaultWidth: templateData.width,
    defaultHeight: templateData.height,
    clearSelectionCallback: () => {},
    saveCallback: () => {}, // No saving in customization mode
  });

  // Initialize canvas
  useEffect(() => {
    const initializeCanvas = () => {
      if (!containerRef.current || !canvasRef.current) return false;

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      if (containerWidth === 0 || containerHeight === 0) {
        return false;
      }

      const canvas = new fabric.Canvas(canvasRef.current, {
        controlsAboveOverlay: true,
        preserveObjectStacking: true,
      });

      init({
        initialCanvas: canvas,
        initialContainer: container,
      });

      return canvas;
    };

    const canvas = initializeCanvas();

    if (!canvas) {
      const timeoutId = setTimeout(() => {
        const retryCanvas = initializeCanvas();
        if (retryCanvas) {
          setIsInitialized(true);
        }
      }, 100);

      return () => clearTimeout(timeoutId);
    } else {
      setIsInitialized(true);
    }

    return () => {
      if (canvas) {
        canvas.dispose();
      }
    };
  }, [init]);

  // Lock non-editable objects and setup customization mode
  useEffect(() => {
    if (!editor?.canvas || !isInitialized) return;

    const canvas = editor.canvas;
    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);

    console.log('Setting up customization mode:', {
      editableLayerIds,
      totalObjects: canvas.getObjects().length,
      editableLayers: templateData.editableLayers
    });

    // Lock all objects except editable ones
    canvas.getObjects().forEach((obj: any) => {
      console.log('Processing object:', {
        id: obj.id,
        type: obj.type,
        name: obj.name,
        isEditable: editableLayerIds.includes(obj.id)
      });

      if (obj.name === "clip") {
        // Keep workspace as is but make it non-selectable
        obj.set({
          selectable: false,
          evented: false,
        });
        return;
      }

      if (editableLayerIds.includes(obj.id)) {
        console.log('Making object editable:', obj.id, obj.type);
        // Make editable objects selectable and editable
        obj.set({
          selectable: true,
          hasControls: obj.type === 'textbox' ? false : true, // Text doesn't need resize controls
          hasBorders: true,
          lockMovementX: obj.type === 'textbox' ? true : false, // Text can't be moved
          lockMovementY: obj.type === 'textbox' ? true : false,
          lockRotation: true,
          lockScalingX: obj.type === 'textbox' ? true : false,
          lockScalingY: obj.type === 'textbox' ? true : false,
          lockUniScaling: obj.type === 'textbox' ? true : false,
          editable: obj.type === 'textbox' ? true : false, // Enable text editing
        });
      } else {
        // Lock non-editable objects completely
        obj.set({
          selectable: false,
          evented: false,
          lockMovementX: true,
          lockMovementY: true,
          lockRotation: true,
          lockScalingX: true,
          lockScalingY: true,
          lockUniScaling: true,
        });
      }
    });

    canvas.renderAll();
  }, [editor, isInitialized, templateData.editableLayers]);

  // Apply customizations to canvas
  useEffect(() => {
    if (!editor?.canvas || !isInitialized) return;

    const canvas = editor.canvas;

    templateData.editableLayers.forEach(layer => {
      const customValue = customizations[layer.id];
      if (!customValue || customValue === layer.originalValue) return;

      const canvasObject = canvas.getObjects().find((obj: any) => obj.id === layer.id);
      if (!canvasObject) return;

      if (layer.type === 'text' && canvasObject.type === 'textbox') {
        (canvasObject as fabric.Textbox).set('text', customValue);
      } else if (layer.type === 'image' && customValue.startsWith('blob:')) {
        fabric.Image.fromURL(customValue, (img) => {
          if (!canvas) return;

          // Scale image to fit the object bounds
          const scaleX = canvasObject.width! / img.width!;
          const scaleY = canvasObject.height! / img.height!;
          const scale = Math.min(scaleX, scaleY);

          img.set({
            left: canvasObject.left,
            top: canvasObject.top,
            scaleX: scale,
            scaleY: scale,
            originX: canvasObject.originX,
            originY: canvasObject.originY,
            id: layer.id,
            selectable: true,
            hasControls: false,
            lockMovementX: true,
            lockMovementY: true,
            lockRotation: true,
            lockScalingX: true,
            lockScalingY: true,
            lockUniScaling: true,
          });

          canvas.remove(canvasObject);
          canvas.add(img);
          canvas.renderAll();
          generatePreview();
        });
        return;
      }
    });

    canvas.renderAll();
    generatePreview();
  }, [customizations, editor, isInitialized, templateData.editableLayers]);

  // Generate preview from canvas
  const generatePreview = useCallback(() => {
    if (!editor?.canvas) return;

    try {
      const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
      if (!workspace) return;

      // Create a temporary canvas with just the workspace content
      const tempCanvas = new fabric.Canvas(null, {
        width: workspace.width,
        height: workspace.height,
      });

      // Clone all objects except the workspace itself
      const objectsToClone = editor.canvas.getObjects().filter((obj: any) => obj.name !== "clip");
      
      objectsToClone.forEach((obj) => {
        obj.clone((cloned: fabric.Object) => {
          // Adjust position relative to workspace
          cloned.set({
            left: (cloned.left || 0) - (workspace.left || 0),
            top: (cloned.top || 0) - (workspace.top || 0),
          });
          tempCanvas.add(cloned);
        });
      });

      // Generate preview after a short delay to ensure all objects are added
      setTimeout(() => {
        const dataUrl = tempCanvas.toDataURL({
          format: 'png',
          quality: 0.9,
          multiplier: 1,
        });
        onPreviewGenerated(dataUrl);
        tempCanvas.dispose();
      }, 100);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    }
  }, [editor, onPreviewGenerated]);

  // Handle text editing
  const handleTextEdit = useCallback((layerId: string, value: string) => {
    onCustomizationChange(layerId, value);
  }, [onCustomizationChange]);

  // Handle text editing events
  useEffect(() => {
    if (!editor?.canvas) return;

    const handleTextChanged = (e: fabric.IEvent) => {
      const target = e.target;
      if (!target || target.type !== 'textbox') return;

      const layerId = (target as any).id;
      const layer = templateData.editableLayers.find(l => l.id === layerId);
      if (!layer || layer.type !== 'text') return;

      const currentText = (target as fabric.Textbox).text || '';
      handleTextEdit(layerId, currentText);
      generatePreview();
    };

    const handleDoubleClick = (e: fabric.IEvent) => {
      const target = e.target;
      if (!target || target.type !== 'textbox') return;

      const layerId = (target as any).id;
      const layer = templateData.editableLayers.find(l => l.id === layerId);
      if (layer && layer.type === 'text') {
        // Enter editing mode
        (target as fabric.Textbox).enterEditing();
        (target as fabric.Textbox).selectAll();
      }
    };

    editor.canvas.on('text:changed', handleTextChanged);
    editor.canvas.on('mouse:dblclick', handleDoubleClick);

    return () => {
      editor.canvas.off('text:changed', handleTextChanged);
      editor.canvas.off('mouse:dblclick', handleDoubleClick);
    };
  }, [editor, templateData.editableLayers, handleTextEdit, generatePreview]);

  return (
    <div className="h-full flex flex-col bg-muted">
      <div className="flex-1 relative">
        <div className="absolute inset-0" ref={containerRef}>
          <canvas ref={canvasRef} />
        </div>
      </div>
      <Footer editor={editor} />
    </div>
  );
};
