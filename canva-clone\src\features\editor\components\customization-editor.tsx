"use client";

import { useEffect, useRef, useCallback } from "react";
import { fabric } from "fabric";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Footer } from "@/features/editor/components/footer";
import { Toolbar } from "@/features/editor/components/toolbar";
import { EditableLayer } from "@/types/template";

interface CustomizationEditorProps {
  templateData: {
    id: string;
    name: string;
    width: number;
    height: number;
    json: string;
    editableLayers: EditableLayer[];
  };
  customizations: Record<string, string>;
  onCustomizationChange: (layerId: string, value: string) => void;
  onPreviewGenerated: (dataUrl: string) => void;
  activeLayerId?: string | null;
  onLayerActivation?: (layerId: string | null) => void;
}

export const CustomizationEditor = ({
  templateData,
  customizations,
  onCustomizationChange,
  onPreviewGenerated,
  activeLayerId: externalActiveLayerId,
  onLayerActivation,
}: CustomizationEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Initialize editor first
  const { init, editor } = useEditor({
    defaultState: templateData.json,
    defaultWidth: templateData.width,
    defaultHeight: templateData.height,
    clearSelectionCallback: () => {
      onLayerActivation?.(null);
    },
    saveCallback: () => {
      // Generate preview when canvas changes
      generatePreview();
    },
  });

  // Generate preview from canvas (defined after editor)
  const generatePreview = useCallback(() => {
    if (!editor?.canvas) return;

    try {
      const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
      if (!workspace) return;

      const dataUrl = editor.canvas.toDataURL({
        format: 'png',
        quality: 0.9,
        multiplier: 0.5,
      });
      onPreviewGenerated(dataUrl);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    }
  }, [editor, onPreviewGenerated]);

  // Initialize canvas (same as main editor)
  useEffect(() => {
    const initializeCanvas = () => {
      if (!containerRef.current || !canvasRef.current) return false;

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      if (containerWidth === 0 || containerHeight === 0) {
        return false;
      }

      const canvas = new fabric.Canvas(canvasRef.current, {
        controlsAboveOverlay: true,
        preserveObjectStacking: true,
      });

      init({
        initialCanvas: canvas,
        initialContainer: container,
      });

      return canvas;
    };

    const canvas = initializeCanvas();

    if (!canvas) {
      const timeoutId = setTimeout(() => {
        initializeCanvas();
      }, 100);

      return () => clearTimeout(timeoutId);
    }

    return () => {
      if (canvas) {
        canvas.dispose();
      }
    };
  }, [init]);

  // Handle selection changes to notify parent
  useEffect(() => {
    if (!editor?.canvas) return;

    const handleSelectionCreated = (e: fabric.IEvent) => {
      const target = e.target;
      if (target) {
        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);
        if (layer) {
          onLayerActivation?.(layerId);
        }
      }
    };

    const handleSelectionCleared = () => {
      onLayerActivation?.(null);
    };

    const handleTextChanged = (e: fabric.IEvent) => {
      const target = e.target;
      if (target && target.type === 'textbox') {
        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);
        if (layer && layer.type === 'text') {
          const currentText = (target as fabric.Textbox).text || '';
          onCustomizationChange(layerId, currentText);
        }
      }
    };

    editor.canvas.on('selection:created', handleSelectionCreated);
    editor.canvas.on('selection:updated', handleSelectionCreated);
    editor.canvas.on('selection:cleared', handleSelectionCleared);
    editor.canvas.on('text:changed', handleTextChanged);

    return () => {
      editor.canvas.off('selection:created', handleSelectionCreated);
      editor.canvas.off('selection:updated', handleSelectionCreated);
      editor.canvas.off('selection:cleared', handleSelectionCleared);
      editor.canvas.off('text:changed', handleTextChanged);
    };
  }, [editor, templateData.editableLayers, onLayerActivation, onCustomizationChange]);

  // Apply customizations from sidebar to canvas
  useEffect(() => {
    if (!editor?.canvas) return;

    console.log('Applying customizations:', customizations);

    templateData.editableLayers.forEach(layer => {
      const customValue = customizations[layer.id];
      console.log(`Processing layer ${layer.id} (${layer.type}):`, customValue);

      const canvasObject = editor.canvas.getObjects().find((obj: any) => obj.id === layer.id);
      if (!canvasObject) {
        console.log(`Canvas object not found for layer ${layer.id}`);
        return;
      }

      if (layer.type === 'text' && canvasObject.type === 'textbox') {
        const textbox = canvasObject as fabric.Textbox;
        console.log(`Current text: "${textbox.text}", New text: "${customValue}"`);

        if (customValue && textbox.text !== customValue) {
          console.log(`Updating text for ${layer.id} to: "${customValue}"`);
          textbox.set('text', customValue);
          editor.canvas.renderAll();

          // Trigger save to update the canvas state
          if (editor.canvas.fire) {
            editor.canvas.fire('text:changed', { target: textbox });
          }
        }
      } else if (layer.type === 'image' && customValue) {
        console.log(`Replacing image for ${layer.id} with: ${customValue}`);

        // Handle image replacement
        fabric.Image.fromURL(customValue, (img) => {
          if (!editor.canvas) return;

          console.log('Original image object:', canvasObject);
          console.log('New image loaded:', img);

          // Get the original object's properties
          const originalLeft = canvasObject.left || 0;
          const originalTop = canvasObject.top || 0;
          const originalWidth = canvasObject.width || 100;
          const originalHeight = canvasObject.height || 100;
          const originalScaleX = canvasObject.scaleX || 1;
          const originalScaleY = canvasObject.scaleY || 1;

          // Calculate scale to fit within original bounds
          const targetWidth = originalWidth * originalScaleX;
          const targetHeight = originalHeight * originalScaleY;
          const scaleX = targetWidth / img.width!;
          const scaleY = targetHeight / img.height!;
          const scale = Math.min(scaleX, scaleY);

          // Set the new image properties
          img.set({
            left: originalLeft,
            top: originalTop,
            scaleX: scale,
            scaleY: scale,
            originX: canvasObject.originX || 'left',
            originY: canvasObject.originY || 'top',
            selectable: true,
            hasControls: true,
            hasBorders: true,
          });

          // Set the ID using a custom property
          (img as any).id = layer.id;

          // Remove old image and add new one
          const objectIndex = editor.canvas.getObjects().indexOf(canvasObject);
          editor.canvas.remove(canvasObject);
          editor.canvas.insertAt(img, objectIndex, false);
          editor.canvas.setActiveObject(img);
          editor.canvas.renderAll();

          console.log(`Image replaced for ${layer.id}`, img);
        }, {
          crossOrigin: 'anonymous'
        });
      }
    });
  }, [customizations, editor, templateData.editableLayers]);

  // Ensure canvas objects have proper IDs when editor loads
  useEffect(() => {
    if (!editor?.canvas) return;

    const canvas = editor.canvas;
    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);

    console.log('Setting up canvas object IDs for editable layers:', editableLayerIds);

    // Wait a bit for the canvas to be fully loaded
    setTimeout(() => {
      const allObjects = canvas.getObjects();
      console.log('All canvas objects after load:', allObjects.map((obj: any) => ({
        id: obj.id,
        type: obj.type,
        name: obj.name,
        text: obj.type === 'textbox' ? obj.text : undefined
      })));

      // Try to match objects to editable layers by content or position
      templateData.editableLayers.forEach((layer) => {
        let matchedObject = allObjects.find((obj: any) => obj.id === layer.id);

        if (!matchedObject) {
          if (layer.type === 'text') {
            // Try to find by text content if no ID match
            matchedObject = allObjects.find((obj: any) =>
              obj.type === 'textbox' &&
              obj.text === layer.originalValue &&
              !editableLayerIds.includes(obj.id)
            );
          } else if (layer.type === 'image') {
            // For images, try to find by type and position, or just by type if it's the nth image
            const imageObjects = allObjects.filter((obj: any) =>
              obj.type === 'image' && !editableLayerIds.includes(obj.id)
            );

            // Try to match by index (first editable image layer matches first unmatched image object)
            const imageLayerIndex = templateData.editableLayers
              .filter(l => l.type === 'image')
              .indexOf(layer);

            if (imageObjects[imageLayerIndex]) {
              matchedObject = imageObjects[imageLayerIndex];
            }
          }
        }

        if (matchedObject && !(matchedObject as any).id) {
          console.log(`Assigning ID ${layer.id} to ${layer.type} object:`, matchedObject);
          (matchedObject as any).id = layer.id;
        }
      });

      canvas.renderAll();
    }, 500);
  }, [editor, templateData.editableLayers]);

  // Function to select object by layer ID (called from parent)
  useEffect(() => {
    if (!editor?.canvas) return;

    console.log('External active layer changed:', externalActiveLayerId);

    if (externalActiveLayerId) {
      const allObjects = editor.canvas.getObjects();
      console.log('All canvas objects:', allObjects.map((obj: any) => ({ id: obj.id, type: obj.type, name: obj.name })));

      const targetObject = allObjects.find((obj: any) => obj.id === externalActiveLayerId);
      console.log('Found target object:', targetObject);

      if (targetObject) {
        console.log('Selecting object in canvas:', (targetObject as any).id);
        editor.canvas.setActiveObject(targetObject);
        editor.canvas.renderAll();
      } else {
        console.log('Target object not found for ID:', externalActiveLayerId);
      }
    } else {
      // Clear selection if no layer is active
      console.log('Clearing canvas selection');
      editor.canvas.discardActiveObject();
      editor.canvas.renderAll();
    }
  }, [editor, externalActiveLayerId]);

  return (
    <div className="h-full flex flex-col bg-muted">
      <Toolbar
        editor={editor}
        activeTool="select"
        onChangeActiveTool={() => {}}
        key={JSON.stringify(editor?.canvas.getActiveObject())}
      />
      <div className="flex-1 h-[calc(100%-124px)] bg-muted" ref={containerRef}>
        <canvas ref={canvasRef} />
      </div>
      <Footer editor={editor} />
    </div>
  );
};
