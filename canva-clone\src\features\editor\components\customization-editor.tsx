"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { fabric } from "fabric";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Footer } from "@/features/editor/components/footer";
import { EditableLayer } from "@/types/template";

interface CustomizationEditorProps {
  templateData: {
    id: string;
    name: string;
    width: number;
    height: number;
    json: string;
    editableLayers: EditableLayer[];
  };
  customizations: Record<string, string>;
  onCustomizationChange: (layerId: string, value: string) => void;
  onPreviewGenerated: (dataUrl: string) => void;
  activeLayerId?: string | null;
  onLayerActivation?: (layerId: string | null) => void;
}

export const CustomizationEditor = ({
  templateData,
  customizations,
  onCustomizationChange,
  onPreviewGenerated,
  activeLayerId: externalActiveLayerId,
  onLayerActivation,
}: CustomizationEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [activeLayerId, setActiveLayerId] = useState<string | null>(externalActiveLayerId || null);

  // Sync with external active layer
  useEffect(() => {
    setActiveLayerId(externalActiveLayerId || null);
  }, [externalActiveLayerId]);

  // Initialize editor with no-save callback since this is read-only customization
  const { init, editor } = useEditor({
    defaultState: templateData.json,
    defaultWidth: templateData.width,
    defaultHeight: templateData.height,
    clearSelectionCallback: () => {},
    saveCallback: () => {}, // No saving in customization mode
  });

  // Initialize canvas
  useEffect(() => {
    const initializeCanvas = () => {
      if (!containerRef.current || !canvasRef.current) return false;

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      if (containerWidth === 0 || containerHeight === 0) {
        return false;
      }

      const canvas = new fabric.Canvas(canvasRef.current, {
        controlsAboveOverlay: true,
        preserveObjectStacking: true,
      });

      init({
        initialCanvas: canvas,
        initialContainer: container,
      });

      return canvas;
    };

    const canvas = initializeCanvas();

    if (!canvas) {
      const timeoutId = setTimeout(() => {
        const retryCanvas = initializeCanvas();
        if (retryCanvas) {
          setIsInitialized(true);
        }
      }, 100);

      return () => clearTimeout(timeoutId);
    } else {
      setIsInitialized(true);
    }

    return () => {
      if (canvas) {
        canvas.dispose();
      }
    };
  }, [init]);

  // Lock non-editable objects and setup customization mode
  useEffect(() => {
    if (!editor?.canvas || !isInitialized) return;

    const canvas = editor.canvas;
    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);

    console.log('Setting up customization mode:', {
      editableLayerIds,
      totalObjects: canvas.getObjects().length,
      editableLayers: templateData.editableLayers
    });

    // Lock all objects except editable ones
    canvas.getObjects().forEach((obj: any) => {
      console.log('Processing object:', {
        id: obj.id,
        type: obj.type,
        name: obj.name,
        isEditable: editableLayerIds.includes(obj.id)
      });

      if (obj.name === "clip") {
        // Keep workspace as is but make it non-selectable
        obj.set({
          selectable: false,
          evented: false,
        });
        return;
      }

      if (editableLayerIds.includes(obj.id)) {
        console.log('Making object editable:', obj.id, obj.type);
        // Make editable objects selectable and editable
        obj.set({
          selectable: true,
          hasControls: false, // Remove all controls for cleaner look
          hasBorders: true,
          borderColor: '#3b82f6',
          borderScaleFactor: 2,
          lockMovementX: true, // Lock movement for all editable objects
          lockMovementY: true,
          lockRotation: true,
          lockScalingX: true,
          lockScalingY: true,
          lockUniScaling: true,
          editable: obj.type === 'textbox' ? true : false, // Enable text editing for text
          hoverCursor: 'pointer',
          moveCursor: 'pointer',
        });

        // Add visual indicator for editable elements
        if (obj.type === 'textbox') {
          obj.set({
            backgroundColor: 'rgba(59, 130, 246, 0.1)', // Light blue background
          });
        }
      } else {
        // Lock non-editable objects completely
        obj.set({
          selectable: false,
          evented: false,
          lockMovementX: true,
          lockMovementY: true,
          lockRotation: true,
          lockScalingX: true,
          lockScalingY: true,
          lockUniScaling: true,
        });
      }
    });

    canvas.renderAll();
  }, [editor, isInitialized, templateData.editableLayers]);

  // Update visual feedback for editable objects
  const updateEditableObjectsVisuals = useCallback(() => {
    if (!editor?.canvas) return;

    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);

    editor.canvas.getObjects().forEach((obj: any) => {
      if (editableLayerIds.includes(obj.id)) {
        const isActive = obj.id === activeLayerId;

        if (obj.type === 'textbox') {
          obj.set({
            backgroundColor: isActive
              ? 'rgba(59, 130, 246, 0.2)' // Darker blue when active
              : 'rgba(59, 130, 246, 0.1)', // Light blue when inactive
            borderColor: isActive ? '#3b82f6' : '#94a3b8',
            borderScaleFactor: isActive ? 3 : 2,
          });
        } else {
          // For image layers
          obj.set({
            borderColor: isActive ? '#3b82f6' : '#94a3b8',
            borderScaleFactor: isActive ? 3 : 2,
            opacity: isActive ? 1 : 0.8,
          });
        }
      }
    });

    editor.canvas.renderAll();
  }, [editor, templateData.editableLayers, activeLayerId]);

  // Update visuals when active layer changes
  useEffect(() => {
    updateEditableObjectsVisuals();
  }, [activeLayerId, updateEditableObjectsVisuals]);

  // Apply customizations to canvas
  useEffect(() => {
    if (!editor?.canvas || !isInitialized) return;

    const canvas = editor.canvas;

    templateData.editableLayers.forEach(layer => {
      const customValue = customizations[layer.id];
      if (!customValue || customValue === layer.originalValue) return;

      const canvasObject = canvas.getObjects().find((obj: any) => obj.id === layer.id);
      if (!canvasObject) return;

      if (layer.type === 'text' && canvasObject.type === 'textbox') {
        (canvasObject as fabric.Textbox).set('text', customValue);
      } else if (layer.type === 'image' && customValue.startsWith('blob:')) {
        fabric.Image.fromURL(customValue, (img) => {
          if (!canvas) return;

          // Scale image to fit the object bounds
          const scaleX = canvasObject.width! / img.width!;
          const scaleY = canvasObject.height! / img.height!;
          const scale = Math.min(scaleX, scaleY);

          img.set({
            left: canvasObject.left,
            top: canvasObject.top,
            scaleX: scale,
            scaleY: scale,
            originX: canvasObject.originX,
            originY: canvasObject.originY,
            selectable: true,
            hasControls: false,
            lockMovementX: true,
            lockMovementY: true,
            lockRotation: true,
            lockScalingX: true,
            lockScalingY: true,
            lockUniScaling: true,
          });

          // Set the ID using a custom property
          (img as any).id = layer.id;

          canvas.remove(canvasObject);
          canvas.add(img);
          canvas.renderAll();
          generatePreview();
        });
        return;
      }
    });

    canvas.renderAll();
    generatePreview();
  }, [customizations, editor, isInitialized, templateData.editableLayers]);

  // Generate preview from canvas
  const generatePreview = useCallback(() => {
    if (!editor?.canvas) return;

    try {
      const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
      if (!workspace) return;

      // Create a temporary canvas with just the workspace content
      const tempCanvas = new fabric.Canvas(null, {
        width: workspace.width,
        height: workspace.height,
      });

      // Clone all objects except the workspace itself
      const objectsToClone = editor.canvas.getObjects().filter((obj: any) => obj.name !== "clip");
      
      objectsToClone.forEach((obj) => {
        obj.clone((cloned: fabric.Object) => {
          // Adjust position relative to workspace
          cloned.set({
            left: (cloned.left || 0) - (workspace.left || 0),
            top: (cloned.top || 0) - (workspace.top || 0),
          });
          tempCanvas.add(cloned);
        });
      });

      // Generate preview after a short delay to ensure all objects are added
      setTimeout(() => {
        const dataUrl = tempCanvas.toDataURL({
          format: 'png',
          quality: 0.9,
          multiplier: 1,
        });
        onPreviewGenerated(dataUrl);
        tempCanvas.dispose();
      }, 100);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    }
  }, [editor, onPreviewGenerated]);

  // Handle text editing
  const handleTextEdit = useCallback((layerId: string, value: string) => {
    onCustomizationChange(layerId, value);
  }, [onCustomizationChange]);



  // Handle click events and layer activation
  useEffect(() => {
    if (!editor?.canvas) return;

    const handleObjectSelection = (e: fabric.IEvent) => {
      const target = e.target;
      if (!target) {
        setActiveLayerId(null);
        return;
      }

      const layerId = (target as any).id;
      const layer = templateData.editableLayers.find(l => l.id === layerId);

      if (layer) {
        console.log('Layer activated:', layer);
        setActiveLayerId(layerId);
        onLayerActivation?.(layerId);

        // Update visual feedback for all editable objects
        updateEditableObjectsVisuals();
      } else {
        setActiveLayerId(null);
        onLayerActivation?.(null);
      }
    };

    const handleCanvasClick = (e: fabric.IEvent) => {
      if (!e.target) {
        setActiveLayerId(null);
        onLayerActivation?.(null);
        updateEditableObjectsVisuals();
      }
    };

    const handleTextChanged = (e: fabric.IEvent) => {
      const target = e.target;
      if (!target || target.type !== 'textbox') return;

      const layerId = (target as any).id;
      const layer = templateData.editableLayers.find(l => l.id === layerId);
      if (!layer || layer.type !== 'text') return;

      const currentText = (target as fabric.Textbox).text || '';
      handleTextEdit(layerId, currentText);
      generatePreview();
    };

    const handleDoubleClick = (e: fabric.IEvent) => {
      const target = e.target;
      if (!target || target.type !== 'textbox') return;

      const layerId = (target as any).id;
      const layer = templateData.editableLayers.find(l => l.id === layerId);
      if (layer && layer.type === 'text') {
        setActiveLayerId(layerId);
        // Enter editing mode
        (target as fabric.Textbox).enterEditing();
        (target as fabric.Textbox).selectAll();
      }
    };

    editor.canvas.on('selection:created', handleObjectSelection);
    editor.canvas.on('selection:updated', handleObjectSelection);
    editor.canvas.on('selection:cleared', () => setActiveLayerId(null));
    editor.canvas.on('mouse:down', handleCanvasClick);
    editor.canvas.on('text:changed', handleTextChanged);
    editor.canvas.on('mouse:dblclick', handleDoubleClick);

    return () => {
      editor.canvas.off('selection:created', handleObjectSelection);
      editor.canvas.off('selection:updated', handleObjectSelection);
      editor.canvas.off('selection:cleared', () => setActiveLayerId(null));
      editor.canvas.off('mouse:down', handleCanvasClick);
      editor.canvas.off('text:changed', handleTextChanged);
      editor.canvas.off('mouse:dblclick', handleDoubleClick);
    };
  }, [editor, templateData.editableLayers, handleTextEdit, generatePreview]);

  return (
    <div className="h-full flex flex-col bg-muted">
      <div className="flex-1 relative">
        <div className="absolute inset-0" ref={containerRef}>
          <canvas ref={canvasRef} />
        </div>
      </div>
      <Footer editor={editor} />
    </div>
  );
};
