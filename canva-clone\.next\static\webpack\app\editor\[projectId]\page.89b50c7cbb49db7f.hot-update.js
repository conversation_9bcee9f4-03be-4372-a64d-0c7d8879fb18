"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts":
/*!**************************************************************!*\
  !*** ./src/features/editor/hooks/use-thumbnail-generator.ts ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useThumbnailGenerator: function() { return /* binding */ useThumbnailGenerator; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hono__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hono */ \"(app-pages-browser)/./src/lib/hono.ts\");\n\n\n\nconst useThumbnailGenerator = (param)=>{\n    let { editor, projectId, onThumbnailGenerated } = param;\n    const lastThumbnailRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const generateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!editor) {\n            console.warn(\"No editor available for thumbnail generation\");\n            return;\n        }\n        if (!editor.canvas) {\n            console.warn(\"No canvas available for thumbnail generation\");\n            return;\n        }\n        try {\n            console.log(\"Generating thumbnail for project:\", projectId);\n            // Generate thumbnail data URL with smaller size for testing\n            const thumbnailDataUrl = editor.generateThumbnail({\n                width: 300,\n                height: 200,\n                quality: 0.8\n            });\n            if (!thumbnailDataUrl) {\n                console.error(\"Failed to generate thumbnail data URL\");\n                return;\n            }\n            console.log(\"Generated thumbnail data URL length:\", thumbnailDataUrl.length);\n            console.log(\"Thumbnail data URL preview:\", thumbnailDataUrl.substring(0, 100) + \"...\");\n            // Skip if thumbnail hasn't changed significantly\n            if (lastThumbnailRef.current === thumbnailDataUrl) {\n                console.log(\"Thumbnail unchanged, skipping update\");\n                return;\n            }\n            lastThumbnailRef.current = thumbnailDataUrl;\n            console.log(\"Thumbnail generated, updating project...\");\n            // For now, let's store the thumbnail as a data URL directly in the database\n            // This is simpler and doesn't require external file uploads\n            // In production, you might want to upload to a CDN\n            // Update project with thumbnail data URL\n            const response = await _lib_hono__WEBPACK_IMPORTED_MODULE_2__.client.api.projects[\":id\"].$patch({\n                param: {\n                    id: projectId\n                },\n                json: {\n                    thumbnailUrl: thumbnailDataUrl\n                }\n            });\n            if (response.ok) {\n                console.log(\"Thumbnail updated successfully\");\n                const responseData = await response.json();\n                console.log(\"Response data:\", responseData);\n                onThumbnailGenerated === null || onThumbnailGenerated === void 0 ? void 0 : onThumbnailGenerated(thumbnailDataUrl);\n            } else {\n                console.error(\"Failed to update thumbnail:\", response.status, response.statusText);\n                const errorText = await response.text();\n                console.error(\"Error response:\", errorText);\n            }\n        } catch (error) {\n            console.error(\"Error generating thumbnail:\", error);\n        }\n    }, [\n        editor,\n        projectId,\n        onThumbnailGenerated\n    ]);\n    // Debounced version to avoid too frequent thumbnail generation\n    const debouncedGenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default()(generateThumbnail, 2000), [\n        generateThumbnail\n    ]);\n    const forceRegenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        // Reset the last thumbnail to force regeneration\n        lastThumbnailRef.current = null;\n        await generateThumbnail();\n    }, [\n        generateThumbnail\n    ]);\n    return {\n        generateThumbnail,\n        debouncedGenerateThumbnail,\n        forceRegenerateThumbnail\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLXRodW1ibmFpbC1nZW5lcmF0b3IudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRDO0FBQ0w7QUFHSDtBQVE3QixNQUFNSSx3QkFBd0I7UUFBQyxFQUNwQ0MsTUFBTSxFQUNOQyxTQUFTLEVBQ1RDLG9CQUFvQixFQUNPO0lBQzNCLE1BQU1DLG1CQUFtQlAsNkNBQU1BLENBQWdCO0lBRS9DLE1BQU1RLG9CQUFvQlQsa0RBQVdBLENBQUM7UUFDcEMsSUFBSSxDQUFDSyxRQUFRO1lBQ1hLLFFBQVFDLElBQUksQ0FBQztZQUNiO1FBQ0Y7UUFFQSxJQUFJLENBQUNOLE9BQU9PLE1BQU0sRUFBRTtZQUNsQkYsUUFBUUMsSUFBSSxDQUFDO1lBQ2I7UUFDRjtRQUVBLElBQUk7WUFDRkQsUUFBUUcsR0FBRyxDQUFDLHFDQUFxQ1A7WUFFakQsNERBQTREO1lBQzVELE1BQU1RLG1CQUFtQlQsT0FBT0ksaUJBQWlCLENBQUM7Z0JBQ2hETSxPQUFPO2dCQUNQQyxRQUFRO2dCQUNSQyxTQUFTO1lBQ1g7WUFFQSxJQUFJLENBQUNILGtCQUFrQjtnQkFDckJKLFFBQVFRLEtBQUssQ0FBQztnQkFDZDtZQUNGO1lBRUFSLFFBQVFHLEdBQUcsQ0FBQyx3Q0FBd0NDLGlCQUFpQkssTUFBTTtZQUMzRVQsUUFBUUcsR0FBRyxDQUFDLCtCQUErQkMsaUJBQWlCTSxTQUFTLENBQUMsR0FBRyxPQUFPO1lBRWhGLGlEQUFpRDtZQUNqRCxJQUFJWixpQkFBaUJhLE9BQU8sS0FBS1Asa0JBQWtCO2dCQUNqREosUUFBUUcsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQUwsaUJBQWlCYSxPQUFPLEdBQUdQO1lBQzNCSixRQUFRRyxHQUFHLENBQUM7WUFFWiw0RUFBNEU7WUFDNUUsNERBQTREO1lBQzVELG1EQUFtRDtZQUVuRCx5Q0FBeUM7WUFDekMsTUFBTVMsV0FBVyxNQUFNbkIsNkNBQU1BLENBQUNvQixHQUFHLENBQUNDLFFBQVEsQ0FBQyxNQUFNLENBQUNDLE1BQU0sQ0FBQztnQkFDdkRDLE9BQU87b0JBQUVDLElBQUlyQjtnQkFBVTtnQkFDdkJzQixNQUFNO29CQUNKQyxjQUFjZjtnQkFDaEI7WUFDRjtZQUVBLElBQUlRLFNBQVNRLEVBQUUsRUFBRTtnQkFDZnBCLFFBQVFHLEdBQUcsQ0FBQztnQkFDWixNQUFNa0IsZUFBZSxNQUFNVCxTQUFTTSxJQUFJO2dCQUN4Q2xCLFFBQVFHLEdBQUcsQ0FBQyxrQkFBa0JrQjtnQkFDOUJ4QixpQ0FBQUEsMkNBQUFBLHFCQUF1Qk87WUFDekIsT0FBTztnQkFDTEosUUFBUVEsS0FBSyxDQUFDLCtCQUErQkksU0FBU1UsTUFBTSxFQUFFVixTQUFTVyxVQUFVO2dCQUNqRixNQUFNQyxZQUFZLE1BQU1aLFNBQVNhLElBQUk7Z0JBQ3JDekIsUUFBUVEsS0FBSyxDQUFDLG1CQUFtQmdCO1lBQ25DO1FBQ0YsRUFBRSxPQUFPaEIsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsK0JBQStCQTtRQUMvQztJQUNGLEdBQUc7UUFBQ2I7UUFBUUM7UUFBV0M7S0FBcUI7SUFFNUMsK0RBQStEO0lBQy9ELE1BQU02Qiw2QkFBNkJwQyxrREFBV0EsQ0FDNUNFLHNEQUFRQSxDQUFDTyxtQkFBbUIsT0FDNUI7UUFBQ0E7S0FBa0I7SUFHckIsTUFBTTRCLDJCQUEyQnJDLGtEQUFXQSxDQUFDO1FBQzNDLGlEQUFpRDtRQUNqRFEsaUJBQWlCYSxPQUFPLEdBQUc7UUFDM0IsTUFBTVo7SUFDUixHQUFHO1FBQUNBO0tBQWtCO0lBRXRCLE9BQU87UUFDTEE7UUFDQTJCO1FBQ0FDO0lBQ0Y7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLXRodW1ibmFpbC1nZW5lcmF0b3IudHM/ODc2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgZGVib3VuY2UgZnJvbSBcImxvZGFzaC5kZWJvdW5jZVwiO1xuXG5pbXBvcnQgeyBFZGl0b3IgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvdHlwZXNcIjtcbmltcG9ydCB7IGNsaWVudCB9IGZyb20gXCJAL2xpYi9ob25vXCI7XG5cbmludGVyZmFjZSBVc2VUaHVtYm5haWxHZW5lcmF0b3JQcm9wcyB7XG4gIGVkaXRvcjogRWRpdG9yIHwgdW5kZWZpbmVkO1xuICBwcm9qZWN0SWQ6IHN0cmluZztcbiAgb25UaHVtYm5haWxHZW5lcmF0ZWQ/OiAodGh1bWJuYWlsVXJsOiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBjb25zdCB1c2VUaHVtYm5haWxHZW5lcmF0b3IgPSAoe1xuICBlZGl0b3IsXG4gIHByb2plY3RJZCxcbiAgb25UaHVtYm5haWxHZW5lcmF0ZWQsXG59OiBVc2VUaHVtYm5haWxHZW5lcmF0b3JQcm9wcykgPT4ge1xuICBjb25zdCBsYXN0VGh1bWJuYWlsUmVmID0gdXNlUmVmPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IGdlbmVyYXRlVGh1bWJuYWlsID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghZWRpdG9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oXCJObyBlZGl0b3IgYXZhaWxhYmxlIGZvciB0aHVtYm5haWwgZ2VuZXJhdGlvblwiKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoIWVkaXRvci5jYW52YXMpIHtcbiAgICAgIGNvbnNvbGUud2FybihcIk5vIGNhbnZhcyBhdmFpbGFibGUgZm9yIHRodW1ibmFpbCBnZW5lcmF0aW9uXCIpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZyhcIkdlbmVyYXRpbmcgdGh1bWJuYWlsIGZvciBwcm9qZWN0OlwiLCBwcm9qZWN0SWQpO1xuXG4gICAgICAvLyBHZW5lcmF0ZSB0aHVtYm5haWwgZGF0YSBVUkwgd2l0aCBzbWFsbGVyIHNpemUgZm9yIHRlc3RpbmdcbiAgICAgIGNvbnN0IHRodW1ibmFpbERhdGFVcmwgPSBlZGl0b3IuZ2VuZXJhdGVUaHVtYm5haWwoe1xuICAgICAgICB3aWR0aDogMzAwLFxuICAgICAgICBoZWlnaHQ6IDIwMCxcbiAgICAgICAgcXVhbGl0eTogMC44LFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghdGh1bWJuYWlsRGF0YVVybCkge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGdlbmVyYXRlIHRodW1ibmFpbCBkYXRhIFVSTFwiKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhcIkdlbmVyYXRlZCB0aHVtYm5haWwgZGF0YSBVUkwgbGVuZ3RoOlwiLCB0aHVtYm5haWxEYXRhVXJsLmxlbmd0aCk7XG4gICAgICBjb25zb2xlLmxvZyhcIlRodW1ibmFpbCBkYXRhIFVSTCBwcmV2aWV3OlwiLCB0aHVtYm5haWxEYXRhVXJsLnN1YnN0cmluZygwLCAxMDApICsgXCIuLi5cIik7XG5cbiAgICAgIC8vIFNraXAgaWYgdGh1bWJuYWlsIGhhc24ndCBjaGFuZ2VkIHNpZ25pZmljYW50bHlcbiAgICAgIGlmIChsYXN0VGh1bWJuYWlsUmVmLmN1cnJlbnQgPT09IHRodW1ibmFpbERhdGFVcmwpIHtcbiAgICAgICAgY29uc29sZS5sb2coXCJUaHVtYm5haWwgdW5jaGFuZ2VkLCBza2lwcGluZyB1cGRhdGVcIik7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgbGFzdFRodW1ibmFpbFJlZi5jdXJyZW50ID0gdGh1bWJuYWlsRGF0YVVybDtcbiAgICAgIGNvbnNvbGUubG9nKFwiVGh1bWJuYWlsIGdlbmVyYXRlZCwgdXBkYXRpbmcgcHJvamVjdC4uLlwiKTtcblxuICAgICAgLy8gRm9yIG5vdywgbGV0J3Mgc3RvcmUgdGhlIHRodW1ibmFpbCBhcyBhIGRhdGEgVVJMIGRpcmVjdGx5IGluIHRoZSBkYXRhYmFzZVxuICAgICAgLy8gVGhpcyBpcyBzaW1wbGVyIGFuZCBkb2Vzbid0IHJlcXVpcmUgZXh0ZXJuYWwgZmlsZSB1cGxvYWRzXG4gICAgICAvLyBJbiBwcm9kdWN0aW9uLCB5b3UgbWlnaHQgd2FudCB0byB1cGxvYWQgdG8gYSBDRE5cblxuICAgICAgLy8gVXBkYXRlIHByb2plY3Qgd2l0aCB0aHVtYm5haWwgZGF0YSBVUkxcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xpZW50LmFwaS5wcm9qZWN0c1tcIjppZFwiXS4kcGF0Y2goe1xuICAgICAgICBwYXJhbTogeyBpZDogcHJvamVjdElkIH0sXG4gICAgICAgIGpzb246IHtcbiAgICAgICAgICB0aHVtYm5haWxVcmw6IHRodW1ibmFpbERhdGFVcmwsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiVGh1bWJuYWlsIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5XCIpO1xuICAgICAgICBjb25zdCByZXNwb25zZURhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiUmVzcG9uc2UgZGF0YTpcIiwgcmVzcG9uc2VEYXRhKTtcbiAgICAgICAgb25UaHVtYm5haWxHZW5lcmF0ZWQ/Lih0aHVtYm5haWxEYXRhVXJsKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gdXBkYXRlIHRodW1ibmFpbDpcIiwgcmVzcG9uc2Uuc3RhdHVzLCByZXNwb25zZS5zdGF0dXNUZXh0KTtcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgcmVzcG9uc2U6XCIsIGVycm9yVGV4dCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIHRodW1ibmFpbDpcIiwgZXJyb3IpO1xuICAgIH1cbiAgfSwgW2VkaXRvciwgcHJvamVjdElkLCBvblRodW1ibmFpbEdlbmVyYXRlZF0pO1xuXG4gIC8vIERlYm91bmNlZCB2ZXJzaW9uIHRvIGF2b2lkIHRvbyBmcmVxdWVudCB0aHVtYm5haWwgZ2VuZXJhdGlvblxuICBjb25zdCBkZWJvdW5jZWRHZW5lcmF0ZVRodW1ibmFpbCA9IHVzZUNhbGxiYWNrKFxuICAgIGRlYm91bmNlKGdlbmVyYXRlVGh1bWJuYWlsLCAyMDAwKSwgLy8gR2VuZXJhdGUgdGh1bWJuYWlsIDIgc2Vjb25kcyBhZnRlciBsYXN0IGNoYW5nZVxuICAgIFtnZW5lcmF0ZVRodW1ibmFpbF1cbiAgKTtcblxuICBjb25zdCBmb3JjZVJlZ2VuZXJhdGVUaHVtYm5haWwgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgLy8gUmVzZXQgdGhlIGxhc3QgdGh1bWJuYWlsIHRvIGZvcmNlIHJlZ2VuZXJhdGlvblxuICAgIGxhc3RUaHVtYm5haWxSZWYuY3VycmVudCA9IG51bGw7XG4gICAgYXdhaXQgZ2VuZXJhdGVUaHVtYm5haWwoKTtcbiAgfSwgW2dlbmVyYXRlVGh1bWJuYWlsXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBnZW5lcmF0ZVRodW1ibmFpbCxcbiAgICBkZWJvdW5jZWRHZW5lcmF0ZVRodW1ibmFpbCxcbiAgICBmb3JjZVJlZ2VuZXJhdGVUaHVtYm5haWwsXG4gIH07XG59O1xuIl0sIm5hbWVzIjpbInVzZUNhbGxiYWNrIiwidXNlUmVmIiwiZGVib3VuY2UiLCJjbGllbnQiLCJ1c2VUaHVtYm5haWxHZW5lcmF0b3IiLCJlZGl0b3IiLCJwcm9qZWN0SWQiLCJvblRodW1ibmFpbEdlbmVyYXRlZCIsImxhc3RUaHVtYm5haWxSZWYiLCJnZW5lcmF0ZVRodW1ibmFpbCIsImNvbnNvbGUiLCJ3YXJuIiwiY2FudmFzIiwibG9nIiwidGh1bWJuYWlsRGF0YVVybCIsIndpZHRoIiwiaGVpZ2h0IiwicXVhbGl0eSIsImVycm9yIiwibGVuZ3RoIiwic3Vic3RyaW5nIiwiY3VycmVudCIsInJlc3BvbnNlIiwiYXBpIiwicHJvamVjdHMiLCIkcGF0Y2giLCJwYXJhbSIsImlkIiwianNvbiIsInRodW1ibmFpbFVybCIsIm9rIiwicmVzcG9uc2VEYXRhIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsImVycm9yVGV4dCIsInRleHQiLCJkZWJvdW5jZWRHZW5lcmF0ZVRodW1ibmFpbCIsImZvcmNlUmVnZW5lcmF0ZVRodW1ibmFpbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\n"));

/***/ })

});