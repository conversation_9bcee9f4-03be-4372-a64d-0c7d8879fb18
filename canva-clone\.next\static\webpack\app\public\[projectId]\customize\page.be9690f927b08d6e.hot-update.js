"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated, activeLayerId: externalActiveLayerId, onLayerActivation } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeLayerId, setActiveLayerId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(externalActiveLayerId || null);\n    // Sync with external active layer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setActiveLayerId(externalActiveLayerId || null);\n    }, [\n        externalActiveLayerId\n    ]);\n    // Initialize editor with no-save callback since this is read-only customization\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{},\n        saveCallback: ()=>{}\n    });\n    // Initialize canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (retryCanvas) {\n                    setIsInitialized(true);\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        } else {\n            setIsInitialized(true);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Lock non-editable objects and setup customization mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up customization mode:\", {\n            editableLayerIds,\n            totalObjects: canvas.getObjects().length,\n            editableLayers: templateData.editableLayers\n        });\n        // Lock all objects except editable ones\n        canvas.getObjects().forEach((obj)=>{\n            console.log(\"Processing object:\", {\n                id: obj.id,\n                type: obj.type,\n                name: obj.name,\n                isEditable: editableLayerIds.includes(obj.id)\n            });\n            if (obj.name === \"clip\") {\n                // Keep workspace as is but make it non-selectable\n                obj.set({\n                    selectable: false,\n                    evented: false\n                });\n                return;\n            }\n            if (editableLayerIds.includes(obj.id)) {\n                console.log(\"Making object editable:\", obj.id, obj.type);\n                // Make editable objects selectable and editable\n                obj.set({\n                    selectable: true,\n                    hasControls: false,\n                    hasBorders: true,\n                    borderColor: \"#3b82f6\",\n                    borderScaleFactor: 2,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true,\n                    editable: obj.type === \"textbox\" ? true : false,\n                    hoverCursor: \"pointer\",\n                    moveCursor: \"pointer\"\n                });\n                // Add visual indicator for editable elements\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: \"rgba(59, 130, 246, 0.1)\"\n                    });\n                }\n            } else {\n                // Lock non-editable objects completely\n                obj.set({\n                    selectable: false,\n                    evented: false,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n            }\n        });\n        canvas.renderAll();\n    }, [\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Update visuals when active layer changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateEditableObjectsVisuals();\n    }, [\n        activeLayerId,\n        updateEditableObjectsVisuals\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue || customValue === layer.originalValue) return;\n            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                canvasObject.set(\"text\", customValue);\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        id: layer.id,\n                        selectable: true,\n                        hasControls: false,\n                        lockMovementX: true,\n                        lockMovementY: true,\n                        lockRotation: true,\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true\n                    });\n                    canvas.remove(canvasObject);\n                    canvas.add(img);\n                    canvas.renderAll();\n                    generatePreview();\n                });\n                return;\n            }\n        });\n        canvas.renderAll();\n        generatePreview();\n    }, [\n        customizations,\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Generate preview from canvas\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            // Create a temporary canvas with just the workspace content\n            const tempCanvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(null, {\n                width: workspace.width,\n                height: workspace.height\n            });\n            // Clone all objects except the workspace itself\n            const objectsToClone = editor.canvas.getObjects().filter((obj)=>obj.name !== \"clip\");\n            objectsToClone.forEach((obj)=>{\n                obj.clone((cloned)=>{\n                    // Adjust position relative to workspace\n                    cloned.set({\n                        left: (cloned.left || 0) - (workspace.left || 0),\n                        top: (cloned.top || 0) - (workspace.top || 0)\n                    });\n                    tempCanvas.add(cloned);\n                });\n            });\n            // Generate preview after a short delay to ensure all objects are added\n            setTimeout(()=>{\n                const dataUrl = tempCanvas.toDataURL({\n                    format: \"png\",\n                    quality: 0.9,\n                    multiplier: 1\n                });\n                onPreviewGenerated(dataUrl);\n                tempCanvas.dispose();\n            }, 100);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Handle text editing\n    const handleTextEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((layerId, value)=>{\n        onCustomizationChange(layerId, value);\n    }, [\n        onCustomizationChange\n    ]);\n    // Update visual feedback for editable objects\n    const updateEditableObjectsVisuals = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        editor.canvas.getObjects().forEach((obj)=>{\n            if (editableLayerIds.includes(obj.id)) {\n                const isActive = obj.id === activeLayerId;\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: isActive ? \"rgba(59, 130, 246, 0.2)\" // Darker blue when active\n                         : \"rgba(59, 130, 246, 0.1)\",\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2\n                    });\n                } else {\n                    // For image layers\n                    obj.set({\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2,\n                        opacity: isActive ? 1 : 0.8\n                    });\n                }\n            }\n        });\n        editor.canvas.renderAll();\n    }, [\n        editor,\n        templateData.editableLayers,\n        activeLayerId\n    ]);\n    // Handle click events and layer activation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleObjectSelection = (e)=>{\n            const target = e.target;\n            if (!target) {\n                setActiveLayerId(null);\n                return;\n            }\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer) {\n                console.log(\"Layer activated:\", layer);\n                setActiveLayerId(layerId);\n                // Update visual feedback for all editable objects\n                updateEditableObjectsVisuals();\n            } else {\n                setActiveLayerId(null);\n            }\n        };\n        const handleCanvasClick = (e)=>{\n            if (!e.target) {\n                setActiveLayerId(null);\n                updateEditableObjectsVisuals();\n            }\n        };\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (!layer || layer.type !== \"text\") return;\n            const currentText = target.text || \"\";\n            handleTextEdit(layerId, currentText);\n            generatePreview();\n        };\n        const handleDoubleClick = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer && layer.type === \"text\") {\n                setActiveLayerId(layerId);\n                // Enter editing mode\n                target.enterEditing();\n                target.selectAll();\n            }\n        };\n        editor.canvas.on(\"selection:created\", handleObjectSelection);\n        editor.canvas.on(\"selection:updated\", handleObjectSelection);\n        editor.canvas.on(\"selection:cleared\", ()=>setActiveLayerId(null));\n        editor.canvas.on(\"mouse:down\", handleCanvasClick);\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        editor.canvas.on(\"mouse:dblclick\", handleDoubleClick);\n        return ()=>{\n            editor.canvas.off(\"selection:created\", handleObjectSelection);\n            editor.canvas.off(\"selection:updated\", handleObjectSelection);\n            editor.canvas.off(\"selection:cleared\", ()=>setActiveLayerId(null));\n            editor.canvas.off(\"mouse:down\", handleCanvasClick);\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n            editor.canvas.off(\"mouse:dblclick\", handleDoubleClick);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        handleTextEdit,\n        generatePreview\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    ref: containerRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 393,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 392,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"FBNHhm+LxbLIaSPIwz2oPjWZUFY=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});