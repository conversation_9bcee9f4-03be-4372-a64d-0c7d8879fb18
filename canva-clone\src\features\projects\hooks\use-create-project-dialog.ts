import { create } from "zustand";

interface CreateProjectDialogState {
  isOpen: boolean;
  width: number;
  height: number;
  json: string;
  onOpen: (options?: { width?: number; height?: number; json?: string }) => void;
  onClose: () => void;
}

export const useCreateProjectDialog = create<CreateProjectDialogState>((set) => ({
  isOpen: false,
  width: 900,
  height: 1200,
  json: "",
  onOpen: (options = {}) => set({
    isOpen: true,
    width: options.width ?? 900,
    height: options.height ?? 1200,
    json: options.json ?? "",
  }),
  onClose: () => set({
    isOpen: false,
    width: 900,
    height: 1200,
    json: "",
  }),
}));
