"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize editor with no-save callback since this is read-only customization\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{},\n        saveCallback: ()=>{}\n    });\n    // Initialize canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (retryCanvas) {\n                    setIsInitialized(true);\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        } else {\n            setIsInitialized(true);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Lock non-editable objects and setup customization mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        // Lock all objects except editable ones\n        canvas.getObjects().forEach((obj)=>{\n            if (obj.name === \"clip\") {\n                // Keep workspace as is but make it non-selectable\n                obj.set({\n                    selectable: false,\n                    evented: false\n                });\n                return;\n            }\n            if (editableLayerIds.includes(obj.id)) {\n                // Make editable objects selectable and editable\n                obj.set({\n                    selectable: true,\n                    hasControls: obj.type === \"textbox\" ? false : true,\n                    hasBorders: true,\n                    lockMovementX: obj.type === \"textbox\" ? true : false,\n                    lockMovementY: obj.type === \"textbox\" ? true : false,\n                    lockRotation: true,\n                    lockScalingX: obj.type === \"textbox\" ? true : false,\n                    lockScalingY: obj.type === \"textbox\" ? true : false,\n                    lockUniScaling: obj.type === \"textbox\" ? true : false,\n                    editable: obj.type === \"textbox\" ? true : false\n                });\n            } else {\n                // Lock non-editable objects completely\n                obj.set({\n                    selectable: false,\n                    evented: false,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n            }\n        });\n        canvas.renderAll();\n    }, [\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue || customValue === layer.originalValue) return;\n            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                canvasObject.set(\"text\", customValue);\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        id: layer.id,\n                        selectable: true,\n                        hasControls: false,\n                        lockMovementX: true,\n                        lockMovementY: true,\n                        lockRotation: true,\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true\n                    });\n                    canvas.remove(canvasObject);\n                    canvas.add(img);\n                    canvas.renderAll();\n                    generatePreview();\n                });\n                return;\n            }\n        });\n        canvas.renderAll();\n        generatePreview();\n    }, [\n        customizations,\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Generate preview from canvas\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            // Create a temporary canvas with just the workspace content\n            const tempCanvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(null, {\n                width: workspace.width,\n                height: workspace.height\n            });\n            // Clone all objects except the workspace itself\n            const objectsToClone = editor.canvas.getObjects().filter((obj)=>obj.name !== \"clip\");\n            objectsToClone.forEach((obj)=>{\n                obj.clone((cloned)=>{\n                    // Adjust position relative to workspace\n                    cloned.set({\n                        left: (cloned.left || 0) - (workspace.left || 0),\n                        top: (cloned.top || 0) - (workspace.top || 0)\n                    });\n                    tempCanvas.add(cloned);\n                });\n            });\n            // Generate preview after a short delay to ensure all objects are added\n            setTimeout(()=>{\n                const dataUrl = tempCanvas.toDataURL({\n                    format: \"png\",\n                    quality: 0.9,\n                    multiplier: 1\n                });\n                onPreviewGenerated(dataUrl);\n                tempCanvas.dispose();\n            }, 100);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Handle text editing\n    const handleTextEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((layerId, value)=>{\n        onCustomizationChange(layerId, value);\n    }, [\n        onCustomizationChange\n    ]);\n    // Handle text editing events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (!layer || layer.type !== \"text\") return;\n            const currentText = target.text || \"\";\n            handleTextEdit(layerId, currentText);\n            generatePreview();\n        };\n        const handleDoubleClick = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer && layer.type === \"text\") {\n                // Enter editing mode\n                target.enterEditing();\n                target.selectAll();\n            }\n        };\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        editor.canvas.on(\"mouse:dblclick\", handleDoubleClick);\n        return ()=>{\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n            editor.canvas.off(\"mouse:dblclick\", handleDoubleClick);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        handleTextEdit,\n        generatePreview\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    ref: containerRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 283,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"KtmFUFpCV+LsnLVV1hCHd/SkL/U=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});