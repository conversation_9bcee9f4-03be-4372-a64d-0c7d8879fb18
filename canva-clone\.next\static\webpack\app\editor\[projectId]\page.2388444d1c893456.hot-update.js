"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts":
/*!******************************************************!*\
  !*** ./src/features/editor/hooks/use-auto-resize.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoResize: function() { return /* binding */ useAutoResize; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst useAutoResize = (param)=>{\n    let { canvas, container } = param;\n    const autoZoom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!canvas || !container) return;\n        const width = container.offsetWidth;\n        const height = container.offsetHeight;\n        canvas.setWidth(width);\n        canvas.setHeight(height);\n        const center = canvas.getCenter();\n        const zoomRatio = 0.85;\n        const localWorkspace = canvas.getObjects().find((object)=>object.name === \"clip\");\n        if (!localWorkspace) return;\n        // @ts-ignore\n        const scale = fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.util.findScaleToFit(localWorkspace, {\n            width: width,\n            height: height\n        });\n        const zoom = zoomRatio * scale;\n        canvas.setViewportTransform(fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.iMatrix.concat());\n        canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoom);\n        const workspaceCenter = localWorkspace.getCenterPoint();\n        const viewportTransform = canvas.viewportTransform;\n        if (canvas.width === undefined || canvas.height === undefined || !viewportTransform) {\n            return;\n        }\n        viewportTransform[4] = canvas.width / 2 - workspaceCenter.x * viewportTransform[0];\n        viewportTransform[5] = canvas.height / 2 - workspaceCenter.y * viewportTransform[3];\n        canvas.setViewportTransform(viewportTransform);\n        localWorkspace.clone((cloned)=>{\n            canvas.clipPath = cloned;\n            canvas.requestRenderAll();\n        });\n    }, [\n        canvas,\n        container\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let resizeObserver = null;\n        if (canvas && container) {\n            resizeObserver = new ResizeObserver(()=>{\n                autoZoom();\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        canvas,\n        container,\n        autoZoom\n    ]);\n    return {\n        autoZoom\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\n"));

/***/ })

});