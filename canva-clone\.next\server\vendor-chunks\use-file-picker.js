"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-file-picker";
exports.ids = ["vendor-chunks/use-file-picker"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-file-picker/dist/_rollupPluginBabelHelpers-4e04b055.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/use-file-picker/dist/_rollupPluginBabelHelpers-4e04b055.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: () => (/* binding */ _asyncToGenerator),\n/* harmony export */   a: () => (/* binding */ _regeneratorRuntime),\n/* harmony export */   b: () => (/* binding */ _extends),\n/* harmony export */   c: () => (/* binding */ _objectDestructuringEmpty),\n/* harmony export */   d: () => (/* binding */ _inheritsLoose)\n/* harmony export */ });\nfunction _regeneratorRuntime() {\n  _regeneratorRuntime = function () {\n    return exports;\n  };\n  var exports = {},\n    Op = Object.prototype,\n    hasOwn = Op.hasOwnProperty,\n    defineProperty = Object.defineProperty || function (obj, key, desc) {\n      obj[key] = desc.value;\n    },\n    $Symbol = \"function\" == typeof Symbol ? Symbol : {},\n    iteratorSymbol = $Symbol.iterator || \"@@iterator\",\n    asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\",\n    toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n  function define(obj, key, value) {\n    return Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }), obj[key];\n  }\n  try {\n    define({}, \"\");\n  } catch (err) {\n    define = function (obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator,\n      generator = Object.create(protoGenerator.prototype),\n      context = new Context(tryLocsList || []);\n    return defineProperty(generator, \"_invoke\", {\n      value: makeInvokeMethod(innerFn, self, context)\n    }), generator;\n  }\n  function tryCatch(fn, obj, arg) {\n    try {\n      return {\n        type: \"normal\",\n        arg: fn.call(obj, arg)\n      };\n    } catch (err) {\n      return {\n        type: \"throw\",\n        arg: err\n      };\n    }\n  }\n  exports.wrap = wrap;\n  var ContinueSentinel = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n  var getProto = Object.getPrototypeOf,\n    NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype);\n  var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype);\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function (method) {\n      define(prototype, method, function (arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (\"throw\" !== record.type) {\n        var result = record.arg,\n          value = result.value;\n        return value && \"object\" == typeof value && hasOwn.call(value, \"__await\") ? PromiseImpl.resolve(value.__await).then(function (value) {\n          invoke(\"next\", value, resolve, reject);\n        }, function (err) {\n          invoke(\"throw\", err, resolve, reject);\n        }) : PromiseImpl.resolve(value).then(function (unwrapped) {\n          result.value = unwrapped, resolve(result);\n        }, function (error) {\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n      reject(record.arg);\n    }\n    var previousPromise;\n    defineProperty(this, \"_invoke\", {\n      value: function (method, arg) {\n        function callInvokeWithMethodAndArg() {\n          return new PromiseImpl(function (resolve, reject) {\n            invoke(method, arg, resolve, reject);\n          });\n        }\n        return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n      }\n    });\n  }\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = \"suspendedStart\";\n    return function (method, arg) {\n      if (\"executing\" === state) throw new Error(\"Generator is already running\");\n      if (\"completed\" === state) {\n        if (\"throw\" === method) throw arg;\n        return doneResult();\n      }\n      for (context.method = method, context.arg = arg;;) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n        if (\"next\" === context.method) context.sent = context._sent = context.arg;else if (\"throw\" === context.method) {\n          if (\"suspendedStart\" === state) throw state = \"completed\", context.arg;\n          context.dispatchException(context.arg);\n        } else \"return\" === context.method && context.abrupt(\"return\", context.arg);\n        state = \"executing\";\n        var record = tryCatch(innerFn, self, context);\n        if (\"normal\" === record.type) {\n          if (state = context.done ? \"completed\" : \"suspendedYield\", record.arg === ContinueSentinel) continue;\n          return {\n            value: record.arg,\n            done: context.done\n          };\n        }\n        \"throw\" === record.type && (state = \"completed\", context.method = \"throw\", context.arg = record.arg);\n      }\n    };\n  }\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method,\n      method = delegate.iterator[methodName];\n    if (undefined === method) return context.delegate = null, \"throw\" === methodName && delegate.iterator.return && (context.method = \"return\", context.arg = undefined, maybeInvokeDelegate(delegate, context), \"throw\" === context.method) || \"return\" !== methodName && (context.method = \"throw\", context.arg = new TypeError(\"The iterator does not provide a '\" + methodName + \"' method\")), ContinueSentinel;\n    var record = tryCatch(method, delegate.iterator, context.arg);\n    if (\"throw\" === record.type) return context.method = \"throw\", context.arg = record.arg, context.delegate = null, ContinueSentinel;\n    var info = record.arg;\n    return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, \"return\" !== context.method && (context.method = \"next\", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = \"throw\", context.arg = new TypeError(\"iterator result is not an object\"), context.delegate = null, ContinueSentinel);\n  }\n  function pushTryEntry(locs) {\n    var entry = {\n      tryLoc: locs[0]\n    };\n    1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry);\n  }\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\", delete record.arg, entry.completion = record;\n  }\n  function Context(tryLocsList) {\n    this.tryEntries = [{\n      tryLoc: \"root\"\n    }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0);\n  }\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) return iteratorMethod.call(iterable);\n      if (\"function\" == typeof iterable.next) return iterable;\n      if (!isNaN(iterable.length)) {\n        var i = -1,\n          next = function next() {\n            for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next;\n            return next.value = undefined, next.done = !0, next;\n          };\n        return next.next = next;\n      }\n    }\n    return {\n      next: doneResult\n    };\n  }\n  function doneResult() {\n    return {\n      value: undefined,\n      done: !0\n    };\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, \"constructor\", {\n    value: GeneratorFunctionPrototype,\n    configurable: !0\n  }), defineProperty(GeneratorFunctionPrototype, \"constructor\", {\n    value: GeneratorFunction,\n    configurable: !0\n  }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, \"GeneratorFunction\"), exports.isGeneratorFunction = function (genFun) {\n    var ctor = \"function\" == typeof genFun && genFun.constructor;\n    return !!ctor && (ctor === GeneratorFunction || \"GeneratorFunction\" === (ctor.displayName || ctor.name));\n  }, exports.mark = function (genFun) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, \"GeneratorFunction\")), genFun.prototype = Object.create(Gp), genFun;\n  }, exports.awrap = function (arg) {\n    return {\n      __await: arg\n    };\n  }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    void 0 === PromiseImpl && (PromiseImpl = Promise);\n    var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl);\n    return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) {\n      return result.done ? result.value : iter.next();\n    });\n  }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, \"Generator\"), define(Gp, iteratorSymbol, function () {\n    return this;\n  }), define(Gp, \"toString\", function () {\n    return \"[object Generator]\";\n  }), exports.keys = function (val) {\n    var object = Object(val),\n      keys = [];\n    for (var key in object) keys.push(key);\n    return keys.reverse(), function next() {\n      for (; keys.length;) {\n        var key = keys.pop();\n        if (key in object) return next.value = key, next.done = !1, next;\n      }\n      return next.done = !0, next;\n    };\n  }, exports.values = values, Context.prototype = {\n    constructor: Context,\n    reset: function (skipTempReset) {\n      if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) \"t\" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined);\n    },\n    stop: function () {\n      this.done = !0;\n      var rootRecord = this.tryEntries[0].completion;\n      if (\"throw\" === rootRecord.type) throw rootRecord.arg;\n      return this.rval;\n    },\n    dispatchException: function (exception) {\n      if (this.done) throw exception;\n      var context = this;\n      function handle(loc, caught) {\n        return record.type = \"throw\", record.arg = exception, context.next = loc, caught && (context.method = \"next\", context.arg = undefined), !!caught;\n      }\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i],\n          record = entry.completion;\n        if (\"root\" === entry.tryLoc) return handle(\"end\");\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\"),\n            hasFinally = hasOwn.call(entry, \"finallyLoc\");\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0);\n            if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc);\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0);\n          } else {\n            if (!hasFinally) throw new Error(\"try statement without catch or finally\");\n            if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc);\n          }\n        }\n      }\n    },\n    abrupt: function (type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev && hasOwn.call(entry, \"finallyLoc\") && this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n      finallyEntry && (\"break\" === type || \"continue\" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null);\n      var record = finallyEntry ? finallyEntry.completion : {};\n      return record.type = type, record.arg = arg, finallyEntry ? (this.method = \"next\", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record);\n    },\n    complete: function (record, afterLoc) {\n      if (\"throw\" === record.type) throw record.arg;\n      return \"break\" === record.type || \"continue\" === record.type ? this.next = record.arg : \"return\" === record.type ? (this.rval = this.arg = record.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel;\n    },\n    finish: function (finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel;\n      }\n    },\n    catch: function (tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (\"throw\" === record.type) {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n      throw new Error(\"illegal catch attempt\");\n    },\n    delegateYield: function (iterable, resultName, nextLoc) {\n      return this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      }, \"next\" === this.method && (this.arg = undefined), ContinueSentinel;\n    }\n  }, exports;\n}\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\nfunction _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n      args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n      _next(undefined);\n    });\n  };\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _objectDestructuringEmpty(obj) {\n  if (obj == null) throw new TypeError(\"Cannot destructure \" + obj);\n}\n\n\n//# sourceMappingURL=_rollupPluginBabelHelpers-4e04b055.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-file-picker/dist/_rollupPluginBabelHelpers-4e04b055.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-file-picker/dist/index.esm.js":
/*!********************************************************!*\
  !*** ./node_modules/use-file-picker/dist/index.esm.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFilePicker: () => (/* binding */ useFilePicker),\n/* harmony export */   useImperativeFilePicker: () => (/* binding */ useImperativeFilePicker)\n/* harmony export */ });\n/* harmony import */ var _rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_rollupPluginBabelHelpers-4e04b055.js */ \"(ssr)/./node_modules/use-file-picker/dist/_rollupPluginBabelHelpers-4e04b055.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var file_selector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! file-selector */ \"(ssr)/./node_modules/use-file-picker/node_modules/file-selector/dist/es5/index.js\");\n\n\n\n\nfunction openFileDialog(accept, multiple, callback, initializeWithCustomAttributes) {\n  // this function must be called from a user\n  // activation event (ie an onclick event)\n  // Create an input element\n  var inputElement = document.createElement('input');\n  // Hide element and append to body (required to run on iOS safari)\n  inputElement.style.display = 'none';\n  document.body.appendChild(inputElement);\n  // Set its type to file\n  inputElement.type = 'file';\n  // Set accept to the file types you want the user to select.\n  // Include both the file extension and the mime type\n  // if accept is \"*\" then dont set the accept attribute\n  if (accept !== '*') inputElement.accept = accept;\n  // Accept multiple files\n  inputElement.multiple = multiple;\n  // set onchange event to call callback when user has selected file\n  //inputElement.addEventListener('change', callback);\n  inputElement.addEventListener('change', function (arg) {\n    callback(arg);\n    // remove element\n    document.body.removeChild(inputElement);\n  });\n  if (initializeWithCustomAttributes) {\n    initializeWithCustomAttributes(inputElement);\n  }\n  // dispatch a click event to open the file dialog\n  inputElement.dispatchEvent(new MouseEvent('click'));\n}\n\nvar useValidators = function useValidators(_ref) {\n  var onFilesSelectedProp = _ref.onFilesSelected,\n    onFilesSuccessfullySelectedProp = _ref.onFilesSuccessfullySelected,\n    onFilesRejectedProp = _ref.onFilesRejected,\n    onClearProp = _ref.onClear,\n    validators = _ref.validators;\n  // setup validators' event handlers\n  var onFilesSelected = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (data) {\n    onFilesSelectedProp == null ? void 0 : onFilesSelectedProp(data);\n    validators == null ? void 0 : validators.forEach(function (validator) {\n      validator.onFilesSelected(data);\n    });\n  }, [onFilesSelectedProp, validators]);\n  var onFilesSuccessfullySelected = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (data) {\n    onFilesSuccessfullySelectedProp == null ? void 0 : onFilesSuccessfullySelectedProp(data);\n    validators == null ? void 0 : validators.forEach(function (validator) {\n      validator.onFilesSuccessfullySelected(data);\n    });\n  }, [validators, onFilesSuccessfullySelectedProp]);\n  var onFilesRejected = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (errors) {\n    onFilesRejectedProp == null ? void 0 : onFilesRejectedProp(errors);\n    validators == null ? void 0 : validators.forEach(function (validator) {\n      validator.onFilesRejected(errors);\n    });\n  }, [validators, onFilesRejectedProp]);\n  var onClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    onClearProp == null ? void 0 : onClearProp();\n    validators == null ? void 0 : validators.forEach(function (validator) {\n      validator.onClear == null ? void 0 : validator.onClear();\n    });\n  }, [validators, onClearProp]);\n  return {\n    onFilesSelected: onFilesSelected,\n    onFilesSuccessfullySelected: onFilesSuccessfullySelected,\n    onFilesRejected: onFilesRejected,\n    onClear: onClear\n  };\n};\n\n// empty array reference in order to avoid re-renders when no validators are passed as props\nvar EMPTY_ARRAY = [];\nfunction useFilePicker(props) {\n  var _props$accept = props.accept,\n    accept = _props$accept === void 0 ? '*' : _props$accept,\n    _props$multiple = props.multiple,\n    multiple = _props$multiple === void 0 ? true : _props$multiple,\n    _props$readAs = props.readAs,\n    readAs = _props$readAs === void 0 ? 'Text' : _props$readAs,\n    _props$readFilesConte = props.readFilesContent,\n    readFilesContent = _props$readFilesConte === void 0 ? true : _props$readFilesConte,\n    _props$validators = props.validators,\n    validators = _props$validators === void 0 ? EMPTY_ARRAY : _props$validators,\n    initializeWithCustomParameters = props.initializeWithCustomParameters;\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]),\n    plainFiles = _useState[0],\n    setPlainFiles = _useState[1];\n  var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]),\n    filesContent = _useState2[0],\n    setFilesContent = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]),\n    fileErrors = _useState3[0],\n    setFileErrors = _useState3[1];\n  var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),\n    loading = _useState4[0],\n    setLoading = _useState4[1];\n  var _useValidators = useValidators(props),\n    onFilesSelected = _useValidators.onFilesSelected,\n    onFilesSuccessfullySelected = _useValidators.onFilesSuccessfullySelected,\n    onFilesRejected = _useValidators.onFilesRejected,\n    onClear = _useValidators.onClear;\n  var clear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    setPlainFiles([]);\n    setFilesContent([]);\n    setFileErrors([]);\n  }, []);\n  var clearWithEventListener = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    clear();\n    onClear == null ? void 0 : onClear();\n  }, [clear, onClear]);\n  var parseFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (file) {\n    return new Promise( /*#__PURE__*/function () {\n      var _ref = (0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__._)( /*#__PURE__*/(0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.a)().mark(function _callee2(resolve, reject) {\n        var reader, readStrategy, addError;\n        return (0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.a)().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              reader = new FileReader(); //availible reader methods: readAsText, readAsBinaryString, readAsArrayBuffer, readAsDataURL\n              readStrategy = reader[\"readAs\" + readAs];\n              readStrategy.call(reader, file);\n              addError = function addError(_ref2) {\n                var others = (0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.b)({}, ((0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.c)(_ref2), _ref2));\n                reject((0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.b)({}, others));\n              };\n              reader.onload = /*#__PURE__*/(0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__._)( /*#__PURE__*/(0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.a)().mark(function _callee() {\n                return (0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.a)().wrap(function _callee$(_context) {\n                  while (1) switch (_context.prev = _context.next) {\n                    case 0:\n                      return _context.abrupt(\"return\", Promise.all(validators.map(function (validator) {\n                        return validator.validateAfterParsing(props, file, reader)[\"catch\"](function (err) {\n                          return Promise.reject(addError(err));\n                        });\n                      })).then(function () {\n                        return resolve((0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.b)({}, file, {\n                          content: reader.result,\n                          name: file.name,\n                          lastModified: file.lastModified\n                        }));\n                      })[\"catch\"](function () {}));\n                    case 1:\n                    case \"end\":\n                      return _context.stop();\n                  }\n                }, _callee);\n              }));\n              reader.onerror = function () {\n                addError({\n                  name: 'FileReaderError',\n                  readerError: reader.error,\n                  causedByFile: file\n                });\n              };\n            case 6:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }, [props, readAs, validators]);\n  var openFilePicker = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    var fileExtensions = accept instanceof Array ? accept.join(',') : accept;\n    openFileDialog(fileExtensions, multiple, /*#__PURE__*/function () {\n      var _ref4 = (0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__._)( /*#__PURE__*/(0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.a)().mark(function _callee3(evt) {\n        var inputElement, plainFileObjects, validationsBeforeParsing, files, validationsAfterParsing, filesContent;\n        return (0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.a)().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              clear();\n              inputElement = evt.target;\n              plainFileObjects = inputElement.files ? Array.from(inputElement.files) : [];\n              setLoading(true);\n              _context3.next = 6;\n              return Promise.all(validators.map(function (validator) {\n                return validator.validateBeforeParsing(props, plainFileObjects)[\"catch\"](function (err) {\n                  return Array.isArray(err) ? err : [err];\n                });\n              }));\n            case 6:\n              validationsBeforeParsing = _context3.sent.flat(1).filter(Boolean);\n              setPlainFiles(plainFileObjects);\n              setFileErrors(validationsBeforeParsing);\n              if (!validationsBeforeParsing.length) {\n                _context3.next = 15;\n                break;\n              }\n              setLoading(false);\n              setPlainFiles([]);\n              onFilesRejected == null ? void 0 : onFilesRejected({\n                errors: validationsBeforeParsing\n              });\n              onFilesSelected == null ? void 0 : onFilesSelected({\n                errors: validationsBeforeParsing\n              });\n              return _context3.abrupt(\"return\");\n            case 15:\n              if (readFilesContent) {\n                _context3.next = 19;\n                break;\n              }\n              setLoading(false);\n              onFilesSelected == null ? void 0 : onFilesSelected({\n                plainFiles: plainFileObjects,\n                filesContent: []\n              });\n              return _context3.abrupt(\"return\");\n            case 19:\n              _context3.next = 21;\n              return (0,file_selector__WEBPACK_IMPORTED_MODULE_2__.fromEvent)(evt);\n            case 21:\n              files = _context3.sent;\n              validationsAfterParsing = [];\n              _context3.next = 25;\n              return Promise.all(files.map(function (file) {\n                return parseFile(file)[\"catch\"](function (fileError) {\n                  validationsAfterParsing.push.apply(validationsAfterParsing, Array.isArray(fileError) ? fileError : [fileError]);\n                });\n              }));\n            case 25:\n              filesContent = _context3.sent;\n              setLoading(false);\n              if (!validationsAfterParsing.length) {\n                _context3.next = 34;\n                break;\n              }\n              setPlainFiles([]);\n              setFilesContent([]);\n              setFileErrors(function (errors) {\n                return [].concat(errors, validationsAfterParsing);\n              });\n              onFilesRejected == null ? void 0 : onFilesRejected({\n                errors: validationsAfterParsing\n              });\n              onFilesSelected == null ? void 0 : onFilesSelected({\n                errors: validationsBeforeParsing.concat(validationsAfterParsing)\n              });\n              return _context3.abrupt(\"return\");\n            case 34:\n              setFilesContent(filesContent);\n              setPlainFiles(plainFileObjects);\n              setFileErrors([]);\n              onFilesSuccessfullySelected == null ? void 0 : onFilesSuccessfullySelected({\n                filesContent: filesContent,\n                plainFiles: plainFileObjects\n              });\n              onFilesSelected == null ? void 0 : onFilesSelected({\n                plainFiles: plainFileObjects,\n                filesContent: filesContent\n              });\n            case 39:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function (_x3) {\n        return _ref4.apply(this, arguments);\n      };\n    }(), initializeWithCustomParameters);\n  }, [props, accept, clear, initializeWithCustomParameters, multiple, onFilesRejected, onFilesSelected, onFilesSuccessfullySelected, parseFile, readFilesContent, validators]);\n  return {\n    openFilePicker: openFilePicker,\n    filesContent: filesContent,\n    errors: fileErrors,\n    loading: loading,\n    plainFiles: plainFiles,\n    clear: clearWithEventListener\n  };\n}\n\n/**\n * A version of useFilePicker hook that keeps selected files between selections. On top of that it allows to remove files from the selection.\n */\nfunction useImperativeFilePicker(props) {\n  var _onFilesSelected = props.onFilesSelected,\n    _onFilesSuccessfullySelected = props.onFilesSuccessfullySelected,\n    validators = props.validators,\n    onFileRemoved = props.onFileRemoved;\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]),\n    allPlainFiles = _useState[0],\n    setAllPlainFiles = _useState[1];\n  var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]),\n    allFilesContent = _useState2[0],\n    setAllFilesContent = _useState2[1];\n  var _useFilePicker = useFilePicker((0,_rollupPluginBabelHelpers_4e04b055_js__WEBPACK_IMPORTED_MODULE_0__.b)({}, props, {\n      onFilesSelected: function onFilesSelected(data) {\n        var _data$errors;\n        if (!_onFilesSelected) return;\n        if ((_data$errors = data.errors) != null && _data$errors.length) {\n          return _onFilesSelected(data);\n        }\n        // override the files property to return all files that were selected previously and in the current batch\n        _onFilesSelected({\n          errors: undefined,\n          plainFiles: [].concat(allPlainFiles, data.plainFiles || []),\n          filesContent: [].concat(allFilesContent, data.filesContent || [])\n        });\n      },\n      onFilesSuccessfullySelected: function onFilesSuccessfullySelected(data) {\n        setAllPlainFiles(function (previousPlainFiles) {\n          return previousPlainFiles.concat(data.plainFiles);\n        });\n        setAllFilesContent(function (previousFilesContent) {\n          return previousFilesContent.concat(data.filesContent);\n        });\n        if (!_onFilesSuccessfullySelected) return;\n        // override the files property to return all files that were selected previously and in the current batch\n        _onFilesSuccessfullySelected({\n          plainFiles: [].concat(allPlainFiles, data.plainFiles || []),\n          filesContent: [].concat(allFilesContent, data.filesContent || [])\n        });\n      }\n    })),\n    openFilePicker = _useFilePicker.openFilePicker,\n    loading = _useFilePicker.loading,\n    errors = _useFilePicker.errors,\n    clear = _useFilePicker.clear;\n  var clearAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    clear();\n    // clear previous files\n    setAllPlainFiles([]);\n    setAllFilesContent([]);\n  }, [clear]);\n  var removeFileByIndex = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (index) {\n    setAllFilesContent(function (previousFilesContent) {\n      return [].concat(previousFilesContent.slice(0, index), previousFilesContent.slice(index + 1));\n    });\n    setAllPlainFiles(function (previousPlainFiles) {\n      var removedFile = previousPlainFiles[index];\n      validators == null ? void 0 : validators.forEach(function (validator) {\n        return validator.onFileRemoved == null ? void 0 : validator.onFileRemoved(removedFile, index);\n      });\n      onFileRemoved == null ? void 0 : onFileRemoved(removedFile, index);\n      return [].concat(previousPlainFiles.slice(0, index), previousPlainFiles.slice(index + 1));\n    });\n  }, [validators, onFileRemoved]);\n  var removeFileByReference = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (file) {\n    var index = allPlainFiles.findIndex(function (f) {\n      return f === file;\n    });\n    if (index === -1) return;\n    removeFileByIndex(index);\n  }, [removeFileByIndex, allPlainFiles]);\n  return {\n    openFilePicker: openFilePicker,\n    plainFiles: allPlainFiles,\n    filesContent: allFilesContent,\n    loading: loading,\n    errors: errors,\n    clear: clearAll,\n    removeFileByIndex: removeFileByIndex,\n    removeFileByReference: removeFileByReference\n  };\n}\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-file-picker/dist/index.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-file-picker/node_modules/file-selector/dist/es5/file-selector.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/use-file-picker/node_modules/file-selector/dist/es5/file-selector.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* binding */ fromEvent)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _file__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file */ \"(ssr)/./node_modules/use-file-picker/node_modules/file-selector/dist/es5/file.js\");\n\n\nvar FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store',\n    'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n * @param evt\n */\nfunction fromEvent(evt) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function () {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function (_a) {\n            return [2 /*return*/, isDragEvt(evt) && evt.dataTransfer\n                    ? getDataTransferFiles(evt.dataTransfer, evt.type)\n                    : getInputFiles(evt)];\n        });\n    });\n}\nfunction isDragEvt(value) {\n    return !!value.dataTransfer;\n}\nfunction getInputFiles(evt) {\n    var files = isInput(evt.target)\n        ? evt.target.files\n            ? fromList(evt.target.files)\n            : []\n        : [];\n    return files.map(function (file) { return (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file); });\n}\nfunction isInput(value) {\n    return value !== null;\n}\nfunction getDataTransferFiles(dt, type) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function () {\n        var items, files;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    if (!dt.items) return [3 /*break*/, 2];\n                    items = fromList(dt.items)\n                        .filter(function (item) { return item.kind === 'file'; });\n                    // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n                    // only 'dragstart' and 'drop' has access to the data (source node)\n                    if (type !== 'drop') {\n                        return [2 /*return*/, items];\n                    }\n                    return [4 /*yield*/, Promise.all(items.map(toFilePromises))];\n                case 1:\n                    files = _a.sent();\n                    return [2 /*return*/, noIgnoredFiles(flatten(files))];\n                case 2: return [2 /*return*/, noIgnoredFiles(fromList(dt.files)\n                        .map(function (file) { return (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file); }))];\n            }\n        });\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter(function (file) { return FILES_TO_IGNORE.indexOf(file.name) === -1; });\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    var files = [];\n    // tslint:disable: prefer-for-of\n    for (var i = 0; i < items.length; i++) {\n        var file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n    var entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item);\n}\nfunction flatten(items) {\n    return items.reduce(function (acc, files) { return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spread)(acc, (Array.isArray(files) ? flatten(files) : [files])); }, []);\n}\nfunction fromDataTransferItem(item) {\n    var file = item.getAsFile();\n    if (!file) {\n        return Promise.reject(item + \" is not a File\");\n    }\n    var fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file);\n    return Promise.resolve(fwp);\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function () {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function (_a) {\n            return [2 /*return*/, entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry)];\n        });\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    var reader = entry.createReader();\n    return new Promise(function (resolve, reject) {\n        var entries = [];\n        function readEntries() {\n            var _this = this;\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries(function (batch) { return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(_this, void 0, void 0, function () {\n                var files, err_1, items;\n                return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            if (!!batch.length) return [3 /*break*/, 5];\n                            _a.label = 1;\n                        case 1:\n                            _a.trys.push([1, 3, , 4]);\n                            return [4 /*yield*/, Promise.all(entries)];\n                        case 2:\n                            files = _a.sent();\n                            resolve(files);\n                            return [3 /*break*/, 4];\n                        case 3:\n                            err_1 = _a.sent();\n                            reject(err_1);\n                            return [3 /*break*/, 4];\n                        case 4: return [3 /*break*/, 6];\n                        case 5:\n                            items = Promise.all(batch.map(fromEntry));\n                            entries.push(items);\n                            // Continue reading\n                            readEntries();\n                            _a.label = 6;\n                        case 6: return [2 /*return*/];\n                    }\n                });\n            }); }, function (err) {\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function () {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function (_a) {\n            return [2 /*return*/, new Promise(function (resolve, reject) {\n                    entry.file(function (file) {\n                        var fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file, entry.fullPath);\n                        resolve(fwp);\n                    }, function (err) {\n                        reject(err);\n                    });\n                })];\n        });\n    });\n}\n//# sourceMappingURL=file-selector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-file-picker/node_modules/file-selector/dist/es5/file-selector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-file-picker/node_modules/file-selector/dist/es5/file.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/use-file-picker/node_modules/file-selector/dist/es5/file.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMMON_MIME_TYPES: () => (/* binding */ COMMON_MIME_TYPES),\n/* harmony export */   toFileWithPath: () => (/* binding */ toFileWithPath)\n/* harmony export */ });\nvar COMMON_MIME_TYPES = new Map([\n    ['avi', 'video/avi'],\n    ['gif', 'image/gif'],\n    ['ico', 'image/x-icon'],\n    ['jpeg', 'image/jpeg'],\n    ['jpg', 'image/jpeg'],\n    ['mkv', 'video/x-matroska'],\n    ['mov', 'video/quicktime'],\n    ['mp4', 'video/mp4'],\n    ['pdf', 'application/pdf'],\n    ['png', 'image/png'],\n    ['zip', 'application/zip'],\n    ['doc', 'application/msword'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']\n]);\nfunction toFileWithPath(file, path) {\n    var f = withMimeType(file);\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        var webkitRelativePath = file.webkitRelativePath;\n        Object.defineProperty(f, 'path', {\n            value: typeof path === 'string'\n                ? path\n                // If <input webkitdirectory> is set,\n                // the File will have a {webkitRelativePath} property\n                // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n                : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n                    ? webkitRelativePath\n                    : file.name,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    return f;\n}\nfunction withMimeType(file) {\n    var name = file.name;\n    var hasExtension = name && name.lastIndexOf('.') !== -1;\n    if (hasExtension && !file.type) {\n        var ext = name.split('.')\n            .pop().toLowerCase();\n        var type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\n//# sourceMappingURL=file.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-file-picker/node_modules/file-selector/dist/es5/file.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-file-picker/node_modules/file-selector/dist/es5/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/use-file-picker/node_modules/file-selector/dist/es5/index.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* reexport safe */ _file_selector__WEBPACK_IMPORTED_MODULE_0__.fromEvent)\n/* harmony export */ });\n/* harmony import */ var _file_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file-selector */ \"(ssr)/./node_modules/use-file-picker/node_modules/file-selector/dist/es5/file-selector.js\");\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWZpbGUtcGlja2VyL25vZGVfbW9kdWxlcy9maWxlLXNlbGVjdG9yL2Rpc3QvZXM1L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBQzVDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy91c2UtZmlsZS1waWNrZXIvbm9kZV9tb2R1bGVzL2ZpbGUtc2VsZWN0b3IvZGlzdC9lczUvaW5kZXguanM/NTEyYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBmcm9tRXZlbnQgfSBmcm9tICcuL2ZpbGUtc2VsZWN0b3InO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-file-picker/node_modules/file-selector/dist/es5/index.js\n");

/***/ })

};
;