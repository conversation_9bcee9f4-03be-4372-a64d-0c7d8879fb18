"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/camera.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Camera; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.399.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Camera = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Camera\", [\n    [\n        \"path\",\n        {\n            d: \"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z\",\n            key: \"1tc9qg\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"13\",\n            r: \"3\",\n            key: \"1vg3eu\"\n        }\n    ]\n]);\n //# sourceMappingURL=camera.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2FtZXJhLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sTUFBQUEsU0FBU0MsZ0VBQWdCQSxDQUFDLFVBQVU7SUFDeEM7UUFDRTtRQUNBO1lBQ0VDLEdBQUc7WUFDSEMsS0FBSztRQUNQO0tBQ0Y7SUFDQTtRQUFDO1FBQVU7WUFBRUMsSUFBSTtZQUFNQyxJQUFJO1lBQU1DLEdBQUc7WUFBS0gsS0FBSztRQUFBO0tBQVU7Q0FDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9jYW1lcmEudHM/MjRmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENhbWVyYVxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTVRRdU5TQTBhQzAxVERjZ04wZzBZVElnTWlBd0lEQWdNQzB5SURKMk9XRXlJRElnTUNBd0lEQWdNaUF5YURFMllUSWdNaUF3SURBZ01DQXlMVEpXT1dFeUlESWdNQ0F3SURBdE1pMHlhQzB6YkMweUxqVXRNM29pSUM4K0NpQWdQR05wY21Oc1pTQmplRDBpTVRJaUlHTjVQU0l4TXlJZ2NqMGlNeUlnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2FtZXJhXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2FtZXJhID0gY3JlYXRlTHVjaWRlSWNvbignQ2FtZXJhJywgW1xuICBbXG4gICAgJ3BhdGgnLFxuICAgIHtcbiAgICAgIGQ6ICdNMTQuNSA0aC01TDcgN0g0YTIgMiAwIDAgMC0yIDJ2OWEyIDIgMCAwIDAgMiAyaDE2YTIgMiAwIDAgMCAyLTJWOWEyIDIgMCAwIDAtMi0yaC0zbC0yLjUtM3onLFxuICAgICAga2V5OiAnMXRjOXFnJyxcbiAgICB9LFxuICBdLFxuICBbJ2NpcmNsZScsIHsgY3g6ICcxMicsIGN5OiAnMTMnLCByOiAnMycsIGtleTogJzF2ZzNldScgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2FtZXJhO1xuIl0sIm5hbWVzIjpbIkNhbWVyYSIsImNyZWF0ZUx1Y2lkZUljb24iLCJkIiwia2V5IiwiY3giLCJjeSIsInIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/editor/components/navbar.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/navbar.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: function() { return /* binding */ Navbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CiFileOn!=!react-icons/ci */ \"(app-pages-browser)/./node_modules/react-icons/ci/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BsCloudCheck_BsCloudSlash_react_icons_bs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BsCloudCheck,BsCloudSlash!=!react-icons/bs */ \"(app-pages-browser)/./node_modules/react-icons/bs/index.mjs\");\n/* harmony import */ var use_file_picker__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-file-picker */ \"(app-pages-browser)/./node_modules/use-file-picker/dist/index.esm.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer-click.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo-2.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo-2.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _features_auth_components_user_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/auth/components/user-button */ \"(app-pages-browser)/./src/features/auth/components/user-button.tsx\");\n/* harmony import */ var _features_editor_components_logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/logo */ \"(app-pages-browser)/./src/features/editor/components/logo.tsx\");\n/* harmony import */ var _features_editor_components_project_name__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/project-name */ \"(app-pages-browser)/./src/features/editor/components/project-name.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_hint__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/hint */ \"(app-pages-browser)/./src/components/hint.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Navbar = (param)=>{\n    let { id, editor, activeTool, onChangeActiveTool, onSave, initialData } = param;\n    _s();\n    const data = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutationState)({\n        filters: {\n            mutationKey: [\n                \"project\",\n                {\n                    id\n                }\n            ],\n            exact: true\n        },\n        select: (mutation)=>mutation.state.status\n    });\n    const currentStatus = data[data.length - 1];\n    const isError = currentStatus === \"error\";\n    const isPending = currentStatus === \"pending\";\n    const { openFilePicker } = (0,use_file_picker__WEBPACK_IMPORTED_MODULE_1__.useFilePicker)({\n        accept: \".json\",\n        onFilesSuccessfullySelected: (param)=>{\n            let { plainFiles } = param;\n            if (plainFiles && plainFiles.length > 0) {\n                const file = plainFiles[0];\n                const reader = new FileReader();\n                reader.readAsText(file, \"UTF-8\");\n                reader.onload = ()=>{\n                    editor === null || editor === void 0 ? void 0 : editor.loadJson(reader.result);\n                };\n            }\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"w-full flex items-center p-4 h-[68px] gap-x-8 border-b lg:pl-[34px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_logo__WEBPACK_IMPORTED_MODULE_3__.Logo, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex items-center gap-x-1 h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                        modal: false,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    children: [\n                                        \"File\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"size-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                align: \"start\",\n                                className: \"min-w-60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                    onClick: ()=>openFilePicker(),\n                                    className: \"flex items-center gap-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                            className: \"size-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Open\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Open a JSON file\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                        orientation: \"vertical\",\n                        className: \"mx-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_project_name__WEBPACK_IMPORTED_MODULE_4__.ProjectName, {\n                                id: id,\n                                name: initialData.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined),\n                            initialData.isCustomizable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                variant: \"secondary\",\n                                className: \"text-xs\",\n                                children: \"Template\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                        orientation: \"vertical\",\n                        className: \"mx-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Select\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>onChangeActiveTool(\"select\"),\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"select\" && \"bg-gray-100\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Undo\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            disabled: !(editor === null || editor === void 0 ? void 0 : editor.canUndo()),\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.onUndo(),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Redo\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            disabled: !(editor === null || editor === void 0 ? void 0 : editor.canRedo()),\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.onRedo(),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Save\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: onSave,\n                            disabled: !onSave,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Generate Thumbnail\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>{\n                                if (editor) {\n                                    console.log(\"Manual thumbnail generation triggered\");\n                                    const thumbnailDataUrl = editor.generateThumbnail({\n                                        width: 300,\n                                        height: 200,\n                                        quality: 0.8\n                                    });\n                                    console.log(\"Generated thumbnail:\", thumbnailDataUrl.substring(0, 100) + \"...\");\n                                }\n                            },\n                            disabled: !editor,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                        orientation: \"vertical\",\n                        className: \"mx-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, undefined),\n                    isPending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"size-4 animate-spin text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Saving...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, undefined),\n                    !isPending && isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsCloudCheck_BsCloudSlash_react_icons_bs__WEBPACK_IMPORTED_MODULE_20__.BsCloudSlash, {\n                                className: \"size-[20px] text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Failed to save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, undefined),\n                    !isPending && !isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsCloudCheck_BsCloudSlash_react_icons_bs__WEBPACK_IMPORTED_MODULE_20__.BsCloudCheck, {\n                                className: \"size-[20px] text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Saved\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-auto flex items-center gap-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                                modal: false,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            size: \"sm\",\n                                            variant: \"ghost\",\n                                            children: [\n                                                \"Export\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"size-4 ml-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"min-w-60\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.saveJson(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"JSON\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Save for later editing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.savePng(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"PNG\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Best for sharing on the web\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.saveJpg(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"JPG\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Best for printing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.saveSvg(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"SVG\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Best for editing in vector software\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_auth_components_user_button__WEBPACK_IMPORTED_MODULE_2__.UserButton, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"VCYRclSx01n/ShJ89dLY9EBkSlQ=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutationState,\n        use_file_picker__WEBPACK_IMPORTED_MODULE_1__.useFilePicker\n    ];\n});\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/navbar.tsx\n"));

/***/ })

});