"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _features_editor_components_customization_editor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/components/customization-editor */ \"(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeLayerId, setActiveLayerId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers safely\n                let editableLayers = [];\n                try {\n                    editableLayers = templateData.editableLayers ? JSON.parse(templateData.editableLayers) : [];\n                } catch (error) {\n                    console.error(\"Error parsing editable layers:\", error);\n                    editableLayers = [];\n                }\n                console.log(\"Template loaded:\", {\n                    templateData,\n                    editableLayers,\n                    json: templateData.json ? JSON.parse(templateData.json) : null\n                });\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleCustomizationChange = (layerId, value)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        // Create object URL for immediate preview\n        const imageUrl = URL.createObjectURL(file);\n        handleCustomizationChange(layerId, imageUrl);\n    };\n    const handlePreviewGenerated = (dataUrl)=>{\n        setPreviewUrl(dataUrl);\n    };\n    const handleLayerActivation = (layerId)=>{\n        setActiveLayerId(layerId);\n    };\n    // Initialize preview with template thumbnail\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n        }\n    }, [\n        template\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it and download the current preview\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            if (previewUrl) {\n                const link = document.createElement(\"a\");\n                link.href = previewUrl;\n                link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n                link.click();\n            }\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Customize Elements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 p-3 rounded-lg border-2 transition-all cursor-pointer \".concat(activeLayerId === layer.id ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                onClick: ()=>handleLayerActivation(layer.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                variant: activeLayerId === layer.id ? \"default\" : layer.type === \"text\" ? \"outline\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm \".concat(activeLayerId === layer.id ? \"text-blue-900\" : \"text-gray-700\"),\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            activeLayerId === layer.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-auto\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: customizations[layer.id] || \"\",\n                                                                onChange: (e)=>handleCustomizationChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 296,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 317,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 318,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 316,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"h-[600px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Live Preview & Editor\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Double-click text elements to edit them directly\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"h-[calc(100%-80px)] p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_customization_editor__WEBPACK_IMPORTED_MODULE_8__.CustomizationEditor, {\n                                            templateData: {\n                                                id: template.id,\n                                                name: template.name,\n                                                width: template.width,\n                                                height: template.height,\n                                                json: template.json,\n                                                editableLayers: template.editableLayers\n                                            },\n                                            customizations: customizations,\n                                            onCustomizationChange: handleCustomizationChange,\n                                            onPreviewGenerated: handlePreviewGenerated,\n                                            activeLayerId: activeLayerId,\n                                            onLayerActivation: handleLayerActivation\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"UqYuE9JKgDmrCOdj/zSgTwB0xHg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated, activeLayerId: externalActiveLayerId, onLayerActivation } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeLayerId, setActiveLayerId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(externalActiveLayerId || null);\n    // Sync with external active layer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setActiveLayerId(externalActiveLayerId || null);\n    }, [\n        externalActiveLayerId\n    ]);\n    // Initialize editor with no-save callback since this is read-only customization\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{},\n        saveCallback: ()=>{}\n    });\n    // Initialize canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (retryCanvas) {\n                    setIsInitialized(true);\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        } else {\n            setIsInitialized(true);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Lock non-editable objects and setup customization mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up customization mode:\", {\n            editableLayerIds,\n            totalObjects: canvas.getObjects().length,\n            editableLayers: templateData.editableLayers\n        });\n        // Lock all objects except editable ones\n        canvas.getObjects().forEach((obj)=>{\n            console.log(\"Processing object:\", {\n                id: obj.id,\n                type: obj.type,\n                name: obj.name,\n                isEditable: editableLayerIds.includes(obj.id)\n            });\n            if (obj.name === \"clip\") {\n                // Keep workspace as is but make it non-selectable\n                obj.set({\n                    selectable: false,\n                    evented: false\n                });\n                return;\n            }\n            if (editableLayerIds.includes(obj.id)) {\n                console.log(\"Making object editable:\", obj.id, obj.type);\n                // Make editable objects selectable and editable\n                obj.set({\n                    selectable: true,\n                    hasControls: false,\n                    hasBorders: true,\n                    borderColor: \"#3b82f6\",\n                    borderScaleFactor: 2,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true,\n                    editable: obj.type === \"textbox\" ? true : false,\n                    hoverCursor: \"pointer\",\n                    moveCursor: \"pointer\"\n                });\n                // Add visual indicator for editable elements\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: \"rgba(59, 130, 246, 0.1)\"\n                    });\n                }\n            } else {\n                // Lock non-editable objects completely\n                obj.set({\n                    selectable: false,\n                    evented: false,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n            }\n        });\n        canvas.renderAll();\n    }, [\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Update visual feedback for editable objects\n    const updateEditableObjectsVisuals = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        editor.canvas.getObjects().forEach((obj)=>{\n            if (editableLayerIds.includes(obj.id)) {\n                const isActive = obj.id === activeLayerId;\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: isActive ? \"rgba(59, 130, 246, 0.2)\" // Darker blue when active\n                         : \"rgba(59, 130, 246, 0.1)\",\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2\n                    });\n                } else {\n                    // For image layers\n                    obj.set({\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2,\n                        opacity: isActive ? 1 : 0.8\n                    });\n                }\n            }\n        });\n        editor.canvas.renderAll();\n    }, [\n        editor,\n        templateData.editableLayers,\n        activeLayerId\n    ]);\n    // Update visuals when active layer changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateEditableObjectsVisuals();\n    }, [\n        activeLayerId,\n        updateEditableObjectsVisuals\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue || customValue === layer.originalValue) return;\n            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                canvasObject.set(\"text\", customValue);\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        selectable: true,\n                        hasControls: false,\n                        lockMovementX: true,\n                        lockMovementY: true,\n                        lockRotation: true,\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true\n                    });\n                    // Set the ID using a custom property\n                    img.id = layer.id;\n                    canvas.remove(canvasObject);\n                    canvas.add(img);\n                    canvas.renderAll();\n                    generatePreview();\n                });\n                return;\n            }\n        });\n        canvas.renderAll();\n        generatePreview();\n    }, [\n        customizations,\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Generate preview from canvas\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            // Create a temporary canvas with just the workspace content\n            const tempCanvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(null, {\n                width: workspace.width,\n                height: workspace.height\n            });\n            // Clone all objects except the workspace itself\n            const objectsToClone = editor.canvas.getObjects().filter((obj)=>obj.name !== \"clip\");\n            objectsToClone.forEach((obj)=>{\n                obj.clone((cloned)=>{\n                    // Adjust position relative to workspace\n                    cloned.set({\n                        left: (cloned.left || 0) - (workspace.left || 0),\n                        top: (cloned.top || 0) - (workspace.top || 0)\n                    });\n                    tempCanvas.add(cloned);\n                });\n            });\n            // Generate preview after a short delay to ensure all objects are added\n            setTimeout(()=>{\n                const dataUrl = tempCanvas.toDataURL({\n                    format: \"png\",\n                    quality: 0.9,\n                    multiplier: 1\n                });\n                onPreviewGenerated(dataUrl);\n                tempCanvas.dispose();\n            }, 100);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Handle text editing\n    const handleTextEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((layerId, value)=>{\n        onCustomizationChange(layerId, value);\n    }, [\n        onCustomizationChange\n    ]);\n    // Handle click events and layer activation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleObjectSelection = (e)=>{\n            const target = e.target;\n            if (!target) {\n                setActiveLayerId(null);\n                return;\n            }\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer) {\n                console.log(\"Layer activated:\", layer);\n                setActiveLayerId(layerId);\n                onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(layerId);\n                // Update visual feedback for all editable objects\n                updateEditableObjectsVisuals();\n            } else {\n                setActiveLayerId(null);\n                onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n            }\n        };\n        const handleCanvasClick = (e)=>{\n            if (!e.target) {\n                setActiveLayerId(null);\n                updateEditableObjectsVisuals();\n            }\n        };\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (!layer || layer.type !== \"text\") return;\n            const currentText = target.text || \"\";\n            handleTextEdit(layerId, currentText);\n            generatePreview();\n        };\n        const handleDoubleClick = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer && layer.type === \"text\") {\n                setActiveLayerId(layerId);\n                // Enter editing mode\n                target.enterEditing();\n                target.selectAll();\n            }\n        };\n        editor.canvas.on(\"selection:created\", handleObjectSelection);\n        editor.canvas.on(\"selection:updated\", handleObjectSelection);\n        editor.canvas.on(\"selection:cleared\", ()=>setActiveLayerId(null));\n        editor.canvas.on(\"mouse:down\", handleCanvasClick);\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        editor.canvas.on(\"mouse:dblclick\", handleDoubleClick);\n        return ()=>{\n            editor.canvas.off(\"selection:created\", handleObjectSelection);\n            editor.canvas.off(\"selection:updated\", handleObjectSelection);\n            editor.canvas.off(\"selection:cleared\", ()=>setActiveLayerId(null));\n            editor.canvas.off(\"mouse:down\", handleCanvasClick);\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n            editor.canvas.off(\"mouse:dblclick\", handleDoubleClick);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        handleTextEdit,\n        generatePreview\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    ref: containerRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 398,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"ReGyCtJ/TVgJB/jkwtT/sRPNUGM=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});