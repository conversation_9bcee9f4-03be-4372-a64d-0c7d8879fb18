"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts":
/*!**************************************************************!*\
  !*** ./src/features/editor/hooks/use-thumbnail-generator.ts ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useThumbnailGenerator: function() { return /* binding */ useThumbnailGenerator; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _lib_hono__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hono */ \"(app-pages-browser)/./src/lib/hono.ts\");\n\n\n\n\nconst useThumbnailGenerator = (param)=>{\n    let { editor, projectId, onThumbnailGenerated } = param;\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const lastThumbnailRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const generateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!editor) {\n            console.warn(\"No editor available for thumbnail generation\");\n            return;\n        }\n        if (!editor.canvas) {\n            console.warn(\"No canvas available for thumbnail generation\");\n            return;\n        }\n        try {\n            console.log(\"Generating thumbnail for project:\", projectId);\n            // Generate thumbnail data URL with smaller size for testing\n            const thumbnailDataUrl = editor.generateThumbnail({\n                width: 300,\n                height: 200,\n                quality: 0.8\n            });\n            if (!thumbnailDataUrl) {\n                console.error(\"Failed to generate thumbnail data URL\");\n                return;\n            }\n            console.log(\"Generated thumbnail data URL length:\", thumbnailDataUrl.length);\n            console.log(\"Thumbnail data URL preview:\", thumbnailDataUrl.substring(0, 100) + \"...\");\n            // Test if data URL is valid\n            const isValidDataUrl = thumbnailDataUrl.startsWith(\"data:image/\");\n            console.log(\"Is valid data URL:\", isValidDataUrl);\n            // Test if we can create an image from it\n            const testImg = new Image();\n            testImg.onload = ()=>console.log(\"Data URL is valid image\");\n            testImg.onerror = (e)=>console.error(\"Data URL is invalid:\", e);\n            testImg.src = thumbnailDataUrl;\n            // Skip if thumbnail hasn't changed significantly\n            if (lastThumbnailRef.current === thumbnailDataUrl) {\n                console.log(\"Thumbnail unchanged, skipping update\");\n                return;\n            }\n            lastThumbnailRef.current = thumbnailDataUrl;\n            console.log(\"Thumbnail generated, updating project...\");\n            // For now, let's store the thumbnail as a data URL directly in the database\n            // This is simpler and doesn't require external file uploads\n            // In production, you might want to upload to a CDN\n            // Update project with thumbnail data URL\n            console.log(\"Sending PATCH request with thumbnailUrl length:\", thumbnailDataUrl.length);\n            const response = await _lib_hono__WEBPACK_IMPORTED_MODULE_2__.client.api.projects[\":id\"].$patch({\n                param: {\n                    id: projectId\n                },\n                json: {\n                    thumbnailUrl: thumbnailDataUrl\n                }\n            });\n            console.log(\"PATCH response status:\", response.status);\n            if (response.ok) {\n                console.log(\"Thumbnail updated successfully\");\n                const responseData = await response.json();\n                console.log(\"Response data:\", responseData);\n                // Invalidate projects query to refresh the UI\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        \"projects\"\n                    ]\n                });\n                // Also invalidate the individual project query\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        \"project\",\n                        {\n                            id: projectId\n                        }\n                    ]\n                });\n                // Force refetch of projects data\n                queryClient.refetchQueries({\n                    queryKey: [\n                        \"projects\"\n                    ]\n                });\n                onThumbnailGenerated === null || onThumbnailGenerated === void 0 ? void 0 : onThumbnailGenerated(thumbnailDataUrl);\n            } else {\n                console.error(\"Failed to update thumbnail:\", response.status, response.statusText);\n                const errorText = await response.text();\n                console.error(\"Error response:\", errorText);\n            }\n        } catch (error) {\n            console.error(\"Error generating thumbnail:\", error);\n        }\n    }, [\n        editor,\n        projectId,\n        onThumbnailGenerated\n    ]);\n    // Debounced version to avoid too frequent thumbnail generation\n    const debouncedGenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default()(generateThumbnail, 2000), [\n        generateThumbnail\n    ]);\n    const forceRegenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        // Reset the last thumbnail to force regeneration\n        lastThumbnailRef.current = null;\n        await generateThumbnail();\n    }, [\n        generateThumbnail\n    ]);\n    return {\n        generateThumbnail,\n        debouncedGenerateThumbnail,\n        forceRegenerateThumbnail\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\n"));

/***/ })

});