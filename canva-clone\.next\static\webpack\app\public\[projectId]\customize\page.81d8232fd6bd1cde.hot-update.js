"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated, activeLayerId: externalActiveLayerId, onLayerActivation } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize editor first\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{\n            onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n        },\n        saveCallback: ()=>{\n            // Generate preview when canvas changes\n            generatePreview();\n        }\n    });\n    // Generate preview from canvas (defined after editor)\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            const dataUrl = editor.canvas.toDataURL({\n                format: \"png\",\n                quality: 0.9,\n                multiplier: 0.5\n            });\n            onPreviewGenerated(dataUrl);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Initialize canvas (same as main editor)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                initializeCanvas();\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Handle selection changes to notify parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleSelectionCreated = (e)=>{\n            const target = e.target;\n            if (target) {\n                const layerId = target.id;\n                const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n                if (layer) {\n                    onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(layerId);\n                }\n            }\n        };\n        const handleSelectionCleared = ()=>{\n            onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n        };\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (target && target.type === \"textbox\") {\n                const layerId = target.id;\n                const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n                if (layer && layer.type === \"text\") {\n                    const currentText = target.text || \"\";\n                    onCustomizationChange(layerId, currentText);\n                }\n            }\n        };\n        editor.canvas.on(\"selection:created\", handleSelectionCreated);\n        editor.canvas.on(\"selection:updated\", handleSelectionCreated);\n        editor.canvas.on(\"selection:cleared\", handleSelectionCleared);\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        return ()=>{\n            editor.canvas.off(\"selection:created\", handleSelectionCreated);\n            editor.canvas.off(\"selection:updated\", handleSelectionCreated);\n            editor.canvas.off(\"selection:cleared\", handleSelectionCleared);\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        onLayerActivation,\n        onCustomizationChange\n    ]);\n    // Apply customizations from sidebar to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        console.log(\"Applying customizations:\", customizations);\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            console.log(\"Processing layer \".concat(layer.id, \" (\").concat(layer.type, \"):\"), customValue);\n            const canvasObject = editor.canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) {\n                console.log(\"Canvas object not found for layer \".concat(layer.id));\n                return;\n            }\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                const textbox = canvasObject;\n                console.log('Current text: \"'.concat(textbox.text, '\", New text: \"').concat(customValue, '\"'));\n                if (customValue && textbox.text !== customValue) {\n                    console.log(\"Updating text for \".concat(layer.id, ' to: \"').concat(customValue, '\"'));\n                    textbox.set(\"text\", customValue);\n                    editor.canvas.renderAll();\n                    // Trigger save to update the canvas state\n                    if (editor.canvas.fire) {\n                        editor.canvas.fire(\"text:changed\", {\n                            target: textbox\n                        });\n                    }\n                }\n            } else if (layer.type === \"image\" && customValue && customValue.startsWith(\"blob:\")) {\n                console.log(\"Replacing image for \".concat(layer.id, \" with: \").concat(customValue));\n                // Handle image replacement\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!editor.canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY\n                    });\n                    // Set the ID using a custom property\n                    img.id = layer.id;\n                    editor.canvas.remove(canvasObject);\n                    editor.canvas.add(img);\n                    editor.canvas.renderAll();\n                    console.log(\"Image replaced for \".concat(layer.id));\n                });\n            }\n        });\n    }, [\n        customizations,\n        editor,\n        templateData.editableLayers\n    ]);\n    // Ensure canvas objects have proper IDs when editor loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up canvas object IDs for editable layers:\", editableLayerIds);\n        // Wait a bit for the canvas to be fully loaded\n        setTimeout(()=>{\n            const allObjects = canvas.getObjects();\n            console.log(\"All canvas objects after load:\", allObjects.map((obj)=>({\n                    id: obj.id,\n                    type: obj.type,\n                    name: obj.name,\n                    text: obj.type === \"textbox\" ? obj.text : undefined\n                })));\n            // Try to match objects to editable layers by content or position\n            templateData.editableLayers.forEach((layer)=>{\n                let matchedObject = allObjects.find((obj)=>obj.id === layer.id);\n                if (!matchedObject && layer.type === \"text\") {\n                    // Try to find by text content if no ID match\n                    matchedObject = allObjects.find((obj)=>obj.type === \"textbox\" && obj.text === layer.originalValue && !editableLayerIds.includes(obj.id));\n                }\n                if (matchedObject && !matchedObject.id) {\n                    console.log(\"Assigning ID \".concat(layer.id, \" to object:\"), matchedObject);\n                    matchedObject.id = layer.id;\n                }\n            });\n            canvas.renderAll();\n        }, 500);\n    }, [\n        editor,\n        templateData.editableLayers\n    ]);\n    // Function to select object by layer ID (called from parent)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        console.log(\"External active layer changed:\", externalActiveLayerId);\n        if (externalActiveLayerId) {\n            const allObjects = editor.canvas.getObjects();\n            console.log(\"All canvas objects:\", allObjects.map((obj)=>({\n                    id: obj.id,\n                    type: obj.type,\n                    name: obj.name\n                })));\n            const targetObject = allObjects.find((obj)=>obj.id === externalActiveLayerId);\n            console.log(\"Found target object:\", targetObject);\n            if (targetObject) {\n                console.log(\"Selecting object in canvas:\", targetObject.id);\n                editor.canvas.setActiveObject(targetObject);\n                editor.canvas.renderAll();\n            } else {\n                console.log(\"Target object not found for ID:\", externalActiveLayerId);\n            }\n        } else {\n            // Clear selection if no layer is active\n            console.log(\"Clearing canvas selection\");\n            editor.canvas.discardActiveObject();\n            editor.canvas.renderAll();\n        }\n    }, [\n        editor,\n        externalActiveLayerId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_5__.Toolbar, {\n                editor: editor,\n                activeTool: \"select\",\n                onChangeActiveTool: ()=>{}\n            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                ref: containerRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"xWIKbIYg0XVsBz7xgaDlw9unfJI=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});