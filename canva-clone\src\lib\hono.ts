import { hc } from "hono/client";

import { AppType } from "@/app/api/[[...route]]/route";

// Function to get the base URL dynamically
// This ensures the API client works regardless of which port the app is running on
const getBaseUrl = () => {
  // On the server side, use the environment variable
  if (typeof window === 'undefined') {
    return process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  }

  // On the client side, use the current window location
  // This automatically adapts to whatever port Next.js chooses (3000, 3001, etc.)
  return window.location.origin;
};

export const client = hc<AppType>(getBaseUrl());
