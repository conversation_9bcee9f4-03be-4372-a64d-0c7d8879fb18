"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize editor with no-save callback since this is read-only customization\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{},\n        saveCallback: ()=>{}\n    });\n    // Initialize canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (retryCanvas) {\n                    setIsInitialized(true);\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        } else {\n            setIsInitialized(true);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Lock non-editable objects and setup customization mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        // Lock all objects except editable ones\n        canvas.getObjects().forEach((obj)=>{\n            if (obj.name === \"clip\") {\n                // Keep workspace as is but make it non-selectable\n                obj.set({\n                    selectable: false,\n                    evented: false\n                });\n                return;\n            }\n            if (editableLayerIds.includes(obj.id)) {\n                // Make editable objects selectable and editable\n                obj.set({\n                    selectable: true,\n                    hasControls: obj.type === \"textbox\" ? false : true,\n                    hasBorders: true,\n                    lockMovementX: obj.type === \"textbox\" ? true : false,\n                    lockMovementY: obj.type === \"textbox\" ? true : false,\n                    lockRotation: true,\n                    lockScalingX: obj.type === \"textbox\" ? true : false,\n                    lockScalingY: obj.type === \"textbox\" ? true : false,\n                    lockUniScaling: obj.type === \"textbox\" ? true : false,\n                    editable: obj.type === \"textbox\" ? true : false\n                });\n            } else {\n                // Lock non-editable objects completely\n                obj.set({\n                    selectable: false,\n                    evented: false,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n            }\n        });\n        canvas.renderAll();\n    }, [\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue || customValue === layer.originalValue) return;\n            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                canvasObject.set(\"text\", customValue);\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        id: layer.id,\n                        selectable: true,\n                        hasControls: false,\n                        lockMovementX: true,\n                        lockMovementY: true,\n                        lockRotation: true,\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true\n                    });\n                    canvas.remove(canvasObject);\n                    canvas.add(img);\n                    canvas.renderAll();\n                    generatePreview();\n                });\n                return;\n            }\n        });\n        canvas.renderAll();\n        generatePreview();\n    }, [\n        customizations,\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Generate preview from canvas\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            // Create a temporary canvas with just the workspace content\n            const tempCanvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(null, {\n                width: workspace.width,\n                height: workspace.height\n            });\n            // Clone all objects except the workspace itself\n            const objectsToClone = editor.canvas.getObjects().filter((obj)=>obj.name !== \"clip\");\n            objectsToClone.forEach((obj)=>{\n                obj.clone((cloned)=>{\n                    // Adjust position relative to workspace\n                    cloned.set({\n                        left: (cloned.left || 0) - (workspace.left || 0),\n                        top: (cloned.top || 0) - (workspace.top || 0)\n                    });\n                    tempCanvas.add(cloned);\n                });\n            });\n            // Generate preview after a short delay to ensure all objects are added\n            setTimeout(()=>{\n                const dataUrl = tempCanvas.toDataURL({\n                    format: \"png\",\n                    quality: 0.9,\n                    multiplier: 1\n                });\n                onPreviewGenerated(dataUrl);\n                tempCanvas.dispose();\n            }, 100);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Handle text editing\n    const handleTextEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((layerId, value)=>{\n        onCustomizationChange(layerId, value);\n    }, [\n        onCustomizationChange\n    ]);\n    // Handle text editing events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (!layer || layer.type !== \"text\") return;\n            const currentText = target.text || \"\";\n            handleTextEdit(layerId, currentText);\n        };\n        const handleSelectionCreated = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer && layer.type === \"text\") {\n                // Enable editing mode for text objects\n                target.enterEditing();\n            }\n        };\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        editor.canvas.on(\"selection:created\", handleSelectionCreated);\n        return ()=>{\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n            editor.canvas.off(\"selection:created\", handleSelectionCreated);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        handleTextEdit\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    ref: containerRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"KtmFUFpCV+LsnLVV1hCHd/SkL/U=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});