"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@panva";
exports.ids = ["vendor-chunks/@panva"];
exports.modules = {

/***/ "(rsc)/./node_modules/@panva/hkdf/dist/node/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@panva/hkdf/dist/node/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ hkdf),\n/* harmony export */   hkdf: () => (/* binding */ hkdf)\n/* harmony export */ });\n/* harmony import */ var _runtime_hkdf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./runtime/hkdf.js */ \"(rsc)/./node_modules/@panva/hkdf/dist/node/esm/runtime/hkdf.js\");\n\nfunction normalizeDigest(digest) {\n    switch (digest) {\n        case 'sha256':\n        case 'sha384':\n        case 'sha512':\n        case 'sha1':\n            return digest;\n        default:\n            throw new TypeError('unsupported \"digest\" value');\n    }\n}\nfunction normalizeUint8Array(input, label) {\n    if (typeof input === 'string')\n        return new TextEncoder().encode(input);\n    if (!(input instanceof Uint8Array))\n        throw new TypeError(`\"${label}\"\" must be an instance of Uint8Array or a string`);\n    return input;\n}\nfunction normalizeIkm(input) {\n    const ikm = normalizeUint8Array(input, 'ikm');\n    if (!ikm.byteLength)\n        throw new TypeError(`\"ikm\" must be at least one byte in length`);\n    return ikm;\n}\nfunction normalizeInfo(input) {\n    const info = normalizeUint8Array(input, 'info');\n    if (info.byteLength > 1024) {\n        throw TypeError('\"info\" must not contain more than 1024 bytes');\n    }\n    return info;\n}\nfunction normalizeKeylen(input, digest) {\n    if (typeof input !== 'number' || !Number.isInteger(input) || input < 1) {\n        throw new TypeError('\"keylen\" must be a positive integer');\n    }\n    const hashlen = parseInt(digest.substr(3), 10) >> 3 || 20;\n    if (input > 255 * hashlen) {\n        throw new TypeError('\"keylen\" too large');\n    }\n    return input;\n}\nasync function hkdf(digest, ikm, salt, info, keylen) {\n    return (0,_runtime_hkdf_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(normalizeDigest(digest), normalizeIkm(ikm), normalizeUint8Array(salt, 'salt'), normalizeInfo(info), normalizeKeylen(keylen, digest));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@panva/hkdf/dist/node/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@panva/hkdf/dist/node/esm/runtime/fallback.js":
/*!********************************************************************!*\
  !*** ./node_modules/@panva/hkdf/dist/node/esm/runtime/fallback.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((digest, ikm, salt, info, keylen) => {\n    const hashlen = parseInt(digest.substr(3), 10) >> 3 || 20;\n    const prk = (0,crypto__WEBPACK_IMPORTED_MODULE_0__.createHmac)(digest, salt.byteLength ? salt : new Uint8Array(hashlen))\n        .update(ikm)\n        .digest();\n    const N = Math.ceil(keylen / hashlen);\n    const T = new Uint8Array(hashlen * N + info.byteLength + 1);\n    let prev = 0;\n    let start = 0;\n    for (let c = 1; c <= N; c++) {\n        T.set(info, start);\n        T[start + info.byteLength] = c;\n        T.set((0,crypto__WEBPACK_IMPORTED_MODULE_0__.createHmac)(digest, prk)\n            .update(T.subarray(prev, start + info.byteLength + 1))\n            .digest(), start);\n        prev = start;\n        start += hashlen;\n    }\n    return T.slice(0, keylen);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBhbnZhL2hrZGYvZGlzdC9ub2RlL2VzbS9ydW50aW1lL2ZhbGxiYWNrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DO0FBQ3BDLGlFQUFlO0FBQ2Y7QUFDQSxnQkFBZ0Isa0RBQVU7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFFBQVE7QUFDNUI7QUFDQTtBQUNBLGNBQWMsa0RBQVU7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9AcGFudmEvaGtkZi9kaXN0L25vZGUvZXNtL3J1bnRpbWUvZmFsbGJhY2suanM/YzM2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVIbWFjIH0gZnJvbSAnY3J5cHRvJztcbmV4cG9ydCBkZWZhdWx0IChkaWdlc3QsIGlrbSwgc2FsdCwgaW5mbywga2V5bGVuKSA9PiB7XG4gICAgY29uc3QgaGFzaGxlbiA9IHBhcnNlSW50KGRpZ2VzdC5zdWJzdHIoMyksIDEwKSA+PiAzIHx8IDIwO1xuICAgIGNvbnN0IHByayA9IGNyZWF0ZUhtYWMoZGlnZXN0LCBzYWx0LmJ5dGVMZW5ndGggPyBzYWx0IDogbmV3IFVpbnQ4QXJyYXkoaGFzaGxlbikpXG4gICAgICAgIC51cGRhdGUoaWttKVxuICAgICAgICAuZGlnZXN0KCk7XG4gICAgY29uc3QgTiA9IE1hdGguY2VpbChrZXlsZW4gLyBoYXNobGVuKTtcbiAgICBjb25zdCBUID0gbmV3IFVpbnQ4QXJyYXkoaGFzaGxlbiAqIE4gKyBpbmZvLmJ5dGVMZW5ndGggKyAxKTtcbiAgICBsZXQgcHJldiA9IDA7XG4gICAgbGV0IHN0YXJ0ID0gMDtcbiAgICBmb3IgKGxldCBjID0gMTsgYyA8PSBOOyBjKyspIHtcbiAgICAgICAgVC5zZXQoaW5mbywgc3RhcnQpO1xuICAgICAgICBUW3N0YXJ0ICsgaW5mby5ieXRlTGVuZ3RoXSA9IGM7XG4gICAgICAgIFQuc2V0KGNyZWF0ZUhtYWMoZGlnZXN0LCBwcmspXG4gICAgICAgICAgICAudXBkYXRlKFQuc3ViYXJyYXkocHJldiwgc3RhcnQgKyBpbmZvLmJ5dGVMZW5ndGggKyAxKSlcbiAgICAgICAgICAgIC5kaWdlc3QoKSwgc3RhcnQpO1xuICAgICAgICBwcmV2ID0gc3RhcnQ7XG4gICAgICAgIHN0YXJ0ICs9IGhhc2hsZW47XG4gICAgfVxuICAgIHJldHVybiBULnNsaWNlKDAsIGtleWxlbik7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@panva/hkdf/dist/node/esm/runtime/fallback.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@panva/hkdf/dist/node/esm/runtime/hkdf.js":
/*!****************************************************************!*\
  !*** ./node_modules/@panva/hkdf/dist/node/esm/runtime/hkdf.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var _fallback_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fallback.js */ \"(rsc)/./node_modules/@panva/hkdf/dist/node/esm/runtime/fallback.js\");\n\n\nlet hkdf;\nif (typeof crypto__WEBPACK_IMPORTED_MODULE_0__.hkdf === 'function' && !process.versions.electron) {\n    hkdf = async (...args) => new Promise((resolve, reject) => {\n        crypto__WEBPACK_IMPORTED_MODULE_0__.hkdf(...args, (err, arrayBuffer) => {\n            if (err)\n                reject(err);\n            else\n                resolve(new Uint8Array(arrayBuffer));\n        });\n    });\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (digest, ikm, salt, info, keylen) => (hkdf || _fallback_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(digest, ikm, salt, info, keylen));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBhbnZhL2hrZGYvZGlzdC9ub2RlL2VzbS9ydW50aW1lL2hrZGYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlDO0FBQ0k7QUFDckM7QUFDQSxXQUFXLHdDQUFXO0FBQ3RCO0FBQ0EsUUFBUSx3Q0FBVztBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSxpRUFBZSxvREFBb0Qsb0RBQVEsa0NBQWtDLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL0BwYW52YS9oa2RmL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9oa2RmLmpzPzhlMDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5pbXBvcnQgZmFsbGJhY2sgZnJvbSAnLi9mYWxsYmFjay5qcyc7XG5sZXQgaGtkZjtcbmlmICh0eXBlb2YgY3J5cHRvLmhrZGYgPT09ICdmdW5jdGlvbicgJiYgIXByb2Nlc3MudmVyc2lvbnMuZWxlY3Ryb24pIHtcbiAgICBoa2RmID0gYXN5bmMgKC4uLmFyZ3MpID0+IG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgY3J5cHRvLmhrZGYoLi4uYXJncywgKGVyciwgYXJyYXlCdWZmZXIpID0+IHtcbiAgICAgICAgICAgIGlmIChlcnIpXG4gICAgICAgICAgICAgICAgcmVqZWN0KGVycik7XG4gICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgcmVzb2x2ZShuZXcgVWludDhBcnJheShhcnJheUJ1ZmZlcikpO1xuICAgICAgICB9KTtcbiAgICB9KTtcbn1cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIChkaWdlc3QsIGlrbSwgc2FsdCwgaW5mbywga2V5bGVuKSA9PiAoaGtkZiB8fCBmYWxsYmFjaykoZGlnZXN0LCBpa20sIHNhbHQsIGluZm8sIGtleWxlbik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@panva/hkdf/dist/node/esm/runtime/hkdf.js\n");

/***/ })

};
;