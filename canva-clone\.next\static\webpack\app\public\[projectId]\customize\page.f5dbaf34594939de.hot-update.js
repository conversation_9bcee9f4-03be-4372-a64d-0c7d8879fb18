"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [canvasInitialized, setCanvasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Canvas refs for Fabric.js\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fabricCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers safely\n                let editableLayers = [];\n                try {\n                    editableLayers = templateData.editableLayers ? JSON.parse(templateData.editableLayers) : [];\n                } catch (error) {\n                    console.error(\"Error parsing editable layers:\", error);\n                    editableLayers = [];\n                }\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleTextChange = (layerId, value)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        // Create object URL for immediate preview\n        const imageUrl = URL.createObjectURL(file);\n        // Update customizations immediately\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: imageUrl\n            }));\n    // In a real implementation, you'd upload to your storage service here\n    // const uploadedUrl = await uploadToStorage(file);\n    // setCustomizations(prev => ({ ...prev, [layerId]: uploadedUrl }));\n    };\n    // Initialize preview - start with template thumbnail\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!template) return;\n        // Always start with the template thumbnail for immediate preview\n        if (template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n            setCanvasInitialized(true);\n        }\n        // Only initialize Fabric.js canvas if there are editable layers and customizations\n        if (!template.editableLayers || template.editableLayers.length === 0) {\n            return;\n        }\n        // Clean up existing canvas\n        if (fabricCanvasRef.current) {\n            try {\n                fabricCanvasRef.current.dispose();\n            } catch (error) {\n                console.warn(\"Error disposing canvas:\", error);\n            }\n            fabricCanvasRef.current = null;\n        }\n        const initCanvas = async ()=>{\n            if (!canvasRef.current || !template) return;\n            try {\n                // Create a simple canvas for customization preview\n                const canvas = new fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Canvas(canvasRef.current, {\n                    width: template.width,\n                    height: template.height,\n                    selection: false,\n                    preserveObjectStacking: true,\n                    backgroundColor: \"white\"\n                });\n                fabricCanvasRef.current = canvas;\n                // Load template JSON if available\n                if (template.json) {\n                    try {\n                        const templateJson = JSON.parse(template.json);\n                        await new Promise((resolve, reject)=>{\n                            canvas.loadFromJSON(templateJson, ()=>{\n                                try {\n                                    // Remove workspace/clip objects that cause positioning issues\n                                    const objects = canvas.getObjects();\n                                    const workspace = objects.find((obj)=>obj.name === \"clip\");\n                                    if (workspace) {\n                                        canvas.remove(workspace);\n                                    }\n                                    // Remove any other editor-specific objects\n                                    objects.forEach((obj)=>{\n                                        if (obj.name === \"clip\" || obj.selectable === false) {\n                                            canvas.remove(obj);\n                                        }\n                                    });\n                                    canvas.renderAll();\n                                    resolve();\n                                } catch (error) {\n                                    reject(error);\n                                }\n                            }, (error)=>{\n                                reject(error);\n                            });\n                        });\n                    } catch (jsonError) {\n                        console.error(\"Error parsing template JSON:\", jsonError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize canvas:\", error);\n            }\n        };\n        // Delay initialization to ensure DOM is ready\n        const timeoutId = setTimeout(initCanvas, 200);\n        return ()=>{\n            clearTimeout(timeoutId);\n            if (fabricCanvasRef.current) {\n                try {\n                    fabricCanvasRef.current.dispose();\n                } catch (error) {\n                    console.warn(\"Error disposing canvas in cleanup:\", error);\n                }\n                fabricCanvasRef.current = null;\n            }\n        };\n    }, [\n        template\n    ]);\n    // Generate preview from canvas\n    const generatePreviewFromCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!fabricCanvasRef.current || !template) {\n            // Fallback to template thumbnail\n            if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n            return;\n        }\n        try {\n            const canvas = fabricCanvasRef.current;\n            // Calculate appropriate multiplier for good quality but reasonable size\n            const maxPreviewSize = 800; // Max width or height for preview\n            const templateMaxDimension = Math.max(template.width, template.height);\n            const multiplier = Math.min(1, maxPreviewSize / templateMaxDimension);\n            const dataURL = canvas.toDataURL({\n                format: \"png\",\n                quality: 0.9,\n                multiplier: multiplier\n            });\n            setPreviewUrl(dataURL);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n            // Fallback to template thumbnail\n            if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n        }\n    }, [\n        template\n    ]);\n    // Apply customizations to canvas objects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!fabricCanvasRef.current || !canvasInitialized || !template || !template.editableLayers) return;\n        try {\n            const canvas = fabricCanvasRef.current;\n            const objects = canvas.getObjects();\n            // Apply customizations to editable objects\n            template.editableLayers.forEach((layer)=>{\n                const customValue = customizations[layer.id];\n                if (!customValue) return;\n                // Find the canvas object by ID\n                const canvasObject = objects.find((obj)=>obj.id === layer.id);\n                if (!canvasObject) return;\n                if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                    // Apply text customization\n                    canvasObject.set(\"text\", customValue);\n                } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                    // Apply image customization\n                    fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Image.fromURL(customValue, (img)=>{\n                        if (!fabricCanvasRef.current) return;\n                        try {\n                            // Scale image to fit the object bounds\n                            const scaleX = canvasObject.width / img.width;\n                            const scaleY = canvasObject.height / img.height;\n                            const scale = Math.min(scaleX, scaleY);\n                            img.set({\n                                left: canvasObject.left,\n                                top: canvasObject.top,\n                                scaleX: scale,\n                                scaleY: scale,\n                                originX: canvasObject.originX,\n                                originY: canvasObject.originY,\n                                id: layer.id\n                            });\n                            // Replace the object\n                            fabricCanvasRef.current.remove(canvasObject);\n                            fabricCanvasRef.current.add(img);\n                            fabricCanvasRef.current.renderAll();\n                            generatePreviewFromCanvas();\n                        } catch (error) {\n                            console.error(\"Error applying image customization:\", error);\n                        }\n                    });\n                    return; // Skip the renderAll below since it's handled in the callback\n                }\n            });\n            canvas.renderAll();\n            generatePreviewFromCanvas();\n        } catch (error) {\n            console.error(\"Error applying customizations:\", error);\n        }\n    }, [\n        customizations,\n        canvasInitialized,\n        template,\n        generatePreviewFromCanvas\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Generate preview from current canvas state\n            generatePreviewFromCanvas();\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // In a real implementation, you'd get a download URL from the API\n            const link = document.createElement(\"a\");\n            link.href = (template === null || template === void 0 ? void 0 : template.thumbnailUrl) || \"\";\n            link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n            link.click();\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 337,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 345,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Customize Elements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: customizations[layer.id] || \"\",\n                                                                onChange: (e)=>handleTextChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 483,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 484,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center bg-gray-100 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-lg overflow-hidden flex items-center justify-center\",\n                                                style: {\n                                                    aspectRatio: \"\".concat(template.width, \"/\").concat(template.height),\n                                                    maxWidth: \"100%\",\n                                                    maxHeight: \"500px\",\n                                                    width: \"auto\",\n                                                    height: \"auto\"\n                                                },\n                                                children: previewUrl || template.thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: previewUrl || template.thumbnailUrl || \"\",\n                                                    alt: \"Template preview\",\n                                                    className: \"max-w-full max-h-full object-contain\",\n                                                    style: {\n                                                        width: \"auto\",\n                                                        height: \"auto\",\n                                                        maxWidth: \"100%\",\n                                                        maxHeight: \"100%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center bg-gray-50 w-full h-full min-h-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    display: \"none\"\n                },\n                width: (template === null || template === void 0 ? void 0 : template.width) || 800,\n                height: (template === null || template === void 0 ? void 0 : template.height) || 600\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 558,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"w0bUFtRIeJ9lE84wprAeFbjL2rs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ })

});