"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeLayerId, setActiveLayerId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize editor with no-save callback since this is read-only customization\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{},\n        saveCallback: ()=>{}\n    });\n    // Initialize canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (retryCanvas) {\n                    setIsInitialized(true);\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        } else {\n            setIsInitialized(true);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Lock non-editable objects and setup customization mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up customization mode:\", {\n            editableLayerIds,\n            totalObjects: canvas.getObjects().length,\n            editableLayers: templateData.editableLayers\n        });\n        // Lock all objects except editable ones\n        canvas.getObjects().forEach((obj)=>{\n            console.log(\"Processing object:\", {\n                id: obj.id,\n                type: obj.type,\n                name: obj.name,\n                isEditable: editableLayerIds.includes(obj.id)\n            });\n            if (obj.name === \"clip\") {\n                // Keep workspace as is but make it non-selectable\n                obj.set({\n                    selectable: false,\n                    evented: false\n                });\n                return;\n            }\n            if (editableLayerIds.includes(obj.id)) {\n                console.log(\"Making object editable:\", obj.id, obj.type);\n                // Make editable objects selectable and editable\n                obj.set({\n                    selectable: true,\n                    hasControls: false,\n                    hasBorders: true,\n                    borderColor: \"#3b82f6\",\n                    borderScaleFactor: 2,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true,\n                    editable: obj.type === \"textbox\" ? true : false,\n                    hoverCursor: \"pointer\",\n                    moveCursor: \"pointer\"\n                });\n                // Add visual indicator for editable elements\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: \"rgba(59, 130, 246, 0.1)\"\n                    });\n                }\n            } else {\n                // Lock non-editable objects completely\n                obj.set({\n                    selectable: false,\n                    evented: false,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n            }\n        });\n        canvas.renderAll();\n    }, [\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Update visuals when active layer changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateEditableObjectsVisuals();\n    }, [\n        activeLayerId,\n        updateEditableObjectsVisuals\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue || customValue === layer.originalValue) return;\n            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                canvasObject.set(\"text\", customValue);\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        id: layer.id,\n                        selectable: true,\n                        hasControls: false,\n                        lockMovementX: true,\n                        lockMovementY: true,\n                        lockRotation: true,\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true\n                    });\n                    canvas.remove(canvasObject);\n                    canvas.add(img);\n                    canvas.renderAll();\n                    generatePreview();\n                });\n                return;\n            }\n        });\n        canvas.renderAll();\n        generatePreview();\n    }, [\n        customizations,\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Generate preview from canvas\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            // Create a temporary canvas with just the workspace content\n            const tempCanvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(null, {\n                width: workspace.width,\n                height: workspace.height\n            });\n            // Clone all objects except the workspace itself\n            const objectsToClone = editor.canvas.getObjects().filter((obj)=>obj.name !== \"clip\");\n            objectsToClone.forEach((obj)=>{\n                obj.clone((cloned)=>{\n                    // Adjust position relative to workspace\n                    cloned.set({\n                        left: (cloned.left || 0) - (workspace.left || 0),\n                        top: (cloned.top || 0) - (workspace.top || 0)\n                    });\n                    tempCanvas.add(cloned);\n                });\n            });\n            // Generate preview after a short delay to ensure all objects are added\n            setTimeout(()=>{\n                const dataUrl = tempCanvas.toDataURL({\n                    format: \"png\",\n                    quality: 0.9,\n                    multiplier: 1\n                });\n                onPreviewGenerated(dataUrl);\n                tempCanvas.dispose();\n            }, 100);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Handle text editing\n    const handleTextEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((layerId, value)=>{\n        onCustomizationChange(layerId, value);\n    }, [\n        onCustomizationChange\n    ]);\n    // Update visual feedback for editable objects\n    const updateEditableObjectsVisuals = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        editor.canvas.getObjects().forEach((obj)=>{\n            if (editableLayerIds.includes(obj.id)) {\n                const isActive = obj.id === activeLayerId;\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: isActive ? \"rgba(59, 130, 246, 0.2)\" // Darker blue when active\n                         : \"rgba(59, 130, 246, 0.1)\",\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2\n                    });\n                } else {\n                    // For image layers\n                    obj.set({\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2,\n                        opacity: isActive ? 1 : 0.8\n                    });\n                }\n            }\n        });\n        editor.canvas.renderAll();\n    }, [\n        editor,\n        templateData.editableLayers,\n        activeLayerId\n    ]);\n    // Handle click events and layer activation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleObjectSelection = (e)=>{\n            const target = e.target;\n            if (!target) {\n                setActiveLayerId(null);\n                return;\n            }\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer) {\n                console.log(\"Layer activated:\", layer);\n                setActiveLayerId(layerId);\n                // Update visual feedback for all editable objects\n                updateEditableObjectsVisuals();\n            } else {\n                setActiveLayerId(null);\n            }\n        };\n        const handleCanvasClick = (e)=>{\n            if (!e.target) {\n                setActiveLayerId(null);\n                updateEditableObjectsVisuals();\n            }\n        };\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (!layer || layer.type !== \"text\") return;\n            const currentText = target.text || \"\";\n            handleTextEdit(layerId, currentText);\n            generatePreview();\n        };\n        const handleDoubleClick = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer && layer.type === \"text\") {\n                setActiveLayerId(layerId);\n                // Enter editing mode\n                target.enterEditing();\n                target.selectAll();\n            }\n        };\n        editor.canvas.on(\"selection:created\", handleObjectSelection);\n        editor.canvas.on(\"selection:updated\", handleObjectSelection);\n        editor.canvas.on(\"selection:cleared\", ()=>setActiveLayerId(null));\n        editor.canvas.on(\"mouse:down\", handleCanvasClick);\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        editor.canvas.on(\"mouse:dblclick\", handleDoubleClick);\n        return ()=>{\n            editor.canvas.off(\"selection:created\", handleObjectSelection);\n            editor.canvas.off(\"selection:updated\", handleObjectSelection);\n            editor.canvas.off(\"selection:cleared\", ()=>setActiveLayerId(null));\n            editor.canvas.off(\"mouse:down\", handleCanvasClick);\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n            editor.canvas.off(\"mouse:dblclick\", handleDoubleClick);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        handleTextEdit,\n        generatePreview\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    ref: containerRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 385,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"3HmZcOUthtzHc7Ic02kxwkEq44w=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});