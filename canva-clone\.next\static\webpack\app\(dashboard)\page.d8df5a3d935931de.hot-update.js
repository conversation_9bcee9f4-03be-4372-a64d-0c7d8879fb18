"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/projects-section.tsx":
/*!**************************************************!*\
  !*** ./src/app/(dashboard)/projects-section.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsSection: function() { return /* binding */ ProjectsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _features_projects_api_use_get_projects__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/projects/api/use-get-projects */ \"(app-pages-browser)/./src/features/projects/api/use-get-projects.ts\");\n/* harmony import */ var _features_projects_api_use_delete_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-delete-project */ \"(app-pages-browser)/./src/features/projects/api/use-delete-project.ts\");\n/* harmony import */ var _features_projects_api_use_duplicate_project__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/projects/api/use-duplicate-project */ \"(app-pages-browser)/./src/features/projects/api/use-duplicate-project.ts\");\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_confirm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-confirm */ \"(app-pages-browser)/./src/hooks/use-confirm.tsx\");\n/* harmony import */ var _project_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./project-card */ \"(app-pages-browser)/./src/app/(dashboard)/project-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst ProjectsSection = ()=>{\n    _s();\n    const [ConfirmDialog, confirm] = (0,_hooks_use_confirm__WEBPACK_IMPORTED_MODULE_8__.useConfirm)(\"Are you sure?\", \"You are about to delete this project.\");\n    const [updatingProjectId, setUpdatingProjectId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const duplicateMutation = (0,_features_projects_api_use_duplicate_project__WEBPACK_IMPORTED_MODULE_5__.useDuplicateProject)();\n    const removeMutation = (0,_features_projects_api_use_delete_project__WEBPACK_IMPORTED_MODULE_4__.useDeleteProject)();\n    const updateMutation = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_6__.useUpdateProject)(updatingProjectId || \"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const onCopy = (id)=>{\n        duplicateMutation.mutate({\n            id\n        });\n    };\n    const onDelete = async (id)=>{\n        const ok = await confirm();\n        if (ok) {\n            removeMutation.mutate({\n                id\n            });\n        }\n    };\n    const onTogglePublic = (id, isPublic)=>{\n        setUpdatingProjectId(id);\n        updateMutation.mutate({\n            isPublic\n        });\n    };\n    const onConfigureTemplate = (id)=>{\n        // Navigate to the editor with template configuration mode\n        router.push(\"/editor/\".concat(id, \"?mode=template-config\"));\n    };\n    const { data, status, fetchNextPage, isFetchingNextPage, hasNextPage } = (0,_features_projects_api_use_get_projects__WEBPACK_IMPORTED_MODULE_3__.useGetProjects)();\n    if (status === \"pending\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"font-semibold text-lg\",\n                    children: \"Recent projects\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-y-4 items-center justify-center h-32\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"size-6 animate-spin text-muted-foreground\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (status === \"error\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"font-semibold text-lg\",\n                    children: \"Recent projects\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-y-4 items-center justify-center h-32\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"size-6 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground text-sm\",\n                            children: \"Failed to load projects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!data.pages.length || !data.pages[0].data.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"font-semibold text-lg\",\n                    children: \"Recent projects\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-y-4 items-center justify-center h-32\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"size-6 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground text-sm\",\n                            children: \"No projects found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConfirmDialog, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"font-semibold text-lg\",\n                children: \"Recent projects\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6\",\n                children: data.pages.map((group, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                        children: group.data.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_project_card__WEBPACK_IMPORTED_MODULE_9__.ProjectCard, {\n                                id: project.id,\n                                name: project.name,\n                                width: project.width,\n                                height: project.height,\n                                thumbnailUrl: project.thumbnailUrl,\n                                updatedAt: new Date(project.updatedAt),\n                                isPublic: project.isPublic || false,\n                                isCustomizable: project.isCustomizable || false,\n                                onClick: ()=>router.push(\"/editor/\".concat(project.id)),\n                                onCopy: onCopy,\n                                onDelete: onDelete,\n                                onTogglePublic: onTogglePublic,\n                                onConfigureTemplate: onConfigureTemplate,\n                                copyLoading: duplicateMutation.isPending,\n                                deleteLoading: removeMutation.isPending,\n                                togglePublicLoading: updateMutation.isPending && updatingProjectId === project.id\n                            }, project.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, undefined))\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined),\n            hasNextPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex items-center justify-center pt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                    variant: \"ghost\",\n                    onClick: ()=>fetchNextPage(),\n                    disabled: isFetchingNextPage,\n                    children: isFetchingNextPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"size-4 mr-2 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 17\n                            }, undefined),\n                            \"Loading...\"\n                        ]\n                    }, void 0, true) : \"Load more\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\projects-section.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProjectsSection, \"xSdnWiI7fYBLfpPab+j9rXCyEic=\", false, function() {\n    return [\n        _hooks_use_confirm__WEBPACK_IMPORTED_MODULE_8__.useConfirm,\n        _features_projects_api_use_duplicate_project__WEBPACK_IMPORTED_MODULE_5__.useDuplicateProject,\n        _features_projects_api_use_delete_project__WEBPACK_IMPORTED_MODULE_4__.useDeleteProject,\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_6__.useUpdateProject,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _features_projects_api_use_get_projects__WEBPACK_IMPORTED_MODULE_3__.useGetProjects\n    ];\n});\n_c = ProjectsSection;\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/projects-section.tsx\n"));

/***/ })

});