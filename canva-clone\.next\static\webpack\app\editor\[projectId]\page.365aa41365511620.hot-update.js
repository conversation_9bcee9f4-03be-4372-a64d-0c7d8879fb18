"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_utils_thumbnail__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/utils/thumbnail */ \"(app-pages-browser)/./src/features/editor/utils/thumbnail.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        const data = JSON.parse(json);\n        canvas.loadFromJSON(data, ()=>{\n            autoZoom();\n        });\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        generateThumbnail: (options)=>{\n            return (0,_features_editor_utils_thumbnail__WEBPACK_IMPORTED_MODULE_5__.generateThumbnail)(canvas, options);\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_7__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_8__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_9__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_6__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        // Fix fabric.js textBaseline issue\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Text.prototype.set({\n            textBaseline: \"middle\"\n        });\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox.prototype.set({\n            textBaseline: \"middle\"\n        });\n        // Ensure we have valid dimensions\n        const containerWidth = initialContainer.offsetWidth || 800;\n        const containerHeight = initialContainer.offsetHeight || 600;\n        const workspaceWidth = initialWidth.current || 900;\n        const workspaceHeight = initialHeight.current || 1200;\n        // Validate dimensions\n        if (workspaceWidth <= 0 || workspaceHeight <= 0) {\n            console.error(\"Invalid workspace dimensions:\", {\n                workspaceWidth,\n                workspaceHeight\n            });\n            return;\n        }\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: workspaceWidth,\n            height: workspaceHeight,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(containerWidth);\n        initialCanvas.setHeight(containerHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ })

});