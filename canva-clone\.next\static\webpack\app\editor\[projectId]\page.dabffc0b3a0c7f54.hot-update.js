"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts":
/*!**************************************************************!*\
  !*** ./src/features/editor/hooks/use-thumbnail-generator.ts ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useThumbnailGenerator: function() { return /* binding */ useThumbnailGenerator; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hono__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hono */ \"(app-pages-browser)/./src/lib/hono.ts\");\n\n\n\nconst useThumbnailGenerator = (param)=>{\n    let { editor, projectId, onThumbnailGenerated } = param;\n    const lastThumbnailRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const generateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!editor) {\n            console.warn(\"No editor available for thumbnail generation\");\n            return;\n        }\n        if (!editor.canvas) {\n            console.warn(\"No canvas available for thumbnail generation\");\n            return;\n        }\n        try {\n            console.log(\"Generating thumbnail for project:\", projectId);\n            // Generate thumbnail data URL with smaller size for testing\n            const thumbnailDataUrl = editor.generateThumbnail({\n                width: 300,\n                height: 200,\n                quality: 0.8\n            });\n            if (!thumbnailDataUrl) {\n                console.error(\"Failed to generate thumbnail data URL\");\n                return;\n            }\n            console.log(\"Generated thumbnail data URL length:\", thumbnailDataUrl.length);\n            console.log(\"Thumbnail data URL preview:\", thumbnailDataUrl.substring(0, 100) + \"...\");\n            // Test if data URL is valid\n            const isValidDataUrl = thumbnailDataUrl.startsWith(\"data:image/\");\n            console.log(\"Is valid data URL:\", isValidDataUrl);\n            // Test if we can create an image from it\n            const testImg = new Image();\n            testImg.onload = ()=>console.log(\"Data URL is valid image\");\n            testImg.onerror = (e)=>console.error(\"Data URL is invalid:\", e);\n            testImg.src = thumbnailDataUrl;\n            // Skip if thumbnail hasn't changed significantly\n            if (lastThumbnailRef.current === thumbnailDataUrl) {\n                console.log(\"Thumbnail unchanged, skipping update\");\n                return;\n            }\n            lastThumbnailRef.current = thumbnailDataUrl;\n            console.log(\"Thumbnail generated, updating project...\");\n            // For now, let's store the thumbnail as a data URL directly in the database\n            // This is simpler and doesn't require external file uploads\n            // In production, you might want to upload to a CDN\n            // Update project with thumbnail data URL\n            const response = await _lib_hono__WEBPACK_IMPORTED_MODULE_2__.client.api.projects[\":id\"].$patch({\n                param: {\n                    id: projectId\n                },\n                json: {\n                    thumbnailUrl: thumbnailDataUrl\n                }\n            });\n            if (response.ok) {\n                console.log(\"Thumbnail updated successfully\");\n                const responseData = await response.json();\n                console.log(\"Response data:\", responseData);\n                onThumbnailGenerated === null || onThumbnailGenerated === void 0 ? void 0 : onThumbnailGenerated(thumbnailDataUrl);\n            } else {\n                console.error(\"Failed to update thumbnail:\", response.status, response.statusText);\n                const errorText = await response.text();\n                console.error(\"Error response:\", errorText);\n            }\n        } catch (error) {\n            console.error(\"Error generating thumbnail:\", error);\n        }\n    }, [\n        editor,\n        projectId,\n        onThumbnailGenerated\n    ]);\n    // Debounced version to avoid too frequent thumbnail generation\n    const debouncedGenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default()(generateThumbnail, 2000), [\n        generateThumbnail\n    ]);\n    const forceRegenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        // Reset the last thumbnail to force regeneration\n        lastThumbnailRef.current = null;\n        await generateThumbnail();\n    }, [\n        generateThumbnail\n    ]);\n    return {\n        generateThumbnail,\n        debouncedGenerateThumbnail,\n        forceRegenerateThumbnail\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\n"));

/***/ })

});