"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth4webapi";
exports.ids = ["vendor-chunks/oauth4webapi"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth4webapi/build/index.js":
/*!**************************************************!*\
  !*** ./node_modules/oauth4webapi/build/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OperationProcessingError: () => (/* binding */ OperationProcessingError),\n/* harmony export */   UnsupportedOperationError: () => (/* binding */ UnsupportedOperationError),\n/* harmony export */   authorizationCodeGrantRequest: () => (/* binding */ authorizationCodeGrantRequest),\n/* harmony export */   calculatePKCECodeChallenge: () => (/* binding */ calculatePKCECodeChallenge),\n/* harmony export */   clientCredentialsGrantRequest: () => (/* binding */ clientCredentialsGrantRequest),\n/* harmony export */   clockSkew: () => (/* binding */ clockSkew),\n/* harmony export */   clockTolerance: () => (/* binding */ clockTolerance),\n/* harmony export */   customFetch: () => (/* binding */ customFetch),\n/* harmony export */   deviceAuthorizationRequest: () => (/* binding */ deviceAuthorizationRequest),\n/* harmony export */   deviceCodeGrantRequest: () => (/* binding */ deviceCodeGrantRequest),\n/* harmony export */   discoveryRequest: () => (/* binding */ discoveryRequest),\n/* harmony export */   expectNoNonce: () => (/* binding */ expectNoNonce),\n/* harmony export */   expectNoState: () => (/* binding */ expectNoState),\n/* harmony export */   experimentalCustomFetch: () => (/* binding */ experimentalCustomFetch),\n/* harmony export */   experimentalUseMtlsAlias: () => (/* binding */ experimentalUseMtlsAlias),\n/* harmony export */   experimental_customFetch: () => (/* binding */ experimental_customFetch),\n/* harmony export */   experimental_jwksCache: () => (/* binding */ experimental_jwksCache),\n/* harmony export */   experimental_useMtlsAlias: () => (/* binding */ experimental_useMtlsAlias),\n/* harmony export */   experimental_validateDetachedSignatureResponse: () => (/* binding */ experimental_validateDetachedSignatureResponse),\n/* harmony export */   experimental_validateJwtAccessToken: () => (/* binding */ experimental_validateJwtAccessToken),\n/* harmony export */   generateKeyPair: () => (/* binding */ generateKeyPair),\n/* harmony export */   generateRandomCodeVerifier: () => (/* binding */ generateRandomCodeVerifier),\n/* harmony export */   generateRandomNonce: () => (/* binding */ generateRandomNonce),\n/* harmony export */   generateRandomState: () => (/* binding */ generateRandomState),\n/* harmony export */   getValidatedIdTokenClaims: () => (/* binding */ getValidatedIdTokenClaims),\n/* harmony export */   introspectionRequest: () => (/* binding */ introspectionRequest),\n/* harmony export */   isOAuth2Error: () => (/* binding */ isOAuth2Error),\n/* harmony export */   issueRequestObject: () => (/* binding */ issueRequestObject),\n/* harmony export */   parseWwwAuthenticateChallenges: () => (/* binding */ parseWwwAuthenticateChallenges),\n/* harmony export */   processAuthorizationCodeOAuth2Response: () => (/* binding */ processAuthorizationCodeOAuth2Response),\n/* harmony export */   processAuthorizationCodeOpenIDResponse: () => (/* binding */ processAuthorizationCodeOpenIDResponse),\n/* harmony export */   processClientCredentialsResponse: () => (/* binding */ processClientCredentialsResponse),\n/* harmony export */   processDeviceAuthorizationResponse: () => (/* binding */ processDeviceAuthorizationResponse),\n/* harmony export */   processDeviceCodeResponse: () => (/* binding */ processDeviceCodeResponse),\n/* harmony export */   processDiscoveryResponse: () => (/* binding */ processDiscoveryResponse),\n/* harmony export */   processIntrospectionResponse: () => (/* binding */ processIntrospectionResponse),\n/* harmony export */   processPushedAuthorizationResponse: () => (/* binding */ processPushedAuthorizationResponse),\n/* harmony export */   processRefreshTokenResponse: () => (/* binding */ processRefreshTokenResponse),\n/* harmony export */   processRevocationResponse: () => (/* binding */ processRevocationResponse),\n/* harmony export */   processUserInfoResponse: () => (/* binding */ processUserInfoResponse),\n/* harmony export */   protectedResourceRequest: () => (/* binding */ protectedResourceRequest),\n/* harmony export */   pushedAuthorizationRequest: () => (/* binding */ pushedAuthorizationRequest),\n/* harmony export */   refreshTokenGrantRequest: () => (/* binding */ refreshTokenGrantRequest),\n/* harmony export */   revocationRequest: () => (/* binding */ revocationRequest),\n/* harmony export */   skipAuthTimeCheck: () => (/* binding */ skipAuthTimeCheck),\n/* harmony export */   skipStateCheck: () => (/* binding */ skipStateCheck),\n/* harmony export */   skipSubjectCheck: () => (/* binding */ skipSubjectCheck),\n/* harmony export */   useMtlsAlias: () => (/* binding */ useMtlsAlias),\n/* harmony export */   userInfoRequest: () => (/* binding */ userInfoRequest),\n/* harmony export */   validateAuthResponse: () => (/* binding */ validateAuthResponse),\n/* harmony export */   validateDetachedSignatureResponse: () => (/* binding */ validateDetachedSignatureResponse),\n/* harmony export */   validateJwtAccessToken: () => (/* binding */ validateJwtAccessToken),\n/* harmony export */   validateJwtAuthResponse: () => (/* binding */ validateJwtAuthResponse)\n/* harmony export */ });\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'oauth4webapi';\n    const VERSION = 'v2.11.1';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nfunction looseInstanceOf(input, expected) {\n    if (input == null) {\n        return false;\n    }\n    try {\n        return (input instanceof expected ||\n            Object.getPrototypeOf(input)[Symbol.toStringTag] === expected.prototype[Symbol.toStringTag]);\n    }\n    catch {\n        return false;\n    }\n}\nconst clockSkew = Symbol();\nconst clockTolerance = Symbol();\nconst customFetch = Symbol();\nconst experimental_jwksCache = Symbol();\nconst useMtlsAlias = Symbol();\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nfunction buf(input) {\n    if (typeof input === 'string') {\n        return encoder.encode(input);\n    }\n    return decoder.decode(input);\n}\nconst CHUNK_SIZE = 0x8000;\nfunction encodeBase64Url(input) {\n    if (input instanceof ArrayBuffer) {\n        input = new Uint8Array(input);\n    }\n    const arr = [];\n    for (let i = 0; i < input.byteLength; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join('')).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\nfunction decodeBase64Url(input) {\n    try {\n        const binary = atob(input.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, ''));\n        const bytes = new Uint8Array(binary.length);\n        for (let i = 0; i < binary.length; i++) {\n            bytes[i] = binary.charCodeAt(i);\n        }\n        return bytes;\n    }\n    catch (cause) {\n        throw new OPE('The input to be decoded is not correctly encoded.', { cause });\n    }\n}\nfunction b64u(input) {\n    if (typeof input === 'string') {\n        return decodeBase64Url(input);\n    }\n    return encodeBase64Url(input);\n}\nclass LRU {\n    constructor(maxSize) {\n        this.cache = new Map();\n        this._cache = new Map();\n        this.maxSize = maxSize;\n    }\n    get(key) {\n        let v = this.cache.get(key);\n        if (v) {\n            return v;\n        }\n        if ((v = this._cache.get(key))) {\n            this.update(key, v);\n            return v;\n        }\n        return undefined;\n    }\n    has(key) {\n        return this.cache.has(key) || this._cache.has(key);\n    }\n    set(key, value) {\n        if (this.cache.has(key)) {\n            this.cache.set(key, value);\n        }\n        else {\n            this.update(key, value);\n        }\n        return this;\n    }\n    delete(key) {\n        if (this.cache.has(key)) {\n            return this.cache.delete(key);\n        }\n        if (this._cache.has(key)) {\n            return this._cache.delete(key);\n        }\n        return false;\n    }\n    update(key, value) {\n        this.cache.set(key, value);\n        if (this.cache.size >= this.maxSize) {\n            this._cache = this.cache;\n            this.cache = new Map();\n        }\n    }\n}\nclass UnsupportedOperationError extends Error {\n    constructor(message) {\n        super(message ?? 'operation not supported');\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass OperationProcessingError extends Error {\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nconst OPE = OperationProcessingError;\nconst dpopNonces = new LRU(100);\nfunction isCryptoKey(key) {\n    return key instanceof CryptoKey;\n}\nfunction isPrivateKey(key) {\n    return isCryptoKey(key) && key.type === 'private';\n}\nfunction isPublicKey(key) {\n    return isCryptoKey(key) && key.type === 'public';\n}\nconst SUPPORTED_JWS_ALGS = [\n    'PS256',\n    'ES256',\n    'RS256',\n    'PS384',\n    'ES384',\n    'RS384',\n    'PS512',\n    'ES512',\n    'RS512',\n    'EdDSA',\n];\nfunction processDpopNonce(response) {\n    try {\n        const nonce = response.headers.get('dpop-nonce');\n        if (nonce) {\n            dpopNonces.set(new URL(response.url).origin, nonce);\n        }\n    }\n    catch { }\n    return response;\n}\nfunction normalizeTyp(value) {\n    return value.toLowerCase().replace(/^application\\//, '');\n}\nfunction isJsonObject(input) {\n    if (input === null || typeof input !== 'object' || Array.isArray(input)) {\n        return false;\n    }\n    return true;\n}\nfunction prepareHeaders(input) {\n    if (looseInstanceOf(input, Headers)) {\n        input = Object.fromEntries(input.entries());\n    }\n    const headers = new Headers(input);\n    if (USER_AGENT && !headers.has('user-agent')) {\n        headers.set('user-agent', USER_AGENT);\n    }\n    if (headers.has('authorization')) {\n        throw new TypeError('\"options.headers\" must not include the \"authorization\" header name');\n    }\n    if (headers.has('dpop')) {\n        throw new TypeError('\"options.headers\" must not include the \"dpop\" header name');\n    }\n    return headers;\n}\nfunction signal(value) {\n    if (typeof value === 'function') {\n        value = value();\n    }\n    if (!(value instanceof AbortSignal)) {\n        throw new TypeError('\"options.signal\" must return or be an instance of AbortSignal');\n    }\n    return value;\n}\nasync function discoveryRequest(issuerIdentifier, options) {\n    if (!(issuerIdentifier instanceof URL)) {\n        throw new TypeError('\"issuerIdentifier\" must be an instance of URL');\n    }\n    if (issuerIdentifier.protocol !== 'https:' && issuerIdentifier.protocol !== 'http:') {\n        throw new TypeError('\"issuer.protocol\" must be \"https:\" or \"http:\"');\n    }\n    const url = new URL(issuerIdentifier.href);\n    switch (options?.algorithm) {\n        case undefined:\n        case 'oidc':\n            url.pathname = `${url.pathname}/.well-known/openid-configuration`.replace('//', '/');\n            break;\n        case 'oauth2':\n            if (url.pathname === '/') {\n                url.pathname = '.well-known/oauth-authorization-server';\n            }\n            else {\n                url.pathname = `.well-known/oauth-authorization-server/${url.pathname}`.replace('//', '/');\n            }\n            break;\n        default:\n            throw new TypeError('\"options.algorithm\" must be \"oidc\" (default), or \"oauth2\"');\n    }\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nfunction validateString(input) {\n    return typeof input === 'string' && input.length !== 0;\n}\nasync function processDiscoveryResponse(expectedIssuerIdentifier, response) {\n    if (!(expectedIssuerIdentifier instanceof URL)) {\n        throw new TypeError('\"expectedIssuer\" must be an instance of URL');\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform Authorization Server Metadata response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.issuer)) {\n        throw new OPE('\"response\" body \"issuer\" property must be a non-empty string');\n    }\n    if (new URL(json.issuer).href !== expectedIssuerIdentifier.href) {\n        throw new OPE('\"response\" body \"issuer\" does not match \"expectedIssuer\"');\n    }\n    return json;\n}\nfunction randomBytes() {\n    return b64u(crypto.getRandomValues(new Uint8Array(32)));\n}\nfunction generateRandomCodeVerifier() {\n    return randomBytes();\n}\nfunction generateRandomState() {\n    return randomBytes();\n}\nfunction generateRandomNonce() {\n    return randomBytes();\n}\nasync function calculatePKCECodeChallenge(codeVerifier) {\n    if (!validateString(codeVerifier)) {\n        throw new TypeError('\"codeVerifier\" must be a non-empty string');\n    }\n    return b64u(await crypto.subtle.digest('SHA-256', buf(codeVerifier)));\n}\nfunction getKeyAndKid(input) {\n    if (input instanceof CryptoKey) {\n        return { key: input };\n    }\n    if (!(input?.key instanceof CryptoKey)) {\n        return {};\n    }\n    if (input.kid !== undefined && !validateString(input.kid)) {\n        throw new TypeError('\"kid\" must be a non-empty string');\n    }\n    return { key: input.key, kid: input.kid };\n}\nfunction formUrlEncode(token) {\n    return encodeURIComponent(token).replace(/%20/g, '+');\n}\nfunction clientSecretBasic(clientId, clientSecret) {\n    const username = formUrlEncode(clientId);\n    const password = formUrlEncode(clientSecret);\n    const credentials = btoa(`${username}:${password}`);\n    return `Basic ${credentials}`;\n}\nfunction psAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'PS256';\n        case 'SHA-384':\n            return 'PS384';\n        case 'SHA-512':\n            return 'PS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name');\n    }\n}\nfunction rsAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'RS256';\n        case 'SHA-384':\n            return 'RS384';\n        case 'SHA-512':\n            return 'RS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name');\n    }\n}\nfunction esAlg(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n            return 'ES256';\n        case 'P-384':\n            return 'ES384';\n        case 'P-521':\n            return 'ES512';\n        default:\n            throw new UnsupportedOperationError('unsupported EcKeyAlgorithm namedCurve');\n    }\n}\nfunction keyToJws(key) {\n    switch (key.algorithm.name) {\n        case 'RSA-PSS':\n            return psAlg(key);\n        case 'RSASSA-PKCS1-v1_5':\n            return rsAlg(key);\n        case 'ECDSA':\n            return esAlg(key);\n        case 'Ed25519':\n        case 'Ed448':\n            return 'EdDSA';\n        default:\n            throw new UnsupportedOperationError('unsupported CryptoKey algorithm name');\n    }\n}\nfunction getClockSkew(client) {\n    const skew = client?.[clockSkew];\n    return typeof skew === 'number' && Number.isFinite(skew) ? skew : 0;\n}\nfunction getClockTolerance(client) {\n    const tolerance = client?.[clockTolerance];\n    return typeof tolerance === 'number' && Number.isFinite(tolerance) && Math.sign(tolerance) !== -1\n        ? tolerance\n        : 30;\n}\nfunction epochTime() {\n    return Math.floor(Date.now() / 1000);\n}\nfunction clientAssertion(as, client) {\n    const now = epochTime() + getClockSkew(client);\n    return {\n        jti: randomBytes(),\n        aud: [as.issuer, as.token_endpoint],\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n        sub: client.client_id,\n    };\n}\nasync function privateKeyJwt(as, client, key, kid) {\n    return jwt({\n        alg: keyToJws(key),\n        kid,\n    }, clientAssertion(as, client), key);\n}\nfunction assertAs(as) {\n    if (typeof as !== 'object' || as === null) {\n        throw new TypeError('\"as\" must be an object');\n    }\n    if (!validateString(as.issuer)) {\n        throw new TypeError('\"as.issuer\" property must be a non-empty string');\n    }\n    return true;\n}\nfunction assertClient(client) {\n    if (typeof client !== 'object' || client === null) {\n        throw new TypeError('\"client\" must be an object');\n    }\n    if (!validateString(client.client_id)) {\n        throw new TypeError('\"client.client_id\" property must be a non-empty string');\n    }\n    return true;\n}\nfunction assertClientSecret(clientSecret) {\n    if (!validateString(clientSecret)) {\n        throw new TypeError('\"client.client_secret\" property must be a non-empty string');\n    }\n    return clientSecret;\n}\nfunction assertNoClientPrivateKey(clientAuthMethod, clientPrivateKey) {\n    if (clientPrivateKey !== undefined) {\n        throw new TypeError(`\"options.clientPrivateKey\" property must not be provided when ${clientAuthMethod} client authentication method is used.`);\n    }\n}\nfunction assertNoClientSecret(clientAuthMethod, clientSecret) {\n    if (clientSecret !== undefined) {\n        throw new TypeError(`\"client.client_secret\" property must not be provided when ${clientAuthMethod} client authentication method is used.`);\n    }\n}\nasync function clientAuthentication(as, client, body, headers, clientPrivateKey) {\n    body.delete('client_secret');\n    body.delete('client_assertion_type');\n    body.delete('client_assertion');\n    switch (client.token_endpoint_auth_method) {\n        case undefined:\n        case 'client_secret_basic': {\n            assertNoClientPrivateKey('client_secret_basic', clientPrivateKey);\n            headers.set('authorization', clientSecretBasic(client.client_id, assertClientSecret(client.client_secret)));\n            break;\n        }\n        case 'client_secret_post': {\n            assertNoClientPrivateKey('client_secret_post', clientPrivateKey);\n            body.set('client_id', client.client_id);\n            body.set('client_secret', assertClientSecret(client.client_secret));\n            break;\n        }\n        case 'private_key_jwt': {\n            assertNoClientSecret('private_key_jwt', client.client_secret);\n            if (clientPrivateKey === undefined) {\n                throw new TypeError('\"options.clientPrivateKey\" must be provided when \"client.token_endpoint_auth_method\" is \"private_key_jwt\"');\n            }\n            const { key, kid } = getKeyAndKid(clientPrivateKey);\n            if (!isPrivateKey(key)) {\n                throw new TypeError('\"options.clientPrivateKey.key\" must be a private CryptoKey');\n            }\n            body.set('client_id', client.client_id);\n            body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n            body.set('client_assertion', await privateKeyJwt(as, client, key, kid));\n            break;\n        }\n        case 'tls_client_auth':\n        case 'self_signed_tls_client_auth':\n        case 'none': {\n            assertNoClientSecret(client.token_endpoint_auth_method, client.client_secret);\n            assertNoClientPrivateKey(client.token_endpoint_auth_method, clientPrivateKey);\n            body.set('client_id', client.client_id);\n            break;\n        }\n        default:\n            throw new UnsupportedOperationError('unsupported client token_endpoint_auth_method');\n    }\n}\nasync function jwt(header, claimsSet, key) {\n    if (!key.usages.includes('sign')) {\n        throw new TypeError('CryptoKey instances used for signing assertions must include \"sign\" in their \"usages\"');\n    }\n    const input = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(claimsSet)))}`;\n    const signature = b64u(await crypto.subtle.sign(keyToSubtle(key), key, buf(input)));\n    return `${input}.${signature}`;\n}\nasync function issueRequestObject(as, client, parameters, privateKey) {\n    assertAs(as);\n    assertClient(client);\n    parameters = new URLSearchParams(parameters);\n    const { key, kid } = getKeyAndKid(privateKey);\n    if (!isPrivateKey(key)) {\n        throw new TypeError('\"privateKey.key\" must be a private CryptoKey');\n    }\n    parameters.set('client_id', client.client_id);\n    const now = epochTime() + getClockSkew(client);\n    const claims = {\n        ...Object.fromEntries(parameters.entries()),\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n    };\n    let resource;\n    if (parameters.has('resource') &&\n        (resource = parameters.getAll('resource')) &&\n        resource.length > 1) {\n        claims.resource = resource;\n    }\n    {\n        let value = parameters.get('max_age');\n        if (value !== null) {\n            claims.max_age = parseInt(value, 10);\n            if (!Number.isFinite(claims.max_age)) {\n                throw new OPE('\"max_age\" parameter must be a number');\n            }\n        }\n    }\n    {\n        let value = parameters.get('claims');\n        if (value !== null) {\n            try {\n                claims.claims = JSON.parse(value);\n            }\n            catch (cause) {\n                throw new OPE('failed to parse the \"claims\" parameter as JSON', { cause });\n            }\n            if (!isJsonObject(claims.claims)) {\n                throw new OPE('\"claims\" parameter must be a JSON with a top level object');\n            }\n        }\n    }\n    {\n        let value = parameters.get('authorization_details');\n        if (value !== null) {\n            try {\n                claims.authorization_details = JSON.parse(value);\n            }\n            catch (cause) {\n                throw new OPE('failed to parse the \"authorization_details\" parameter as JSON', { cause });\n            }\n            if (!Array.isArray(claims.authorization_details)) {\n                throw new OPE('\"authorization_details\" parameter must be a JSON with a top level array');\n            }\n        }\n    }\n    return jwt({\n        alg: keyToJws(key),\n        typ: 'oauth-authz-req+jwt',\n        kid,\n    }, claims, key);\n}\nasync function dpopProofJwt(headers, options, url, htm, clockSkew, accessToken) {\n    const { privateKey, publicKey, nonce = dpopNonces.get(url.origin) } = options;\n    if (!isPrivateKey(privateKey)) {\n        throw new TypeError('\"DPoP.privateKey\" must be a private CryptoKey');\n    }\n    if (!isPublicKey(publicKey)) {\n        throw new TypeError('\"DPoP.publicKey\" must be a public CryptoKey');\n    }\n    if (nonce !== undefined && !validateString(nonce)) {\n        throw new TypeError('\"DPoP.nonce\" must be a non-empty string or undefined');\n    }\n    if (!publicKey.extractable) {\n        throw new TypeError('\"DPoP.publicKey.extractable\" must be true');\n    }\n    const now = epochTime() + clockSkew;\n    const proof = await jwt({\n        alg: keyToJws(privateKey),\n        typ: 'dpop+jwt',\n        jwk: await publicJwk(publicKey),\n    }, {\n        iat: now,\n        jti: randomBytes(),\n        htm,\n        nonce,\n        htu: `${url.origin}${url.pathname}`,\n        ath: accessToken ? b64u(await crypto.subtle.digest('SHA-256', buf(accessToken))) : undefined,\n    }, privateKey);\n    headers.set('dpop', proof);\n}\nlet jwkCache;\nasync function getSetPublicJwkCache(key) {\n    const { kty, e, n, x, y, crv } = await crypto.subtle.exportKey('jwk', key);\n    const jwk = { kty, e, n, x, y, crv };\n    jwkCache.set(key, jwk);\n    return jwk;\n}\nasync function publicJwk(key) {\n    jwkCache || (jwkCache = new WeakMap());\n    return jwkCache.get(key) || getSetPublicJwkCache(key);\n}\nfunction validateEndpoint(value, endpoint, options) {\n    if (typeof value !== 'string') {\n        if (options?.[useMtlsAlias]) {\n            throw new TypeError(`\"as.mtls_endpoint_aliases.${endpoint}\" must be a string`);\n        }\n        throw new TypeError(`\"as.${endpoint}\" must be a string`);\n    }\n    return new URL(value);\n}\nfunction resolveEndpoint(as, endpoint, options) {\n    if (options?.[useMtlsAlias] && as.mtls_endpoint_aliases && endpoint in as.mtls_endpoint_aliases) {\n        return validateEndpoint(as.mtls_endpoint_aliases[endpoint], endpoint, options);\n    }\n    return validateEndpoint(as[endpoint], endpoint);\n}\nasync function pushedAuthorizationRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'pushed_authorization_request_endpoint', options);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        await dpopProofJwt(headers, options.DPoP, url, 'POST', getClockSkew(client));\n    }\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nfunction isOAuth2Error(input) {\n    const value = input;\n    if (typeof value !== 'object' || Array.isArray(value) || value === null) {\n        return false;\n    }\n    return value.error !== undefined;\n}\nfunction unquote(value) {\n    if (value.length >= 2 && value[0] === '\"' && value[value.length - 1] === '\"') {\n        return value.slice(1, -1);\n    }\n    return value;\n}\nconst SPLIT_REGEXP = /((?:,|, )?[0-9a-zA-Z!#$%&'*+-.^_`|~]+=)/;\nconst SCHEMES_REGEXP = /(?:^|, ?)([0-9a-zA-Z!#$%&'*+\\-.^_`|~]+)(?=$|[ ,])/g;\nfunction wwwAuth(scheme, params) {\n    const arr = params.split(SPLIT_REGEXP).slice(1);\n    if (!arr.length) {\n        return { scheme: scheme.toLowerCase(), parameters: {} };\n    }\n    arr[arr.length - 1] = arr[arr.length - 1].replace(/,$/, '');\n    const parameters = {};\n    for (let i = 1; i < arr.length; i += 2) {\n        const idx = i;\n        if (arr[idx][0] === '\"') {\n            while (arr[idx].slice(-1) !== '\"' && ++i < arr.length) {\n                arr[idx] += arr[i];\n            }\n        }\n        const key = arr[idx - 1].replace(/^(?:, ?)|=$/g, '').toLowerCase();\n        parameters[key] = unquote(arr[idx]);\n    }\n    return {\n        scheme: scheme.toLowerCase(),\n        parameters,\n    };\n}\nfunction parseWwwAuthenticateChallenges(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    const header = response.headers.get('www-authenticate');\n    if (header === null) {\n        return undefined;\n    }\n    const result = [];\n    for (const { 1: scheme, index } of header.matchAll(SCHEMES_REGEXP)) {\n        result.push([scheme, index]);\n    }\n    if (!result.length) {\n        return undefined;\n    }\n    const challenges = result.map(([scheme, indexOf], i, others) => {\n        const next = others[i + 1];\n        let parameters;\n        if (next) {\n            parameters = header.slice(indexOf, next[1]);\n        }\n        else {\n            parameters = header.slice(indexOf);\n        }\n        return wwwAuth(scheme, parameters);\n    });\n    return challenges;\n}\nasync function processPushedAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 201) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Pushed Authorization Request Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.request_uri)) {\n        throw new OPE('\"response\" body \"request_uri\" property must be a non-empty string');\n    }\n    if (typeof json.expires_in !== 'number' || json.expires_in <= 0) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    return json;\n}\nasync function protectedResourceRequest(accessToken, method, url, headers, body, options) {\n    if (!validateString(accessToken)) {\n        throw new TypeError('\"accessToken\" must be a non-empty string');\n    }\n    if (!(url instanceof URL)) {\n        throw new TypeError('\"url\" must be an instance of URL');\n    }\n    headers = prepareHeaders(headers);\n    if (options?.DPoP === undefined) {\n        headers.set('authorization', `Bearer ${accessToken}`);\n    }\n    else {\n        await dpopProofJwt(headers, options.DPoP, url, 'GET', getClockSkew({ [clockSkew]: options?.[clockSkew] }), accessToken);\n        headers.set('authorization', `DPoP ${accessToken}`);\n    }\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nasync function userInfoRequest(as, client, accessToken, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'userinfo_endpoint', options);\n    const headers = prepareHeaders(options?.headers);\n    if (client.userinfo_signed_response_alg) {\n        headers.set('accept', 'application/jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n        headers.append('accept', 'application/jwt');\n    }\n    return protectedResourceRequest(accessToken, 'GET', url, headers, null, {\n        ...options,\n        [clockSkew]: getClockSkew(client),\n    });\n}\nlet jwksMap;\nfunction setJwksCache(as, jwks, uat, cache) {\n    jwksMap || (jwksMap = new WeakMap());\n    jwksMap.set(as, {\n        jwks,\n        uat,\n        get age() {\n            return epochTime() - this.uat;\n        },\n    });\n    if (cache) {\n        Object.assign(cache, { jwks: structuredClone(jwks), uat });\n    }\n}\nfunction isFreshJwksCache(input) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || epochTime() - input.uat >= 300) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !isJsonObject(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, isJsonObject)) {\n        return false;\n    }\n    return true;\n}\nfunction clearJwksCache(as, cache) {\n    jwksMap?.delete(as);\n    delete cache?.jwks;\n    delete cache?.uat;\n}\nasync function getPublicSigKeyFromIssuerJwksUri(as, options, header) {\n    const { alg, kid } = header;\n    checkSupportedJwsAlg(alg);\n    if (!jwksMap?.has(as) && isFreshJwksCache(options?.[experimental_jwksCache])) {\n        setJwksCache(as, options?.[experimental_jwksCache].jwks, options?.[experimental_jwksCache].uat);\n    }\n    let jwks;\n    let age;\n    if (jwksMap?.has(as)) {\n        ;\n        ({ jwks, age } = jwksMap.get(as));\n        if (age >= 300) {\n            clearJwksCache(as, options?.[experimental_jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n    }\n    else {\n        jwks = await jwksRequest(as, options).then(processJwksResponse);\n        age = 0;\n        setJwksCache(as, jwks, epochTime(), options?.[experimental_jwksCache]);\n    }\n    let kty;\n    switch (alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            kty = 'RSA';\n            break;\n        case 'ES':\n            kty = 'EC';\n            break;\n        case 'Ed':\n            kty = 'OKP';\n            break;\n        default:\n            throw new UnsupportedOperationError();\n    }\n    const candidates = jwks.keys.filter((jwk) => {\n        if (jwk.kty !== kty) {\n            return false;\n        }\n        if (kid !== undefined && kid !== jwk.kid) {\n            return false;\n        }\n        if (jwk.alg !== undefined && alg !== jwk.alg) {\n            return false;\n        }\n        if (jwk.use !== undefined && jwk.use !== 'sig') {\n            return false;\n        }\n        if (jwk.key_ops?.includes('verify') === false) {\n            return false;\n        }\n        switch (true) {\n            case alg === 'ES256' && jwk.crv !== 'P-256':\n            case alg === 'ES384' && jwk.crv !== 'P-384':\n            case alg === 'ES512' && jwk.crv !== 'P-521':\n            case alg === 'EdDSA' && !(jwk.crv === 'Ed25519' || jwk.crv === 'Ed448'):\n                return false;\n        }\n        return true;\n    });\n    const { 0: jwk, length } = candidates;\n    if (!length) {\n        if (age >= 60) {\n            clearJwksCache(as, options?.[experimental_jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n        throw new OPE('error when selecting a JWT verification key, no applicable keys found');\n    }\n    if (length !== 1) {\n        throw new OPE('error when selecting a JWT verification key, multiple applicable keys found, a \"kid\" JWT Header Parameter is required');\n    }\n    const key = await importJwk(alg, jwk);\n    if (key.type !== 'public') {\n        throw new OPE('jwks_uri must only contain public keys');\n    }\n    return key;\n}\nconst skipSubjectCheck = Symbol();\nfunction getContentType(response) {\n    return response.headers.get('content-type')?.split(';')[0];\n}\nasync function processUserInfoResponse(as, client, expectedSubject, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform UserInfo Endpoint response');\n    }\n    let json;\n    if (getContentType(response) === 'application/jwt') {\n        assertReadableResponse(response);\n        const { claims } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.userinfo_signed_response_alg, as.userinfo_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n            .then(validateOptionalAudience.bind(undefined, client.client_id))\n            .then(validateOptionalIssuer.bind(undefined, as.issuer));\n        json = claims;\n    }\n    else {\n        if (client.userinfo_signed_response_alg) {\n            throw new OPE('JWT UserInfo Response expected');\n        }\n        assertReadableResponse(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw new OPE('failed to parse \"response\" body as JSON', { cause });\n        }\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.sub)) {\n        throw new OPE('\"response\" body \"sub\" property must be a non-empty string');\n    }\n    switch (expectedSubject) {\n        case skipSubjectCheck:\n            break;\n        default:\n            if (!validateString(expectedSubject)) {\n                throw new OPE('\"expectedSubject\" must be a non-empty string');\n            }\n            if (json.sub !== expectedSubject) {\n                throw new OPE('unexpected \"response\" body \"sub\" value');\n            }\n    }\n    return json;\n}\nasync function authenticatedRequest(as, client, method, url, body, headers, options) {\n    await clientAuthentication(as, client, body, headers, options?.clientPrivateKey);\n    headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nasync function tokenEndpointRequest(as, client, grantType, parameters, options) {\n    const url = resolveEndpoint(as, 'token_endpoint', options);\n    parameters.set('grant_type', grantType);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        await dpopProofJwt(headers, options.DPoP, url, 'POST', getClockSkew(client));\n    }\n    return authenticatedRequest(as, client, 'POST', url, parameters, headers, options);\n}\nasync function refreshTokenGrantRequest(as, client, refreshToken, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(refreshToken)) {\n        throw new TypeError('\"refreshToken\" must be a non-empty string');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('refresh_token', refreshToken);\n    return tokenEndpointRequest(as, client, 'refresh_token', parameters, options);\n}\nconst idTokenClaims = new WeakMap();\nfunction getValidatedIdTokenClaims(ref) {\n    if (!ref.id_token) {\n        return undefined;\n    }\n    const claims = idTokenClaims.get(ref);\n    if (!claims) {\n        throw new TypeError('\"ref\" was already garbage collected or did not resolve from the proper sources');\n    }\n    return claims;\n}\nasync function processGenericAccessTokenResponse(as, client, response, ignoreIdToken = false, ignoreRefreshToken = false) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Token Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.access_token)) {\n        throw new OPE('\"response\" body \"access_token\" property must be a non-empty string');\n    }\n    if (!validateString(json.token_type)) {\n        throw new OPE('\"response\" body \"token_type\" property must be a non-empty string');\n    }\n    json.token_type = json.token_type.toLowerCase();\n    if (json.token_type !== 'dpop' && json.token_type !== 'bearer') {\n        throw new UnsupportedOperationError('unsupported `token_type` value');\n    }\n    if (json.expires_in !== undefined &&\n        (typeof json.expires_in !== 'number' || json.expires_in <= 0)) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    if (!ignoreRefreshToken &&\n        json.refresh_token !== undefined &&\n        !validateString(json.refresh_token)) {\n        throw new OPE('\"response\" body \"refresh_token\" property must be a non-empty string');\n    }\n    if (json.scope !== undefined && typeof json.scope !== 'string') {\n        throw new OPE('\"response\" body \"scope\" property must be a string');\n    }\n    if (!ignoreIdToken) {\n        if (json.id_token !== undefined && !validateString(json.id_token)) {\n            throw new OPE('\"response\" body \"id_token\" property must be a non-empty string');\n        }\n        if (json.id_token) {\n            const { claims } = await validateJwt(json.id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n                .then(validatePresence.bind(undefined, ['aud', 'exp', 'iat', 'iss', 'sub']))\n                .then(validateIssuer.bind(undefined, as.issuer))\n                .then(validateAudience.bind(undefined, client.client_id));\n            if (Array.isArray(claims.aud) && claims.aud.length !== 1 && claims.azp !== client.client_id) {\n                throw new OPE('unexpected ID Token \"azp\" (authorized party) claim value');\n            }\n            if (claims.auth_time !== undefined &&\n                (!Number.isFinite(claims.auth_time) || Math.sign(claims.auth_time) !== 1)) {\n                throw new OPE('ID Token \"auth_time\" (authentication time) must be a positive number');\n            }\n            idTokenClaims.set(json, claims);\n        }\n    }\n    return json;\n}\nasync function processRefreshTokenResponse(as, client, response) {\n    return processGenericAccessTokenResponse(as, client, response);\n}\nfunction validateOptionalAudience(expected, result) {\n    if (result.claims.aud !== undefined) {\n        return validateAudience(expected, result);\n    }\n    return result;\n}\nfunction validateAudience(expected, result) {\n    if (Array.isArray(result.claims.aud)) {\n        if (!result.claims.aud.includes(expected)) {\n            throw new OPE('unexpected JWT \"aud\" (audience) claim value');\n        }\n    }\n    else if (result.claims.aud !== expected) {\n        throw new OPE('unexpected JWT \"aud\" (audience) claim value');\n    }\n    return result;\n}\nfunction validateOptionalIssuer(expected, result) {\n    if (result.claims.iss !== undefined) {\n        return validateIssuer(expected, result);\n    }\n    return result;\n}\nfunction validateIssuer(expected, result) {\n    if (result.claims.iss !== expected) {\n        throw new OPE('unexpected JWT \"iss\" (issuer) claim value');\n    }\n    return result;\n}\nconst branded = new WeakSet();\nfunction brand(searchParams) {\n    branded.add(searchParams);\n    return searchParams;\n}\nasync function authorizationCodeGrantRequest(as, client, callbackParameters, redirectUri, codeVerifier, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!branded.has(callbackParameters)) {\n        throw new TypeError('\"callbackParameters\" must be an instance of URLSearchParams obtained from \"validateAuthResponse()\", or \"validateJwtAuthResponse()');\n    }\n    if (!validateString(redirectUri)) {\n        throw new TypeError('\"redirectUri\" must be a non-empty string');\n    }\n    if (!validateString(codeVerifier)) {\n        throw new TypeError('\"codeVerifier\" must be a non-empty string');\n    }\n    const code = getURLSearchParameter(callbackParameters, 'code');\n    if (!code) {\n        throw new OPE('no authorization code in \"callbackParameters\"');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('redirect_uri', redirectUri);\n    parameters.set('code_verifier', codeVerifier);\n    parameters.set('code', code);\n    return tokenEndpointRequest(as, client, 'authorization_code', parameters, options);\n}\nconst jwtClaimNames = {\n    aud: 'audience',\n    c_hash: 'code hash',\n    client_id: 'client id',\n    exp: 'expiration time',\n    iat: 'issued at',\n    iss: 'issuer',\n    jti: 'jwt id',\n    nonce: 'nonce',\n    s_hash: 'state hash',\n    sub: 'subject',\n    ath: 'access token hash',\n    htm: 'http method',\n    htu: 'http uri',\n    cnf: 'confirmation',\n};\nfunction validatePresence(required, result) {\n    for (const claim of required) {\n        if (result.claims[claim] === undefined) {\n            throw new OPE(`JWT \"${claim}\" (${jwtClaimNames[claim]}) claim missing`);\n        }\n    }\n    return result;\n}\nconst expectNoNonce = Symbol();\nconst skipAuthTimeCheck = Symbol();\nasync function processAuthorizationCodeOpenIDResponse(as, client, response, expectedNonce, maxAge) {\n    const result = await processGenericAccessTokenResponse(as, client, response);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (!validateString(result.id_token)) {\n        throw new OPE('\"response\" body \"id_token\" property must be a non-empty string');\n    }\n    maxAge ?? (maxAge = client.default_max_age ?? skipAuthTimeCheck);\n    const claims = getValidatedIdTokenClaims(result);\n    if ((client.require_auth_time || maxAge !== skipAuthTimeCheck) &&\n        claims.auth_time === undefined) {\n        throw new OPE('ID Token \"auth_time\" (authentication time) claim missing');\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        if (typeof maxAge !== 'number' || maxAge < 0) {\n            throw new TypeError('\"maxAge\" must be a non-negative number');\n        }\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw new OPE('too much time has elapsed since the last End-User authentication');\n        }\n    }\n    switch (expectedNonce) {\n        case undefined:\n        case expectNoNonce:\n            if (claims.nonce !== undefined) {\n                throw new OPE('unexpected ID Token \"nonce\" claim value');\n            }\n            break;\n        default:\n            if (!validateString(expectedNonce)) {\n                throw new TypeError('\"expectedNonce\" must be a non-empty string');\n            }\n            if (claims.nonce === undefined) {\n                throw new OPE('ID Token \"nonce\" claim missing');\n            }\n            if (claims.nonce !== expectedNonce) {\n                throw new OPE('unexpected ID Token \"nonce\" claim value');\n            }\n    }\n    return result;\n}\nasync function processAuthorizationCodeOAuth2Response(as, client, response) {\n    const result = await processGenericAccessTokenResponse(as, client, response, true);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (result.id_token !== undefined) {\n        if (typeof result.id_token === 'string' && result.id_token.length) {\n            throw new OPE('Unexpected ID Token returned, use processAuthorizationCodeOpenIDResponse() for OpenID Connect callback processing');\n        }\n        delete result.id_token;\n    }\n    return result;\n}\nfunction checkJwtType(expected, result) {\n    if (typeof result.header.typ !== 'string' || normalizeTyp(result.header.typ) !== expected) {\n        throw new OPE('unexpected JWT \"typ\" header parameter value');\n    }\n    return result;\n}\nasync function clientCredentialsGrantRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    return tokenEndpointRequest(as, client, 'client_credentials', new URLSearchParams(parameters), options);\n}\nasync function processClientCredentialsResponse(as, client, response) {\n    const result = await processGenericAccessTokenResponse(as, client, response, true, true);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    return result;\n}\nasync function revocationRequest(as, client, token, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(token)) {\n        throw new TypeError('\"token\" must be a non-empty string');\n    }\n    const url = resolveEndpoint(as, 'revocation_endpoint', options);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    headers.delete('accept');\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nasync function processRevocationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Revocation Endpoint response');\n    }\n    return undefined;\n}\nfunction assertReadableResponse(response) {\n    if (response.bodyUsed) {\n        throw new TypeError('\"response\" body has been used already');\n    }\n}\nasync function introspectionRequest(as, client, token, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(token)) {\n        throw new TypeError('\"token\" must be a non-empty string');\n    }\n    const url = resolveEndpoint(as, 'introspection_endpoint', options);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    if (options?.requestJwtResponse ?? client.introspection_signed_response_alg) {\n        headers.set('accept', 'application/token-introspection+jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n    }\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nasync function processIntrospectionResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Introspection Endpoint response');\n    }\n    let json;\n    if (getContentType(response) === 'application/token-introspection+jwt') {\n        assertReadableResponse(response);\n        const { claims } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.introspection_signed_response_alg, as.introspection_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n            .then(checkJwtType.bind(undefined, 'token-introspection+jwt'))\n            .then(validatePresence.bind(undefined, ['aud', 'iat', 'iss']))\n            .then(validateIssuer.bind(undefined, as.issuer))\n            .then(validateAudience.bind(undefined, client.client_id));\n        json = claims.token_introspection;\n        if (!isJsonObject(json)) {\n            throw new OPE('JWT \"token_introspection\" claim must be a JSON object');\n        }\n    }\n    else {\n        assertReadableResponse(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw new OPE('failed to parse \"response\" body as JSON', { cause });\n        }\n        if (!isJsonObject(json)) {\n            throw new OPE('\"response\" body must be a top level object');\n        }\n    }\n    if (typeof json.active !== 'boolean') {\n        throw new OPE('\"response\" body \"active\" property must be a boolean');\n    }\n    return json;\n}\nasync function jwksRequest(as, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'jwks_uri');\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.append('accept', 'application/jwk-set+json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nasync function processJwksResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform JSON Web Key Set response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!Array.isArray(json.keys)) {\n        throw new OPE('\"response\" body \"keys\" property must be an array');\n    }\n    if (!Array.prototype.every.call(json.keys, isJsonObject)) {\n        throw new OPE('\"response\" body \"keys\" property members must be JWK formatted objects');\n    }\n    return json;\n}\nasync function handleOAuthBodyError(response) {\n    if (response.status > 399 && response.status < 500) {\n        assertReadableResponse(response);\n        try {\n            const json = await response.json();\n            if (isJsonObject(json) && typeof json.error === 'string' && json.error.length) {\n                if (json.error_description !== undefined && typeof json.error_description !== 'string') {\n                    delete json.error_description;\n                }\n                if (json.error_uri !== undefined && typeof json.error_uri !== 'string') {\n                    delete json.error_uri;\n                }\n                if (json.algs !== undefined && typeof json.algs !== 'string') {\n                    delete json.algs;\n                }\n                if (json.scope !== undefined && typeof json.scope !== 'string') {\n                    delete json.scope;\n                }\n                return json;\n            }\n        }\n        catch { }\n    }\n    return undefined;\n}\nfunction checkSupportedJwsAlg(alg) {\n    if (!SUPPORTED_JWS_ALGS.includes(alg)) {\n        throw new UnsupportedOperationError('unsupported JWS \"alg\" identifier');\n    }\n    return alg;\n}\nfunction checkRsaKeyAlgorithm(algorithm) {\n    if (typeof algorithm.modulusLength !== 'number' || algorithm.modulusLength < 2048) {\n        throw new OPE(`${algorithm.name} modulusLength must be at least 2048 bits`);\n    }\n}\nfunction ecdsaHashName(namedCurve) {\n    switch (namedCurve) {\n        case 'P-256':\n            return 'SHA-256';\n        case 'P-384':\n            return 'SHA-384';\n        case 'P-521':\n            return 'SHA-512';\n        default:\n            throw new UnsupportedOperationError();\n    }\n}\nfunction keyToSubtle(key) {\n    switch (key.algorithm.name) {\n        case 'ECDSA':\n            return {\n                name: key.algorithm.name,\n                hash: ecdsaHashName(key.algorithm.namedCurve),\n            };\n        case 'RSA-PSS': {\n            checkRsaKeyAlgorithm(key.algorithm);\n            switch (key.algorithm.hash.name) {\n                case 'SHA-256':\n                case 'SHA-384':\n                case 'SHA-512':\n                    return {\n                        name: key.algorithm.name,\n                        saltLength: parseInt(key.algorithm.hash.name.slice(-3), 10) >> 3,\n                    };\n                default:\n                    throw new UnsupportedOperationError();\n            }\n        }\n        case 'RSASSA-PKCS1-v1_5':\n            checkRsaKeyAlgorithm(key.algorithm);\n            return key.algorithm.name;\n        case 'Ed448':\n        case 'Ed25519':\n            return key.algorithm.name;\n    }\n    throw new UnsupportedOperationError();\n}\nconst noSignatureCheck = Symbol();\nasync function validateJwt(jws, checkAlg, getKey, clockSkew, clockTolerance) {\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature, length } = jws.split('.');\n    if (length === 5) {\n        throw new UnsupportedOperationError('JWE structure JWTs are not supported');\n    }\n    if (length !== 3) {\n        throw new OPE('Invalid JWT');\n    }\n    let header;\n    try {\n        header = JSON.parse(buf(b64u(protectedHeader)));\n    }\n    catch (cause) {\n        throw new OPE('failed to parse JWT Header body as base64url encoded JSON', { cause });\n    }\n    if (!isJsonObject(header)) {\n        throw new OPE('JWT Header must be a top level object');\n    }\n    checkAlg(header);\n    if (header.crit !== undefined) {\n        throw new OPE('unexpected JWT \"crit\" header parameter');\n    }\n    const signature = b64u(encodedSignature);\n    let key;\n    if (getKey !== noSignatureCheck) {\n        key = await getKey(header);\n        const input = `${protectedHeader}.${payload}`;\n        const verified = await crypto.subtle.verify(keyToSubtle(key), key, signature, buf(input));\n        if (!verified) {\n            throw new OPE('JWT signature verification failed');\n        }\n    }\n    let claims;\n    try {\n        claims = JSON.parse(buf(b64u(payload)));\n    }\n    catch (cause) {\n        throw new OPE('failed to parse JWT Payload body as base64url encoded JSON', { cause });\n    }\n    if (!isJsonObject(claims)) {\n        throw new OPE('JWT Payload must be a top level object');\n    }\n    const now = epochTime() + clockSkew;\n    if (claims.exp !== undefined) {\n        if (typeof claims.exp !== 'number') {\n            throw new OPE('unexpected JWT \"exp\" (expiration time) claim type');\n        }\n        if (claims.exp <= now - clockTolerance) {\n            throw new OPE('unexpected JWT \"exp\" (expiration time) claim value, timestamp is <= now()');\n        }\n    }\n    if (claims.iat !== undefined) {\n        if (typeof claims.iat !== 'number') {\n            throw new OPE('unexpected JWT \"iat\" (issued at) claim type');\n        }\n    }\n    if (claims.iss !== undefined) {\n        if (typeof claims.iss !== 'string') {\n            throw new OPE('unexpected JWT \"iss\" (issuer) claim type');\n        }\n    }\n    if (claims.nbf !== undefined) {\n        if (typeof claims.nbf !== 'number') {\n            throw new OPE('unexpected JWT \"nbf\" (not before) claim type');\n        }\n        if (claims.nbf > now + clockTolerance) {\n            throw new OPE('unexpected JWT \"nbf\" (not before) claim value, timestamp is > now()');\n        }\n    }\n    if (claims.aud !== undefined) {\n        if (typeof claims.aud !== 'string' && !Array.isArray(claims.aud)) {\n            throw new OPE('unexpected JWT \"aud\" (audience) claim type');\n        }\n    }\n    return { header, claims, signature, key };\n}\nasync function validateJwtAuthResponse(as, client, parameters, expectedState, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams, or URL');\n    }\n    const response = getURLSearchParameter(parameters, 'response');\n    if (!response) {\n        throw new OPE('\"parameters\" does not contain a JARM response');\n    }\n    if (typeof as.jwks_uri !== 'string') {\n        throw new TypeError('\"as.jwks_uri\" must be a string');\n    }\n    const { claims } = await validateJwt(response, checkSigningAlgorithm.bind(undefined, client.authorization_signed_response_alg, as.authorization_signing_alg_values_supported), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(client), getClockTolerance(client))\n        .then(validatePresence.bind(undefined, ['aud', 'exp', 'iss']))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const result = new URLSearchParams();\n    for (const [key, value] of Object.entries(claims)) {\n        if (typeof value === 'string' && key !== 'aud') {\n            result.set(key, value);\n        }\n    }\n    return validateAuthResponse(as, client, result, expectedState);\n}\nasync function idTokenHash(alg, data, key) {\n    let algorithm;\n    switch (alg) {\n        case 'RS256':\n        case 'PS256':\n        case 'ES256':\n            algorithm = 'SHA-256';\n            break;\n        case 'RS384':\n        case 'PS384':\n        case 'ES384':\n            algorithm = 'SHA-384';\n            break;\n        case 'RS512':\n        case 'PS512':\n        case 'ES512':\n            algorithm = 'SHA-512';\n            break;\n        case 'EdDSA':\n            if (key.algorithm.name === 'Ed25519') {\n                algorithm = 'SHA-512';\n                break;\n            }\n            throw new UnsupportedOperationError();\n        default:\n            throw new UnsupportedOperationError();\n    }\n    const digest = await crypto.subtle.digest(algorithm, buf(data));\n    return b64u(digest.slice(0, digest.byteLength / 2));\n}\nasync function idTokenHashMatches(data, actual, alg, key) {\n    const expected = await idTokenHash(alg, data, key);\n    return actual === expected;\n}\nasync function validateDetachedSignatureResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        if (!parameters.hash.length) {\n            throw new TypeError('\"parameters\" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters');\n        }\n        parameters = new URLSearchParams(parameters.hash.slice(1));\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams');\n    }\n    parameters = new URLSearchParams(parameters);\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    parameters.delete('id_token');\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            break;\n        default:\n            if (!validateString(expectedState)) {\n                throw new TypeError('\"expectedState\" must be a non-empty string');\n            }\n    }\n    const result = validateAuthResponse({\n        ...as,\n        authorization_response_iss_parameter_supported: false,\n    }, client, parameters, expectedState);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (!id_token) {\n        throw new OPE('\"parameters\" does not contain an ID Token');\n    }\n    const code = getURLSearchParameter(parameters, 'code');\n    if (!code) {\n        throw new OPE('\"parameters\" does not contain an Authorization Code');\n    }\n    if (typeof as.jwks_uri !== 'string') {\n        throw new TypeError('\"as.jwks_uri\" must be a string');\n    }\n    const requiredClaims = [\n        'aud',\n        'exp',\n        'iat',\n        'iss',\n        'sub',\n        'nonce',\n        'c_hash',\n    ];\n    if (typeof expectedState === 'string') {\n        requiredClaims.push('s_hash');\n    }\n    const { claims, header, key } = await validateJwt(id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(client), getClockTolerance(client))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const clockSkew = getClockSkew(client);\n    const now = epochTime() + clockSkew;\n    if (claims.iat < now - 3600) {\n        throw new OPE('unexpected JWT \"iat\" (issued at) claim value, it is too far in the past');\n    }\n    if (typeof claims.c_hash !== 'string' ||\n        (await idTokenHashMatches(code, claims.c_hash, header.alg, key)) !== true) {\n        throw new OPE('invalid ID Token \"c_hash\" (code hash) claim value');\n    }\n    if (claims.s_hash !== undefined && typeof expectedState !== 'string') {\n        throw new OPE('could not verify ID Token \"s_hash\" (state hash) claim value');\n    }\n    if (typeof expectedState === 'string' &&\n        (typeof claims.s_hash !== 'string' ||\n            (await idTokenHashMatches(expectedState, claims.s_hash, header.alg, key)) !== true)) {\n        throw new OPE('invalid ID Token \"s_hash\" (state hash) claim value');\n    }\n    if (claims.auth_time !== undefined &&\n        (!Number.isFinite(claims.auth_time) || Math.sign(claims.auth_time) !== 1)) {\n        throw new OPE('ID Token \"auth_time\" (authentication time) must be a positive number');\n    }\n    maxAge ?? (maxAge = client.default_max_age ?? skipAuthTimeCheck);\n    if ((client.require_auth_time || maxAge !== skipAuthTimeCheck) &&\n        claims.auth_time === undefined) {\n        throw new OPE('ID Token \"auth_time\" (authentication time) claim missing');\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        if (typeof maxAge !== 'number' || maxAge < 0) {\n            throw new TypeError('\"maxAge\" must be a non-negative number');\n        }\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw new OPE('too much time has elapsed since the last End-User authentication');\n        }\n    }\n    if (!validateString(expectedNonce)) {\n        throw new TypeError('\"expectedNonce\" must be a non-empty string');\n    }\n    if (claims.nonce !== expectedNonce) {\n        throw new OPE('unexpected ID Token \"nonce\" claim value');\n    }\n    if (Array.isArray(claims.aud) && claims.aud.length !== 1 && claims.azp !== client.client_id) {\n        throw new OPE('unexpected ID Token \"azp\" (authorized party) claim value');\n    }\n    return result;\n}\nfunction checkSigningAlgorithm(client, issuer, header) {\n    if (client !== undefined) {\n        if (header.alg !== client) {\n            throw new OPE('unexpected JWT \"alg\" header parameter');\n        }\n        return;\n    }\n    if (Array.isArray(issuer)) {\n        if (!issuer.includes(header.alg)) {\n            throw new OPE('unexpected JWT \"alg\" header parameter');\n        }\n        return;\n    }\n    if (header.alg !== 'RS256') {\n        throw new OPE('unexpected JWT \"alg\" header parameter');\n    }\n}\nfunction getURLSearchParameter(parameters, name) {\n    const { 0: value, length } = parameters.getAll(name);\n    if (length > 1) {\n        throw new OPE(`\"${name}\" parameter must be provided only once`);\n    }\n    return value;\n}\nconst skipStateCheck = Symbol();\nconst expectNoState = Symbol();\nfunction validateAuthResponse(as, client, parameters, expectedState) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams, or URL');\n    }\n    if (getURLSearchParameter(parameters, 'response')) {\n        throw new OPE('\"parameters\" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()');\n    }\n    const iss = getURLSearchParameter(parameters, 'iss');\n    const state = getURLSearchParameter(parameters, 'state');\n    if (!iss && as.authorization_response_iss_parameter_supported) {\n        throw new OPE('response parameter \"iss\" (issuer) missing');\n    }\n    if (iss && iss !== as.issuer) {\n        throw new OPE('unexpected \"iss\" (issuer) response parameter value');\n    }\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            if (state !== undefined) {\n                throw new OPE('unexpected \"state\" response parameter encountered');\n            }\n            break;\n        case skipStateCheck:\n            break;\n        default:\n            if (!validateString(expectedState)) {\n                throw new OPE('\"expectedState\" must be a non-empty string');\n            }\n            if (state === undefined) {\n                throw new OPE('response parameter \"state\" missing');\n            }\n            if (state !== expectedState) {\n                throw new OPE('unexpected \"state\" response parameter value');\n            }\n    }\n    const error = getURLSearchParameter(parameters, 'error');\n    if (error) {\n        return {\n            error,\n            error_description: getURLSearchParameter(parameters, 'error_description'),\n            error_uri: getURLSearchParameter(parameters, 'error_uri'),\n        };\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    const token = getURLSearchParameter(parameters, 'token');\n    if (id_token !== undefined || token !== undefined) {\n        throw new UnsupportedOperationError('implicit and hybrid flows are not supported');\n    }\n    return brand(new URLSearchParams(parameters));\n}\nfunction algToSubtle(alg, crv) {\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n        case 'ES256':\n        case 'ES384':\n            return { name: 'ECDSA', namedCurve: `P-${alg.slice(-3)}` };\n        case 'ES512':\n            return { name: 'ECDSA', namedCurve: 'P-521' };\n        case 'EdDSA': {\n            switch (crv) {\n                case 'Ed25519':\n                case 'Ed448':\n                    return crv;\n                default:\n                    throw new UnsupportedOperationError();\n            }\n        }\n        default:\n            throw new UnsupportedOperationError();\n    }\n}\nasync function importJwk(alg, jwk) {\n    const { ext, key_ops, use, ...key } = jwk;\n    return crypto.subtle.importKey('jwk', key, algToSubtle(alg, jwk.crv), true, ['verify']);\n}\nasync function deviceAuthorizationRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'device_authorization_endpoint', options);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nasync function processDeviceAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Device Authorization Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.device_code)) {\n        throw new OPE('\"response\" body \"device_code\" property must be a non-empty string');\n    }\n    if (!validateString(json.user_code)) {\n        throw new OPE('\"response\" body \"user_code\" property must be a non-empty string');\n    }\n    if (!validateString(json.verification_uri)) {\n        throw new OPE('\"response\" body \"verification_uri\" property must be a non-empty string');\n    }\n    if (typeof json.expires_in !== 'number' || json.expires_in <= 0) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    if (json.verification_uri_complete !== undefined &&\n        !validateString(json.verification_uri_complete)) {\n        throw new OPE('\"response\" body \"verification_uri_complete\" property must be a non-empty string');\n    }\n    if (json.interval !== undefined && (typeof json.interval !== 'number' || json.interval <= 0)) {\n        throw new OPE('\"response\" body \"interval\" property must be a positive number');\n    }\n    return json;\n}\nasync function deviceCodeGrantRequest(as, client, deviceCode, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(deviceCode)) {\n        throw new TypeError('\"deviceCode\" must be a non-empty string');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('device_code', deviceCode);\n    return tokenEndpointRequest(as, client, 'urn:ietf:params:oauth:grant-type:device_code', parameters, options);\n}\nasync function processDeviceCodeResponse(as, client, response) {\n    return processGenericAccessTokenResponse(as, client, response);\n}\nasync function generateKeyPair(alg, options) {\n    if (!validateString(alg)) {\n        throw new TypeError('\"alg\" must be a non-empty string');\n    }\n    const algorithm = algToSubtle(alg, alg === 'EdDSA' ? options?.crv ?? 'Ed25519' : undefined);\n    if (alg.startsWith('PS') || alg.startsWith('RS')) {\n        Object.assign(algorithm, {\n            modulusLength: options?.modulusLength ?? 2048,\n            publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n        });\n    }\n    return (crypto.subtle.generateKey(algorithm, options?.extractable ?? false, ['sign', 'verify']));\n}\nfunction normalizeHtu(htu) {\n    const url = new URL(htu);\n    url.search = '';\n    url.hash = '';\n    return url.href;\n}\nasync function validateDPoP(as, request, accessToken, accessTokenClaims, options) {\n    const header = request.headers.get('dpop');\n    if (header === null) {\n        throw new OPE('operation indicated DPoP use but the request has no DPoP HTTP Header');\n    }\n    if (request.headers.get('authorization')?.toLowerCase().startsWith('dpop ') === false) {\n        throw new OPE(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`);\n    }\n    if (typeof accessTokenClaims.cnf?.jkt !== 'string') {\n        throw new OPE('operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim');\n    }\n    const clockSkew = getClockSkew(options);\n    const proof = await validateJwt(header, checkSigningAlgorithm.bind(undefined, undefined, as?.dpop_signing_alg_values_supported || SUPPORTED_JWS_ALGS), async ({ jwk, alg }) => {\n        if (!jwk) {\n            throw new OPE('DPoP Proof is missing the jwk header parameter');\n        }\n        const key = await importJwk(alg, jwk);\n        if (key.type !== 'public') {\n            throw new OPE('DPoP Proof jwk header parameter must contain a public key');\n        }\n        return key;\n    }, clockSkew, getClockTolerance(options))\n        .then(checkJwtType.bind(undefined, 'dpop+jwt'))\n        .then(validatePresence.bind(undefined, ['iat', 'jti', 'ath', 'htm', 'htu']));\n    const now = epochTime() + clockSkew;\n    const diff = Math.abs(now - proof.claims.iat);\n    if (diff > 300) {\n        throw new OPE('DPoP Proof iat is not recent enough');\n    }\n    if (proof.claims.htm !== request.method) {\n        throw new OPE('DPoP Proof htm mismatch');\n    }\n    if (typeof proof.claims.htu !== 'string' ||\n        normalizeHtu(proof.claims.htu) !== normalizeHtu(request.url)) {\n        throw new OPE('DPoP Proof htu mismatch');\n    }\n    {\n        const expected = b64u(await crypto.subtle.digest('SHA-256', encoder.encode(accessToken)));\n        if (proof.claims.ath !== expected) {\n            throw new OPE('DPoP Proof ath mismatch');\n        }\n    }\n    {\n        let components;\n        switch (proof.header.jwk.kty) {\n            case 'EC':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                    y: proof.header.jwk.y,\n                };\n                break;\n            case 'OKP':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                };\n                break;\n            case 'RSA':\n                components = {\n                    e: proof.header.jwk.e,\n                    kty: proof.header.jwk.kty,\n                    n: proof.header.jwk.n,\n                };\n                break;\n            default:\n                throw new UnsupportedOperationError();\n        }\n        const expected = b64u(await crypto.subtle.digest('SHA-256', encoder.encode(JSON.stringify(components))));\n        if (accessTokenClaims.cnf.jkt !== expected) {\n            throw new OPE('JWT Access Token confirmation mismatch');\n        }\n    }\n}\nasync function validateJwtAccessToken(as, request, expectedAudience, options) {\n    assertAs(as);\n    if (!looseInstanceOf(request, Request)) {\n        throw new TypeError('\"request\" must be an instance of Request');\n    }\n    if (!validateString(expectedAudience)) {\n        throw new OPE('\"expectedAudience\" must be a non-empty string');\n    }\n    const authorization = request.headers.get('authorization');\n    if (authorization === null) {\n        throw new OPE('\"request\" is missing an Authorization HTTP Header');\n    }\n    let { 0: scheme, 1: accessToken, length } = authorization.split(' ');\n    scheme = scheme.toLowerCase();\n    switch (scheme) {\n        case 'dpop':\n        case 'bearer':\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported Authorization HTTP Header scheme');\n    }\n    if (length !== 2) {\n        throw new OPE('invalid Authorization HTTP Header format');\n    }\n    const requiredClaims = [\n        'iss',\n        'exp',\n        'aud',\n        'sub',\n        'iat',\n        'jti',\n        'client_id',\n    ];\n    if (options?.requireDPoP || scheme === 'dpop' || request.headers.has('dpop')) {\n        requiredClaims.push('cnf');\n    }\n    const { claims } = await validateJwt(accessToken, checkSigningAlgorithm.bind(undefined, undefined, SUPPORTED_JWS_ALGS), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(options), getClockTolerance(options))\n        .then(checkJwtType.bind(undefined, 'at+jwt'))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, expectedAudience));\n    for (const claim of ['client_id', 'jti', 'sub']) {\n        if (typeof claims[claim] !== 'string') {\n            throw new OPE(`unexpected JWT \"${claim}\" claim type`);\n        }\n    }\n    if ('cnf' in claims) {\n        if (!isJsonObject(claims.cnf)) {\n            throw new OPE('unexpected JWT \"cnf\" (confirmation) claim value');\n        }\n        const { 0: cnf, length } = Object.keys(claims.cnf);\n        if (length) {\n            if (length !== 1) {\n                throw new UnsupportedOperationError('multiple confirmation claims are not supported');\n            }\n            if (cnf !== 'jkt') {\n                throw new UnsupportedOperationError('unsupported JWT Confirmation method');\n            }\n        }\n    }\n    if (options?.requireDPoP ||\n        scheme === 'dpop' ||\n        claims.cnf?.jkt !== undefined ||\n        request.headers.has('dpop')) {\n        await validateDPoP(as, request, accessToken, claims, options);\n    }\n    return claims;\n}\nconst experimentalCustomFetch = customFetch;\nconst experimental_customFetch = customFetch;\nconst experimentalUseMtlsAlias = useMtlsAlias;\nconst experimental_useMtlsAlias = useMtlsAlias;\nconst experimental_validateDetachedSignatureResponse = validateDetachedSignatureResponse;\nconst experimental_validateJwtAccessToken = validateJwtAccessToken;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth4webapi/build/index.js\n");

/***/ })

};
;