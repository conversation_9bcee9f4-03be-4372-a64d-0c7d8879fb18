"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Canvas refs for preview generation\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fabricCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers\n                const editableLayers = JSON.parse(templateData.editableLayers);\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleTextChange = (layerId, value)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        // Create object URL for immediate preview\n        const imageUrl = URL.createObjectURL(file);\n        // Update customizations immediately\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: imageUrl\n            }));\n    // In a real implementation, you'd upload to your storage service here\n    // const uploadedUrl = await uploadToStorage(file);\n    // setCustomizations(prev => ({ ...prev, [layerId]: uploadedUrl }));\n    };\n    // Initialize canvas when template is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!template || !canvasRef.current) return;\n        // Clean up existing canvas safely\n        if (fabricCanvasRef.current) {\n            try {\n                fabricCanvasRef.current.dispose();\n            } catch (error) {\n                console.warn(\"Error disposing canvas:\", error);\n            }\n            fabricCanvasRef.current = null;\n        }\n        // Initialize canvas with proper error handling\n        const initCanvas = async ()=>{\n            if (!canvasRef.current || !template) return;\n            try {\n                // Ensure canvas element is properly mounted\n                const canvasElement = canvasRef.current;\n                if (!canvasElement.getContext) {\n                    console.warn(\"Canvas element not ready\");\n                    return;\n                }\n                // Test canvas context availability\n                const testContext = canvasElement.getContext(\"2d\");\n                if (!testContext) {\n                    console.warn(\"Canvas 2D context not available\");\n                    return;\n                }\n                // Create new fabric canvas with proper sizing\n                const canvas = new fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Canvas(canvasElement, {\n                    width: template.width,\n                    height: template.height,\n                    selection: false,\n                    preserveObjectStacking: true\n                });\n                // Verify canvas was created successfully\n                if (!canvas.getContext() || !canvas.getElement()) {\n                    console.warn(\"Fabric canvas not properly initialized\");\n                    canvas.dispose();\n                    return;\n                }\n                fabricCanvasRef.current = canvas;\n                // Load template JSON with error handling\n                try {\n                    const templateJson = JSON.parse(template.json);\n                    // Use a promise-based approach for loadFromJSON\n                    await new Promise((resolve, reject)=>{\n                        canvas.loadFromJSON(templateJson, ()=>{\n                            try {\n                                if (!fabricCanvasRef.current) {\n                                    reject(new Error(\"Canvas disposed during loading\"));\n                                    return;\n                                }\n                                // Ensure canvas is properly sized and rendered\n                                fabricCanvasRef.current.setDimensions({\n                                    width: template.width,\n                                    height: template.height\n                                });\n                                fabricCanvasRef.current.renderAll();\n                                generatePreviewFromCanvas();\n                                resolve();\n                            } catch (error) {\n                                reject(error);\n                            }\n                        }, (error)=>{\n                            reject(error);\n                        });\n                    });\n                } catch (jsonError) {\n                    console.error(\"Failed to load template JSON:\", jsonError);\n                    // Fallback to template thumbnail\n                    if (template.thumbnailUrl) {\n                        setPreviewUrl(template.thumbnailUrl);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize canvas:\", error);\n                // Fallback to template thumbnail\n                if (template.thumbnailUrl) {\n                    setPreviewUrl(template.thumbnailUrl);\n                }\n            }\n        };\n        // Use multiple timeouts to ensure proper initialization\n        const timeoutId = setTimeout(()=>{\n            initCanvas();\n        }, 200);\n        return ()=>{\n            clearTimeout(timeoutId);\n            if (fabricCanvasRef.current) {\n                try {\n                    fabricCanvasRef.current.dispose();\n                } catch (error) {\n                    console.warn(\"Error disposing canvas in cleanup:\", error);\n                }\n                fabricCanvasRef.current = null;\n            }\n        };\n    }, [\n        template\n    ]);\n    // Generate preview from canvas\n    const generatePreviewFromCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!fabricCanvasRef.current) return;\n        try {\n            // Check if canvas is properly initialized\n            const canvas = fabricCanvasRef.current;\n            if (!canvas.getContext || !canvas.getElement()) {\n                console.warn(\"Canvas not properly initialized\");\n                return;\n            }\n            const dataURL = canvas.toDataURL({\n                format: \"png\",\n                quality: 0.8,\n                multiplier: 1\n            });\n            setPreviewUrl(dataURL);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n            // Fallback to template thumbnail if preview generation fails\n            if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n        }\n    }, [\n        template\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!fabricCanvasRef.current || !template) return;\n        const canvas = fabricCanvasRef.current;\n        // Check if canvas is properly initialized\n        if (!canvas.getContext || !canvas.getElement()) {\n            console.warn(\"Canvas not ready for customizations\");\n            return;\n        }\n        try {\n            // Apply customizations\n            Object.entries(customizations).forEach((param)=>{\n                let [layerId, value] = param;\n                const object = canvas.getObjects().find((obj)=>obj.id === layerId);\n                if (object) {\n                    if (object.type === \"textbox\") {\n                        // Apply text customization\n                        object.set(\"text\", value);\n                    } else if (object.type === \"image\" || object.type === \"rect\" || object.type === \"circle\") {\n                        // Apply image customization\n                        if (value && value.startsWith(\"blob:\")) {\n                            fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Image.fromURL(value, (img)=>{\n                                if (!fabricCanvasRef.current) return; // Check if canvas still exists\n                                try {\n                                    // Scale image to fit the object bounds\n                                    const scaleX = object.width / img.width;\n                                    const scaleY = object.height / img.height;\n                                    const scale = Math.min(scaleX, scaleY);\n                                    img.set({\n                                        left: object.left,\n                                        top: object.top,\n                                        scaleX: scale,\n                                        scaleY: scale,\n                                        originX: object.originX,\n                                        originY: object.originY\n                                    });\n                                    // Replace the object with the new image\n                                    fabricCanvasRef.current.remove(object);\n                                    fabricCanvasRef.current.add(img);\n                                    fabricCanvasRef.current.renderAll();\n                                    generatePreviewFromCanvas();\n                                } catch (error) {\n                                    console.error(\"Error applying image customization:\", error);\n                                }\n                            });\n                            return; // Skip the renderAll below since it's handled in the callback\n                        }\n                    }\n                }\n            });\n            canvas.renderAll();\n            generatePreviewFromCanvas();\n        } catch (error) {\n            console.error(\"Error applying customizations:\", error);\n        }\n    }, [\n        customizations,\n        template,\n        generatePreviewFromCanvas\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Generate preview from current canvas state\n            generatePreviewFromCanvas();\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // In a real implementation, you'd get a download URL from the API\n            const link = document.createElement(\"a\");\n            link.href = (template === null || template === void 0 ? void 0 : template.thumbnailUrl) || \"\";\n            link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n            link.click();\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 340,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Customize Elements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 432,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: customizations[layer.id] || \"\",\n                                                                onChange: (e)=>handleTextChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 480,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 486,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 487,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 504,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center bg-gray-100 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-lg overflow-hidden flex items-center justify-center\",\n                                                style: {\n                                                    aspectRatio: \"\".concat(template.width, \"/\").concat(template.height),\n                                                    maxWidth: \"100%\",\n                                                    maxHeight: \"500px\",\n                                                    width: \"auto\",\n                                                    height: \"auto\"\n                                                },\n                                                children: previewUrl || template.thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: previewUrl || template.thumbnailUrl || \"\",\n                                                    alt: \"Template preview\",\n                                                    className: \"max-w-full max-h-full object-contain\",\n                                                    style: {\n                                                        width: \"auto\",\n                                                        height: \"auto\",\n                                                        maxWidth: \"100%\",\n                                                        maxHeight: \"100%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center bg-gray-50 w-full h-full min-h-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    display: \"none\"\n                },\n                width: (template === null || template === void 0 ? void 0 : template.width) || 800,\n                height: (template === null || template === void 0 ? void 0 : template.height) || 600\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 561,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 365,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"QQPCiuc/A/FRTC+Fc3uRGNCk9XI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcHVibGljL1twcm9qZWN0SWRdL2N1c3RvbWl6ZS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWlFO0FBQ1Y7QUFDdUM7QUFDOUQ7QUFFZ0I7QUFDRjtBQUNBO0FBQ2tDO0FBQ2xDO0FBSS9CLFNBQVNxQjs7SUFDdEIsTUFBTUMsU0FBU2xCLDBEQUFTQTtJQUN4QixNQUFNbUIsU0FBU2xCLDBEQUFTQTtJQUN4QixNQUFNLENBQUNtQixVQUFVQyxZQUFZLEdBQUd4QiwrQ0FBUUEsQ0FBMEI7SUFDbEUsTUFBTSxDQUFDeUIsU0FBU0MsV0FBVyxHQUFHMUIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDMkIsT0FBT0MsU0FBUyxHQUFHNUIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQzZCLGdCQUFnQkMsa0JBQWtCLEdBQUc5QiwrQ0FBUUEsQ0FBeUIsQ0FBQztJQUM5RSxNQUFNLENBQUMrQixZQUFZQyxjQUFjLEdBQUdoQywrQ0FBUUEsQ0FBZ0I7SUFDNUQsTUFBTSxDQUFDaUMscUJBQXFCQyx1QkFBdUIsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ21DLGVBQWVDLGlCQUFpQixHQUFHcEMsK0NBQVFBLENBQUM7SUFFbkQscUNBQXFDO0lBQ3JDLE1BQU1xQyxZQUFZcEMsNkNBQU1BLENBQW9CO0lBQzVDLE1BQU1xQyxrQkFBa0JyQyw2Q0FBTUEsQ0FBdUI7SUFFckRGLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXdDLGdCQUFnQjtZQUNwQixJQUFJO2dCQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSx3QkFBeUMsT0FBakJwQixPQUFPcUIsU0FBUztnQkFFckUsSUFBSSxDQUFDRixTQUFTRyxFQUFFLEVBQUU7b0JBQ2hCLElBQUlILFNBQVNJLE1BQU0sS0FBSyxLQUFLO3dCQUMzQmhCLFNBQVM7b0JBQ1gsT0FBTzt3QkFDTEEsU0FBUztvQkFDWDtvQkFDQTtnQkFDRjtnQkFFQSxNQUFNaUIsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO2dCQUNoQyxNQUFNQyxlQUFlRixLQUFLQSxJQUFJO2dCQUU5QixvQ0FBb0M7Z0JBQ3BDLElBQUksQ0FBQ0UsYUFBYUMsY0FBYyxJQUFJLENBQUNELGFBQWFFLGNBQWMsRUFBRTtvQkFDaEVyQixTQUFTO29CQUNUO2dCQUNGO2dCQUVBLHdCQUF3QjtnQkFDeEIsTUFBTXFCLGlCQUFrQ0MsS0FBS0MsS0FBSyxDQUFDSixhQUFhRSxjQUFjO2dCQUU5RXpCLFlBQVk7b0JBQ1YsR0FBR3VCLFlBQVk7b0JBQ2ZFO2dCQUNGO2dCQUVBLGlEQUFpRDtnQkFDakQsTUFBTUcsd0JBQWdELENBQUM7Z0JBQ3ZESCxlQUFlSSxPQUFPLENBQUNDLENBQUFBO29CQUNyQkYscUJBQXFCLENBQUNFLE1BQU1DLEVBQUUsQ0FBQyxHQUFHRCxNQUFNRSxhQUFhLElBQUk7Z0JBQzNEO2dCQUNBMUIsa0JBQWtCc0I7WUFFcEIsRUFBRSxPQUFPSyxLQUFLO2dCQUNaN0IsU0FBUztZQUNYLFNBQVU7Z0JBQ1JGLFdBQVc7WUFDYjtRQUNGO1FBRUEsSUFBSUwsT0FBT3FCLFNBQVMsRUFBRTtZQUNwQkg7UUFDRjtJQUNGLEdBQUc7UUFBQ2xCLE9BQU9xQixTQUFTO0tBQUM7SUFFckIsTUFBTWdCLG1CQUFtQixDQUFDQyxTQUFpQkM7UUFDekM5QixrQkFBa0IrQixDQUFBQSxPQUFTO2dCQUN6QixHQUFHQSxJQUFJO2dCQUNQLENBQUNGLFFBQVEsRUFBRUM7WUFDYjtJQUNGO0lBRUEsTUFBTUUsb0JBQW9CLE9BQU9ILFNBQWlCSTtRQUNoRCwwQ0FBMEM7UUFDMUMsTUFBTUMsV0FBV0MsSUFBSUMsZUFBZSxDQUFDSDtRQUVyQyxvQ0FBb0M7UUFDcENqQyxrQkFBa0IrQixDQUFBQSxPQUFTO2dCQUN6QixHQUFHQSxJQUFJO2dCQUNQLENBQUNGLFFBQVEsRUFBRUs7WUFDYjtJQUVBLHNFQUFzRTtJQUN0RSxtREFBbUQ7SUFDbkQsb0VBQW9FO0lBQ3RFO0lBRUEsNENBQTRDO0lBQzVDakUsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUN3QixZQUFZLENBQUNjLFVBQVU4QixPQUFPLEVBQUU7UUFFckMsa0NBQWtDO1FBQ2xDLElBQUk3QixnQkFBZ0I2QixPQUFPLEVBQUU7WUFDM0IsSUFBSTtnQkFDRjdCLGdCQUFnQjZCLE9BQU8sQ0FBQ0MsT0FBTztZQUNqQyxFQUFFLE9BQU96QyxPQUFPO2dCQUNkMEMsUUFBUUMsSUFBSSxDQUFDLDJCQUEyQjNDO1lBQzFDO1lBQ0FXLGdCQUFnQjZCLE9BQU8sR0FBRztRQUM1QjtRQUVBLCtDQUErQztRQUMvQyxNQUFNSSxhQUFhO1lBQ2pCLElBQUksQ0FBQ2xDLFVBQVU4QixPQUFPLElBQUksQ0FBQzVDLFVBQVU7WUFFckMsSUFBSTtnQkFDRiw0Q0FBNEM7Z0JBQzVDLE1BQU1pRCxnQkFBZ0JuQyxVQUFVOEIsT0FBTztnQkFDdkMsSUFBSSxDQUFDSyxjQUFjQyxVQUFVLEVBQUU7b0JBQzdCSixRQUFRQyxJQUFJLENBQUM7b0JBQ2I7Z0JBQ0Y7Z0JBRUEsbUNBQW1DO2dCQUNuQyxNQUFNSSxjQUFjRixjQUFjQyxVQUFVLENBQUM7Z0JBQzdDLElBQUksQ0FBQ0MsYUFBYTtvQkFDaEJMLFFBQVFDLElBQUksQ0FBQztvQkFDYjtnQkFDRjtnQkFFQSw4Q0FBOEM7Z0JBQzlDLE1BQU1LLFNBQVMsSUFBSWhFLDBDQUFNQSxDQUFDaUUsTUFBTSxDQUFDSixlQUFlO29CQUM5Q0ssT0FBT3RELFNBQVNzRCxLQUFLO29CQUNyQkMsUUFBUXZELFNBQVN1RCxNQUFNO29CQUN2QkMsV0FBVztvQkFDWEMsd0JBQXdCO2dCQUMxQjtnQkFFQSx5Q0FBeUM7Z0JBQ3pDLElBQUksQ0FBQ0wsT0FBT0YsVUFBVSxNQUFNLENBQUNFLE9BQU9NLFVBQVUsSUFBSTtvQkFDaERaLFFBQVFDLElBQUksQ0FBQztvQkFDYkssT0FBT1AsT0FBTztvQkFDZDtnQkFDRjtnQkFFQTlCLGdCQUFnQjZCLE9BQU8sR0FBR1E7Z0JBRTFCLHlDQUF5QztnQkFDekMsSUFBSTtvQkFDRixNQUFNTyxlQUFlaEMsS0FBS0MsS0FBSyxDQUFDNUIsU0FBU3VCLElBQUk7b0JBRTdDLGdEQUFnRDtvQkFDaEQsTUFBTSxJQUFJcUMsUUFBYyxDQUFDQyxTQUFTQzt3QkFDaENWLE9BQU9XLFlBQVksQ0FBQ0osY0FBYzs0QkFDaEMsSUFBSTtnQ0FDRixJQUFJLENBQUM1QyxnQkFBZ0I2QixPQUFPLEVBQUU7b0NBQzVCa0IsT0FBTyxJQUFJRSxNQUFNO29DQUNqQjtnQ0FDRjtnQ0FFQSwrQ0FBK0M7Z0NBQy9DakQsZ0JBQWdCNkIsT0FBTyxDQUFDcUIsYUFBYSxDQUFDO29DQUNwQ1gsT0FBT3RELFNBQVNzRCxLQUFLO29DQUNyQkMsUUFBUXZELFNBQVN1RCxNQUFNO2dDQUN6QjtnQ0FDQXhDLGdCQUFnQjZCLE9BQU8sQ0FBQ3NCLFNBQVM7Z0NBQ2pDQztnQ0FDQU47NEJBQ0YsRUFBRSxPQUFPekQsT0FBTztnQ0FDZDBELE9BQU8xRDs0QkFDVDt3QkFDRixHQUFHLENBQUNBOzRCQUNGMEQsT0FBTzFEO3dCQUNUO29CQUNGO2dCQUNGLEVBQUUsT0FBT2dFLFdBQVc7b0JBQ2xCdEIsUUFBUTFDLEtBQUssQ0FBQyxpQ0FBaUNnRTtvQkFDL0MsaUNBQWlDO29CQUNqQyxJQUFJcEUsU0FBU3FFLFlBQVksRUFBRTt3QkFDekI1RCxjQUFjVCxTQUFTcUUsWUFBWTtvQkFDckM7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9qRSxPQUFPO2dCQUNkMEMsUUFBUTFDLEtBQUssQ0FBQyxnQ0FBZ0NBO2dCQUM5QyxpQ0FBaUM7Z0JBQ2pDLElBQUlKLFNBQVNxRSxZQUFZLEVBQUU7b0JBQ3pCNUQsY0FBY1QsU0FBU3FFLFlBQVk7Z0JBQ3JDO1lBQ0Y7UUFDRjtRQUVBLHdEQUF3RDtRQUN4RCxNQUFNQyxZQUFZQyxXQUFXO1lBQzNCdkI7UUFDRixHQUFHO1FBRUgsT0FBTztZQUNMd0IsYUFBYUY7WUFDYixJQUFJdkQsZ0JBQWdCNkIsT0FBTyxFQUFFO2dCQUMzQixJQUFJO29CQUNGN0IsZ0JBQWdCNkIsT0FBTyxDQUFDQyxPQUFPO2dCQUNqQyxFQUFFLE9BQU96QyxPQUFPO29CQUNkMEMsUUFBUUMsSUFBSSxDQUFDLHNDQUFzQzNDO2dCQUNyRDtnQkFDQVcsZ0JBQWdCNkIsT0FBTyxHQUFHO1lBQzVCO1FBQ0Y7SUFDRixHQUFHO1FBQUM1QztLQUFTO0lBRWIsK0JBQStCO0lBQy9CLE1BQU1tRSw0QkFBNEJ4RixrREFBV0EsQ0FBQztRQUM1QyxJQUFJLENBQUNvQyxnQkFBZ0I2QixPQUFPLEVBQUU7UUFFOUIsSUFBSTtZQUNGLDBDQUEwQztZQUMxQyxNQUFNUSxTQUFTckMsZ0JBQWdCNkIsT0FBTztZQUN0QyxJQUFJLENBQUNRLE9BQU9GLFVBQVUsSUFBSSxDQUFDRSxPQUFPTSxVQUFVLElBQUk7Z0JBQzlDWixRQUFRQyxJQUFJLENBQUM7Z0JBQ2I7WUFDRjtZQUVBLE1BQU0wQixVQUFVckIsT0FBT3NCLFNBQVMsQ0FBQztnQkFDL0JDLFFBQVE7Z0JBQ1JDLFNBQVM7Z0JBQ1RDLFlBQVk7WUFDZDtZQUNBcEUsY0FBY2dFO1FBQ2hCLEVBQUUsT0FBT3JFLE9BQU87WUFDZDBDLFFBQVExQyxLQUFLLENBQUMsK0JBQStCQTtZQUM3Qyw2REFBNkQ7WUFDN0QsSUFBSUoscUJBQUFBLCtCQUFBQSxTQUFVcUUsWUFBWSxFQUFFO2dCQUMxQjVELGNBQWNULFNBQVNxRSxZQUFZO1lBQ3JDO1FBQ0Y7SUFDRixHQUFHO1FBQUNyRTtLQUFTO0lBRWIsaUNBQWlDO0lBQ2pDeEIsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUN1QyxnQkFBZ0I2QixPQUFPLElBQUksQ0FBQzVDLFVBQVU7UUFFM0MsTUFBTW9ELFNBQVNyQyxnQkFBZ0I2QixPQUFPO1FBRXRDLDBDQUEwQztRQUMxQyxJQUFJLENBQUNRLE9BQU9GLFVBQVUsSUFBSSxDQUFDRSxPQUFPTSxVQUFVLElBQUk7WUFDOUNaLFFBQVFDLElBQUksQ0FBQztZQUNiO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsdUJBQXVCO1lBQ3ZCK0IsT0FBT0MsT0FBTyxDQUFDekUsZ0JBQWdCd0IsT0FBTyxDQUFDO29CQUFDLENBQUNNLFNBQVNDLE1BQU07Z0JBQ3RELE1BQU0yQyxTQUFTNUIsT0FBTzZCLFVBQVUsR0FBR0MsSUFBSSxDQUFDLENBQUNDLE1BQWFBLElBQUluRCxFQUFFLEtBQUtJO2dCQUNqRSxJQUFJNEMsUUFBUTtvQkFDVixJQUFJQSxPQUFPSSxJQUFJLEtBQUssV0FBVzt3QkFDN0IsMkJBQTJCO3dCQUMxQkosT0FBMEJLLEdBQUcsQ0FBQyxRQUFRaEQ7b0JBQ3pDLE9BQU8sSUFBSTJDLE9BQU9JLElBQUksS0FBSyxXQUFXSixPQUFPSSxJQUFJLEtBQUssVUFBVUosT0FBT0ksSUFBSSxLQUFLLFVBQVU7d0JBQ3hGLDRCQUE0Qjt3QkFDNUIsSUFBSS9DLFNBQVNBLE1BQU1pRCxVQUFVLENBQUMsVUFBVTs0QkFDdENsRywwQ0FBTUEsQ0FBQ0YsS0FBSyxDQUFDcUcsT0FBTyxDQUFDbEQsT0FBTyxDQUFDbUQ7Z0NBQzNCLElBQUksQ0FBQ3pFLGdCQUFnQjZCLE9BQU8sRUFBRSxRQUFRLCtCQUErQjtnQ0FFckUsSUFBSTtvQ0FDRix1Q0FBdUM7b0NBQ3ZDLE1BQU02QyxTQUFTVCxPQUFPMUIsS0FBSyxHQUFJa0MsSUFBSWxDLEtBQUs7b0NBQ3hDLE1BQU1vQyxTQUFTVixPQUFPekIsTUFBTSxHQUFJaUMsSUFBSWpDLE1BQU07b0NBQzFDLE1BQU1vQyxRQUFRQyxLQUFLQyxHQUFHLENBQUNKLFFBQVFDO29DQUUvQkYsSUFBSUgsR0FBRyxDQUFDO3dDQUNOUyxNQUFNZCxPQUFPYyxJQUFJO3dDQUNqQkMsS0FBS2YsT0FBT2UsR0FBRzt3Q0FDZk4sUUFBUUU7d0NBQ1JELFFBQVFDO3dDQUNSSyxTQUFTaEIsT0FBT2dCLE9BQU87d0NBQ3ZCQyxTQUFTakIsT0FBT2lCLE9BQU87b0NBQ3pCO29DQUVBLHdDQUF3QztvQ0FDeENsRixnQkFBZ0I2QixPQUFPLENBQUNzRCxNQUFNLENBQUNsQjtvQ0FDL0JqRSxnQkFBZ0I2QixPQUFPLENBQUN1RCxHQUFHLENBQUNYO29DQUM1QnpFLGdCQUFnQjZCLE9BQU8sQ0FBQ3NCLFNBQVM7b0NBQ2pDQztnQ0FDRixFQUFFLE9BQU8vRCxPQUFPO29DQUNkMEMsUUFBUTFDLEtBQUssQ0FBQyx1Q0FBdUNBO2dDQUN2RDs0QkFDRjs0QkFDQSxRQUFRLDhEQUE4RDt3QkFDeEU7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUVBZ0QsT0FBT2MsU0FBUztZQUNoQkM7UUFDRixFQUFFLE9BQU8vRCxPQUFPO1lBQ2QwQyxRQUFRMUMsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDbEQ7SUFDRixHQUFHO1FBQUNFO1FBQWdCTjtRQUFVbUU7S0FBMEI7SUFFeEQsTUFBTWlDLGtCQUFrQjtRQUN0QnpGLHVCQUF1QjtRQUN2QixJQUFJO1lBQ0YsNkNBQTZDO1lBQzdDd0Q7WUFDQSxvQ0FBb0M7WUFDcEMsTUFBTSxJQUFJUCxRQUFRQyxDQUFBQSxVQUFXVSxXQUFXVixTQUFTO1FBQ25ELEVBQUUsT0FBTzNCLEtBQUs7WUFDWlksUUFBUTFDLEtBQUssQ0FBQywrQkFBK0I4QjtRQUMvQyxTQUFVO1lBQ1J2Qix1QkFBdUI7UUFDekI7SUFDRjtJQUVBLE1BQU0wRixxQkFBcUI7UUFDekJ4RixpQkFBaUI7UUFDakIsSUFBSTtZQUNGLHdFQUF3RTtZQUN4RSw2QkFBNkI7WUFDN0IsTUFBTSxJQUFJK0MsUUFBUUMsQ0FBQUEsVUFBV1UsV0FBV1YsU0FBUztZQUVqRCxrRUFBa0U7WUFDbEUsTUFBTXlDLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztZQUNwQ0YsS0FBS0csSUFBSSxHQUFHekcsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVcUUsWUFBWSxLQUFJO1lBQ3RDaUMsS0FBS0ksUUFBUSxHQUFHLGNBQXlDLE9BQTNCMUcsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVMkcsSUFBSSxLQUFJLFVBQVM7WUFDekRMLEtBQUtNLEtBQUs7UUFDWixFQUFFLE9BQU8xRSxLQUFLO1lBQ1pZLFFBQVExQyxLQUFLLENBQUMsdUJBQXVCOEI7UUFDdkMsU0FBVTtZQUNSckIsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxJQUFJWCxTQUFTO1FBQ1gscUJBQ0UsOERBQUMyRztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDaEksaUhBQU9BO2dCQUFDZ0ksV0FBVTs7Ozs7Ozs7Ozs7SUFHekI7SUFFQSxJQUFJMUcsU0FBUyxDQUFDSixVQUFVO1FBQ3RCLHFCQUNFLDhEQUFDNkc7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQ1gxRyxTQUFTOzs7Ozs7c0NBRVosOERBQUNmLHlEQUFNQTs0QkFBQzJILFNBQVMsSUFBTWpILE9BQU9rSCxJQUFJLENBQUM7NEJBQVlDLFNBQVE7OzhDQUNyRCw4REFBQ25JLGtIQUFTQTtvQ0FBQytILFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBT2xEO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDekgseURBQU1BO3dDQUNMMkgsU0FBUyxJQUFNakgsT0FBT2tILElBQUksQ0FBQyxXQUE0QixPQUFqQm5ILE9BQU9xQixTQUFTO3dDQUN0RCtGLFNBQVE7d0NBQ1JDLE1BQUs7OzBEQUVMLDhEQUFDcEksa0hBQVNBO2dEQUFDK0gsV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7OztrREFHeEMsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNEOzswREFDQyw4REFBQ0U7Z0RBQUdELFdBQVU7O29EQUFzQztvREFDdEM5RyxTQUFTMkcsSUFBSTs7Ozs7OzswREFFM0IsOERBQUNTO2dEQUFFTixXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUt6Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDekgseURBQU1BO3dDQUNMMkgsU0FBU1o7d0NBQ1RpQixVQUFVM0c7d0NBQ1Z3RyxTQUFROzs0Q0FFUHhHLG9DQUNDLDhEQUFDNUIsaUhBQU9BO2dEQUFDZ0ksV0FBVTs7Ozs7dURBQ2pCOzRDQUFLOzs7Ozs7O2tEQUdYLDhEQUFDekgseURBQU1BO3dDQUNMMkgsU0FBU1g7d0NBQ1RnQixVQUFVekc7d0NBQ1ZrRyxXQUFVOzs0Q0FFVGxHLDhCQUNDLDhEQUFDOUIsaUhBQU9BO2dEQUFDZ0ksV0FBVTs7Ozs7cUVBRW5CLDhEQUFDOUgsa0hBQVFBO2dEQUFDOEgsV0FBVTs7Ozs7OzRDQUNwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU1osOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDdEgscURBQUlBOztrREFDSCw4REFBQ0UsMkRBQVVBO2tEQUNULDRFQUFDQywwREFBU0E7NENBQUNtSCxXQUFVO3NEQUFVOzs7Ozs7Ozs7OztrREFFakMsOERBQUNySCw0REFBV0E7d0NBQUNxSCxXQUFVO2tEQUNwQjlHLFNBQVMwQixjQUFjLENBQUM0RixHQUFHLENBQUMsQ0FBQ3ZGO2dEQXVCVEEsb0JBR1pBLHFCQUVJekIsMEJBWU95QixtQ0FBQUEscUJBb0NUQTtpRUEzRVQsOERBQUM4RTtnREFBbUJDLFdBQVU7O2tFQUM1Qiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDbEgsdURBQUtBO2dFQUFDc0gsU0FBU25GLE1BQU1xRCxJQUFJLEtBQUssU0FBUyxZQUFZOztvRUFDakRyRCxNQUFNcUQsSUFBSSxLQUFLLHVCQUNkLDhEQUFDbkcsa0hBQUlBO3dFQUFDNkgsV0FBVTs7Ozs7NkZBRWhCLDhEQUFDM0gsa0hBQVNBO3dFQUFDMkgsV0FBVTs7Ozs7O29FQUV0Qi9FLE1BQU1xRCxJQUFJOzs7Ozs7OzBFQUViLDhEQUFDbUM7Z0VBQUtULFdBQVU7MEVBQXVCL0UsTUFBTTRFLElBQUk7Ozs7Ozs7Ozs7OztvREFHbEQ1RSxNQUFNcUQsSUFBSSxLQUFLLHVCQUNkLDhEQUFDeUI7OzBFQUNDLDhEQUFDdEgsdURBQUtBO2dFQUFDdUgsV0FBVTswRUFDZC9FLE1BQU15RixXQUFXLElBQUk7Ozs7OzswRUFFeEIsOERBQUNsSSx1REFBS0E7Z0VBQ0orQyxPQUFPL0IsY0FBYyxDQUFDeUIsTUFBTUMsRUFBRSxDQUFDLElBQUk7Z0VBQ25DeUYsVUFBVSxDQUFDQyxJQUFNdkYsaUJBQWlCSixNQUFNQyxFQUFFLEVBQUUwRixFQUFFQyxNQUFNLENBQUN0RixLQUFLO2dFQUMxRG1GLGFBQWF6RixNQUFNeUYsV0FBVztnRUFDOUJJLFNBQVMsR0FBRTdGLHFCQUFBQSxNQUFNOEYsV0FBVyxjQUFqQjlGLHlDQUFBQSxtQkFBbUI2RixTQUFTO2dFQUN2Q2QsV0FBVTs7Ozs7OzREQUVYL0UsRUFBQUEsc0JBQUFBLE1BQU04RixXQUFXLGNBQWpCOUYsMENBQUFBLG9CQUFtQjZGLFNBQVMsbUJBQzNCLDhEQUFDUjtnRUFBRU4sV0FBVTs7b0VBQ1Z4RyxFQUFBQSwyQkFBQUEsY0FBYyxDQUFDeUIsTUFBTUMsRUFBRSxDQUFDLGNBQXhCMUIsK0NBQUFBLHlCQUEwQndILE1BQU0sS0FBSTtvRUFBRTtvRUFBRS9GLE1BQU04RixXQUFXLENBQUNELFNBQVM7b0VBQUM7Ozs7Ozs7Ozs7Ozs2RUFLM0UsOERBQUNmOzswRUFDQyw4REFBQ3RILHVEQUFLQTtnRUFBQ3VILFdBQVU7MEVBQXdCOzs7Ozs7MEVBR3pDLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNpQjt3RUFDQzNDLE1BQUs7d0VBQ0w0QyxRQUFRakcsRUFBQUEsc0JBQUFBLE1BQU04RixXQUFXLGNBQWpCOUYsMkNBQUFBLG9DQUFBQSxvQkFBbUJrRyxjQUFjLGNBQWpDbEcsd0RBQUFBLGtDQUFtQ3VGLEdBQUcsQ0FBQ1ksQ0FBQUEsSUFBSyxJQUFNLE9BQUZBLElBQUtDLElBQUksQ0FBQyxTQUFRO3dFQUMxRVYsVUFBVSxDQUFDQztnRkFDSUE7NEVBQWIsTUFBTWxGLFFBQU9rRixrQkFBQUEsRUFBRUMsTUFBTSxDQUFDUyxLQUFLLGNBQWRWLHNDQUFBQSxlQUFnQixDQUFDLEVBQUU7NEVBQ2hDLElBQUlsRixNQUFNO2dGQUNSRCxrQkFBa0JSLE1BQU1DLEVBQUUsRUFBRVE7NEVBQzlCO3dFQUNGO3dFQUNBc0UsV0FBVTs7Ozs7O29FQUlYeEcsY0FBYyxDQUFDeUIsTUFBTUMsRUFBRSxDQUFDLGtCQUN2Qiw4REFBQzZFO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ3RCO2dGQUNDNkMsS0FBSy9ILGNBQWMsQ0FBQ3lCLE1BQU1DLEVBQUUsQ0FBQztnRkFDN0JzRyxLQUFJO2dGQUNKeEIsV0FBVTs7Ozs7OzBGQUVaLDhEQUFDRDtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNNO3dGQUFFTixXQUFVO2tHQUF3Qjs7Ozs7O2tHQUNyQyw4REFBQ3lCO3dGQUNDdkIsU0FBUzs0RkFDUHpHLGtCQUFrQitCLENBQUFBO2dHQUNoQixNQUFNa0csVUFBVTtvR0FBRSxHQUFHbEcsSUFBSTtnR0FBQztnR0FDMUIsT0FBT2tHLE9BQU8sQ0FBQ3pHLE1BQU1DLEVBQUUsQ0FBQztnR0FDeEIsT0FBT3dHOzRGQUNUO3dGQUNGO3dGQUNBMUIsV0FBVTtrR0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O29FQU9OL0UsRUFBQUEsc0JBQUFBLE1BQU04RixXQUFXLGNBQWpCOUYsMENBQUFBLG9CQUFtQjBHLFdBQVcsbUJBQzdCLDhEQUFDckI7d0VBQUVOLFdBQVU7OzRFQUF3Qjs0RUFDeEJsQixLQUFLOEMsS0FBSyxDQUFDM0csTUFBTThGLFdBQVcsQ0FBQ1ksV0FBVyxHQUFHLE9BQU87NEVBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytDQTdFckUxRyxNQUFNQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQTBGMUIsOERBQUM2RTs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ3RILHFEQUFJQTs7a0RBQ0gsOERBQUNFLDJEQUFVQTtrREFDVCw0RUFBQ0MsMERBQVNBOzRDQUFDbUgsV0FBVTtzREFBVTs7Ozs7Ozs7Ozs7a0RBRWpDLDhEQUFDckgsNERBQVdBO2tEQUNWLDRFQUFDb0g7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNEO2dEQUNDQyxXQUFVO2dEQUNWNkIsT0FBTztvREFDTEMsYUFBYSxHQUFxQjVJLE9BQWxCQSxTQUFTc0QsS0FBSyxFQUFDLEtBQW1CLE9BQWhCdEQsU0FBU3VELE1BQU07b0RBQ2pEc0YsVUFBVTtvREFDVkMsV0FBVztvREFDWHhGLE9BQU87b0RBQ1BDLFFBQVE7Z0RBQ1Y7MERBRUMvQyxjQUFjUixTQUFTcUUsWUFBWSxpQkFDbEMsOERBQUNtQjtvREFDQzZDLEtBQUs3SCxjQUFjUixTQUFTcUUsWUFBWSxJQUFJO29EQUM1Q2lFLEtBQUk7b0RBQ0p4QixXQUFVO29EQUNWNkIsT0FBTzt3REFDTHJGLE9BQU87d0RBQ1BDLFFBQVE7d0RBQ1JzRixVQUFVO3dEQUNWQyxXQUFXO29EQUNiOzs7Ozt5RUFHRiw4REFBQ2pDO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDM0gsa0hBQVNBO3dEQUFDMkgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVl2Qyw4REFBQzFEO2dCQUNDMkYsS0FBS2pJO2dCQUNMNkgsT0FBTztvQkFBRUssU0FBUztnQkFBTztnQkFDekIxRixPQUFPdEQsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVc0QsS0FBSyxLQUFJO2dCQUMxQkMsUUFBUXZELENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVXVELE1BQU0sS0FBSTs7Ozs7Ozs7Ozs7O0FBSXBDO0dBemlCd0IxRDs7UUFDUGpCLHNEQUFTQTtRQUNUQyxzREFBU0E7OztLQUZGZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9wdWJsaWMvW3Byb2plY3RJZF0vY3VzdG9taXplL3BhZ2UudHN4PzE5ZDEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUsIHVzZVJlZiwgdXNlQ2FsbGJhY2sgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZVBhcmFtcywgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IHsgTG9hZGVyMiwgQXJyb3dMZWZ0LCBEb3dubG9hZCwgVXBsb2FkLCBUeXBlLCBJbWFnZSBhcyBJbWFnZUljb24gfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XG5pbXBvcnQgeyBmYWJyaWMgfSBmcm9tIFwiZmFicmljXCI7XG5cbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIjtcbmltcG9ydCB7IExhYmVsIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9sYWJlbFwiO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2JhZGdlXCI7XG5cbmltcG9ydCB7IFRlbXBsYXRlUmVzcG9uc2UsIEVkaXRhYmxlTGF5ZXIgfSBmcm9tIFwiQC90eXBlcy90ZW1wbGF0ZVwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDdXN0b21pemVUZW1wbGF0ZVBhZ2UoKSB7XG4gIGNvbnN0IHBhcmFtcyA9IHVzZVBhcmFtcygpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgW3RlbXBsYXRlLCBzZXRUZW1wbGF0ZV0gPSB1c2VTdGF0ZTxUZW1wbGF0ZVJlc3BvbnNlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbY3VzdG9taXphdGlvbnMsIHNldEN1c3RvbWl6YXRpb25zXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIHN0cmluZz4+KHt9KTtcbiAgY29uc3QgW3ByZXZpZXdVcmwsIHNldFByZXZpZXdVcmxdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc0dlbmVyYXRpbmdQcmV2aWV3LCBzZXRJc0dlbmVyYXRpbmdQcmV2aWV3XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzRG93bmxvYWRpbmcsIHNldElzRG93bmxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIENhbnZhcyByZWZzIGZvciBwcmV2aWV3IGdlbmVyYXRpb25cbiAgY29uc3QgY2FudmFzUmVmID0gdXNlUmVmPEhUTUxDYW52YXNFbGVtZW50PihudWxsKTtcbiAgY29uc3QgZmFicmljQ2FudmFzUmVmID0gdXNlUmVmPGZhYnJpYy5DYW52YXMgfCBudWxsPihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGZldGNoVGVtcGxhdGUgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3Byb2plY3RzL3B1YmxpYy8ke3BhcmFtcy5wcm9qZWN0SWR9YCk7XG4gICAgICAgIFxuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDA0KSB7XG4gICAgICAgICAgICBzZXRFcnJvcihcIlRlbXBsYXRlIG5vdCBmb3VuZCBvciBub3QgcHVibGljXCIpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzZXRFcnJvcihcIkZhaWxlZCB0byBsb2FkIHRlbXBsYXRlXCIpO1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnN0IHRlbXBsYXRlRGF0YSA9IGRhdGEuZGF0YTtcbiAgICAgICAgXG4gICAgICAgIC8vIENoZWNrIGlmIHRlbXBsYXRlIGlzIGN1c3RvbWl6YWJsZVxuICAgICAgICBpZiAoIXRlbXBsYXRlRGF0YS5pc0N1c3RvbWl6YWJsZSB8fCAhdGVtcGxhdGVEYXRhLmVkaXRhYmxlTGF5ZXJzKSB7XG4gICAgICAgICAgc2V0RXJyb3IoXCJUaGlzIHRlbXBsYXRlIGlzIG5vdCBjdXN0b21pemFibGVcIik7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gUGFyc2UgZWRpdGFibGUgbGF5ZXJzXG4gICAgICAgIGNvbnN0IGVkaXRhYmxlTGF5ZXJzOiBFZGl0YWJsZUxheWVyW10gPSBKU09OLnBhcnNlKHRlbXBsYXRlRGF0YS5lZGl0YWJsZUxheWVycyk7XG4gICAgICAgIFxuICAgICAgICBzZXRUZW1wbGF0ZSh7XG4gICAgICAgICAgLi4udGVtcGxhdGVEYXRhLFxuICAgICAgICAgIGVkaXRhYmxlTGF5ZXJzLFxuICAgICAgICB9KTtcblxuICAgICAgICAvLyBJbml0aWFsaXplIGN1c3RvbWl6YXRpb25zIHdpdGggb3JpZ2luYWwgdmFsdWVzXG4gICAgICAgIGNvbnN0IGluaXRpYWxDdXN0b21pemF0aW9uczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHt9O1xuICAgICAgICBlZGl0YWJsZUxheWVycy5mb3JFYWNoKGxheWVyID0+IHtcbiAgICAgICAgICBpbml0aWFsQ3VzdG9taXphdGlvbnNbbGF5ZXIuaWRdID0gbGF5ZXIub3JpZ2luYWxWYWx1ZSB8fCAnJztcbiAgICAgICAgfSk7XG4gICAgICAgIHNldEN1c3RvbWl6YXRpb25zKGluaXRpYWxDdXN0b21pemF0aW9ucyk7XG4gICAgICAgIFxuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIHNldEVycm9yKFwiRmFpbGVkIHRvIGxvYWQgdGVtcGxhdGVcIik7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaWYgKHBhcmFtcy5wcm9qZWN0SWQpIHtcbiAgICAgIGZldGNoVGVtcGxhdGUoKTtcbiAgICB9XG4gIH0sIFtwYXJhbXMucHJvamVjdElkXSk7XG5cbiAgY29uc3QgaGFuZGxlVGV4dENoYW5nZSA9IChsYXllcklkOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRDdXN0b21pemF0aW9ucyhwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW2xheWVySWRdOiB2YWx1ZSxcbiAgICB9KSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VVcGxvYWQgPSBhc3luYyAobGF5ZXJJZDogc3RyaW5nLCBmaWxlOiBGaWxlKSA9PiB7XG4gICAgLy8gQ3JlYXRlIG9iamVjdCBVUkwgZm9yIGltbWVkaWF0ZSBwcmV2aWV3XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGZpbGUpO1xuXG4gICAgLy8gVXBkYXRlIGN1c3RvbWl6YXRpb25zIGltbWVkaWF0ZWx5XG4gICAgc2V0Q3VzdG9taXphdGlvbnMocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtsYXllcklkXTogaW1hZ2VVcmwsXG4gICAgfSkpO1xuXG4gICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB5b3UnZCB1cGxvYWQgdG8geW91ciBzdG9yYWdlIHNlcnZpY2UgaGVyZVxuICAgIC8vIGNvbnN0IHVwbG9hZGVkVXJsID0gYXdhaXQgdXBsb2FkVG9TdG9yYWdlKGZpbGUpO1xuICAgIC8vIHNldEN1c3RvbWl6YXRpb25zKHByZXYgPT4gKHsgLi4ucHJldiwgW2xheWVySWRdOiB1cGxvYWRlZFVybCB9KSk7XG4gIH07XG5cbiAgLy8gSW5pdGlhbGl6ZSBjYW52YXMgd2hlbiB0ZW1wbGF0ZSBpcyBsb2FkZWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXRlbXBsYXRlIHx8ICFjYW52YXNSZWYuY3VycmVudCkgcmV0dXJuO1xuXG4gICAgLy8gQ2xlYW4gdXAgZXhpc3RpbmcgY2FudmFzIHNhZmVseVxuICAgIGlmIChmYWJyaWNDYW52YXNSZWYuY3VycmVudCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgZmFicmljQ2FudmFzUmVmLmN1cnJlbnQuZGlzcG9zZSgpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdFcnJvciBkaXNwb3NpbmcgY2FudmFzOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG5cbiAgICAvLyBJbml0aWFsaXplIGNhbnZhcyB3aXRoIHByb3BlciBlcnJvciBoYW5kbGluZ1xuICAgIGNvbnN0IGluaXRDYW52YXMgPSBhc3luYyAoKSA9PiB7XG4gICAgICBpZiAoIWNhbnZhc1JlZi5jdXJyZW50IHx8ICF0ZW1wbGF0ZSkgcmV0dXJuO1xuXG4gICAgICB0cnkge1xuICAgICAgICAvLyBFbnN1cmUgY2FudmFzIGVsZW1lbnQgaXMgcHJvcGVybHkgbW91bnRlZFxuICAgICAgICBjb25zdCBjYW52YXNFbGVtZW50ID0gY2FudmFzUmVmLmN1cnJlbnQ7XG4gICAgICAgIGlmICghY2FudmFzRWxlbWVudC5nZXRDb250ZXh0KSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCdDYW52YXMgZWxlbWVudCBub3QgcmVhZHknKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyBUZXN0IGNhbnZhcyBjb250ZXh0IGF2YWlsYWJpbGl0eVxuICAgICAgICBjb25zdCB0ZXN0Q29udGV4dCA9IGNhbnZhc0VsZW1lbnQuZ2V0Q29udGV4dCgnMmQnKTtcbiAgICAgICAgaWYgKCF0ZXN0Q29udGV4dCkge1xuICAgICAgICAgIGNvbnNvbGUud2FybignQ2FudmFzIDJEIGNvbnRleHQgbm90IGF2YWlsYWJsZScpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIENyZWF0ZSBuZXcgZmFicmljIGNhbnZhcyB3aXRoIHByb3BlciBzaXppbmdcbiAgICAgICAgY29uc3QgY2FudmFzID0gbmV3IGZhYnJpYy5DYW52YXMoY2FudmFzRWxlbWVudCwge1xuICAgICAgICAgIHdpZHRoOiB0ZW1wbGF0ZS53aWR0aCxcbiAgICAgICAgICBoZWlnaHQ6IHRlbXBsYXRlLmhlaWdodCxcbiAgICAgICAgICBzZWxlY3Rpb246IGZhbHNlLFxuICAgICAgICAgIHByZXNlcnZlT2JqZWN0U3RhY2tpbmc6IHRydWUsXG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIFZlcmlmeSBjYW52YXMgd2FzIGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5XG4gICAgICAgIGlmICghY2FudmFzLmdldENvbnRleHQoKSB8fCAhY2FudmFzLmdldEVsZW1lbnQoKSkge1xuICAgICAgICAgIGNvbnNvbGUud2FybignRmFicmljIGNhbnZhcyBub3QgcHJvcGVybHkgaW5pdGlhbGl6ZWQnKTtcbiAgICAgICAgICBjYW52YXMuZGlzcG9zZSgpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50ID0gY2FudmFzO1xuXG4gICAgICAgIC8vIExvYWQgdGVtcGxhdGUgSlNPTiB3aXRoIGVycm9yIGhhbmRsaW5nXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgdGVtcGxhdGVKc29uID0gSlNPTi5wYXJzZSh0ZW1wbGF0ZS5qc29uKTtcblxuICAgICAgICAgIC8vIFVzZSBhIHByb21pc2UtYmFzZWQgYXBwcm9hY2ggZm9yIGxvYWRGcm9tSlNPTlxuICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlPHZvaWQ+KChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgIGNhbnZhcy5sb2FkRnJvbUpTT04odGVtcGxhdGVKc29uLCAoKSA9PiB7XG4gICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgaWYgKCFmYWJyaWNDYW52YXNSZWYuY3VycmVudCkge1xuICAgICAgICAgICAgICAgICAgcmVqZWN0KG5ldyBFcnJvcignQ2FudmFzIGRpc3Bvc2VkIGR1cmluZyBsb2FkaW5nJykpO1xuICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC8vIEVuc3VyZSBjYW52YXMgaXMgcHJvcGVybHkgc2l6ZWQgYW5kIHJlbmRlcmVkXG4gICAgICAgICAgICAgICAgZmFicmljQ2FudmFzUmVmLmN1cnJlbnQuc2V0RGltZW5zaW9ucyh7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogdGVtcGxhdGUud2lkdGgsXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6IHRlbXBsYXRlLmhlaWdodFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50LnJlbmRlckFsbCgpO1xuICAgICAgICAgICAgICAgIGdlbmVyYXRlUHJldmlld0Zyb21DYW52YXMoKTtcbiAgICAgICAgICAgICAgICByZXNvbHZlKCk7XG4gICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgcmVqZWN0KGVycm9yKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSwgKGVycm9yOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgcmVqZWN0KGVycm9yKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0pO1xuICAgICAgICB9IGNhdGNoIChqc29uRXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCB0ZW1wbGF0ZSBKU09OOicsIGpzb25FcnJvcik7XG4gICAgICAgICAgLy8gRmFsbGJhY2sgdG8gdGVtcGxhdGUgdGh1bWJuYWlsXG4gICAgICAgICAgaWYgKHRlbXBsYXRlLnRodW1ibmFpbFVybCkge1xuICAgICAgICAgICAgc2V0UHJldmlld1VybCh0ZW1wbGF0ZS50aHVtYm5haWxVcmwpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGluaXRpYWxpemUgY2FudmFzOicsIGVycm9yKTtcbiAgICAgICAgLy8gRmFsbGJhY2sgdG8gdGVtcGxhdGUgdGh1bWJuYWlsXG4gICAgICAgIGlmICh0ZW1wbGF0ZS50aHVtYm5haWxVcmwpIHtcbiAgICAgICAgICBzZXRQcmV2aWV3VXJsKHRlbXBsYXRlLnRodW1ibmFpbFVybCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9O1xuXG4gICAgLy8gVXNlIG11bHRpcGxlIHRpbWVvdXRzIHRvIGVuc3VyZSBwcm9wZXIgaW5pdGlhbGl6YXRpb25cbiAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGluaXRDYW52YXMoKTtcbiAgICB9LCAyMDApO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgaWYgKGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50KSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgZmFicmljQ2FudmFzUmVmLmN1cnJlbnQuZGlzcG9zZSgpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUud2FybignRXJyb3IgZGlzcG9zaW5nIGNhbnZhcyBpbiBjbGVhbnVwOicsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgICBmYWJyaWNDYW52YXNSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW3RlbXBsYXRlXSk7XG5cbiAgLy8gR2VuZXJhdGUgcHJldmlldyBmcm9tIGNhbnZhc1xuICBjb25zdCBnZW5lcmF0ZVByZXZpZXdGcm9tQ2FudmFzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghZmFicmljQ2FudmFzUmVmLmN1cnJlbnQpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICAvLyBDaGVjayBpZiBjYW52YXMgaXMgcHJvcGVybHkgaW5pdGlhbGl6ZWRcbiAgICAgIGNvbnN0IGNhbnZhcyA9IGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50O1xuICAgICAgaWYgKCFjYW52YXMuZ2V0Q29udGV4dCB8fCAhY2FudmFzLmdldEVsZW1lbnQoKSkge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0NhbnZhcyBub3QgcHJvcGVybHkgaW5pdGlhbGl6ZWQnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhVVJMID0gY2FudmFzLnRvRGF0YVVSTCh7XG4gICAgICAgIGZvcm1hdDogJ3BuZycsXG4gICAgICAgIHF1YWxpdHk6IDAuOCxcbiAgICAgICAgbXVsdGlwbGllcjogMSxcbiAgICAgIH0pO1xuICAgICAgc2V0UHJldmlld1VybChkYXRhVVJMKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGdlbmVyYXRlIHByZXZpZXc6JywgZXJyb3IpO1xuICAgICAgLy8gRmFsbGJhY2sgdG8gdGVtcGxhdGUgdGh1bWJuYWlsIGlmIHByZXZpZXcgZ2VuZXJhdGlvbiBmYWlsc1xuICAgICAgaWYgKHRlbXBsYXRlPy50aHVtYm5haWxVcmwpIHtcbiAgICAgICAgc2V0UHJldmlld1VybCh0ZW1wbGF0ZS50aHVtYm5haWxVcmwpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW3RlbXBsYXRlXSk7XG5cbiAgLy8gQXBwbHkgY3VzdG9taXphdGlvbnMgdG8gY2FudmFzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFmYWJyaWNDYW52YXNSZWYuY3VycmVudCB8fCAhdGVtcGxhdGUpIHJldHVybjtcblxuICAgIGNvbnN0IGNhbnZhcyA9IGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50O1xuXG4gICAgLy8gQ2hlY2sgaWYgY2FudmFzIGlzIHByb3Blcmx5IGluaXRpYWxpemVkXG4gICAgaWYgKCFjYW52YXMuZ2V0Q29udGV4dCB8fCAhY2FudmFzLmdldEVsZW1lbnQoKSkge1xuICAgICAgY29uc29sZS53YXJuKCdDYW52YXMgbm90IHJlYWR5IGZvciBjdXN0b21pemF0aW9ucycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAvLyBBcHBseSBjdXN0b21pemF0aW9uc1xuICAgICAgT2JqZWN0LmVudHJpZXMoY3VzdG9taXphdGlvbnMpLmZvckVhY2goKFtsYXllcklkLCB2YWx1ZV0pID0+IHtcbiAgICAgICAgY29uc3Qgb2JqZWN0ID0gY2FudmFzLmdldE9iamVjdHMoKS5maW5kKChvYmo6IGFueSkgPT4gb2JqLmlkID09PSBsYXllcklkKTtcbiAgICAgICAgaWYgKG9iamVjdCkge1xuICAgICAgICAgIGlmIChvYmplY3QudHlwZSA9PT0gJ3RleHRib3gnKSB7XG4gICAgICAgICAgICAvLyBBcHBseSB0ZXh0IGN1c3RvbWl6YXRpb25cbiAgICAgICAgICAgIChvYmplY3QgYXMgZmFicmljLlRleHRib3gpLnNldCgndGV4dCcsIHZhbHVlKTtcbiAgICAgICAgICB9IGVsc2UgaWYgKG9iamVjdC50eXBlID09PSAnaW1hZ2UnIHx8IG9iamVjdC50eXBlID09PSAncmVjdCcgfHwgb2JqZWN0LnR5cGUgPT09ICdjaXJjbGUnKSB7XG4gICAgICAgICAgICAvLyBBcHBseSBpbWFnZSBjdXN0b21pemF0aW9uXG4gICAgICAgICAgICBpZiAodmFsdWUgJiYgdmFsdWUuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgICAgICAgICBmYWJyaWMuSW1hZ2UuZnJvbVVSTCh2YWx1ZSwgKGltZykgPT4ge1xuICAgICAgICAgICAgICAgIGlmICghZmFicmljQ2FudmFzUmVmLmN1cnJlbnQpIHJldHVybjsgLy8gQ2hlY2sgaWYgY2FudmFzIHN0aWxsIGV4aXN0c1xuXG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgIC8vIFNjYWxlIGltYWdlIHRvIGZpdCB0aGUgb2JqZWN0IGJvdW5kc1xuICAgICAgICAgICAgICAgICAgY29uc3Qgc2NhbGVYID0gb2JqZWN0LndpZHRoISAvIGltZy53aWR0aCE7XG4gICAgICAgICAgICAgICAgICBjb25zdCBzY2FsZVkgPSBvYmplY3QuaGVpZ2h0ISAvIGltZy5oZWlnaHQhO1xuICAgICAgICAgICAgICAgICAgY29uc3Qgc2NhbGUgPSBNYXRoLm1pbihzY2FsZVgsIHNjYWxlWSk7XG5cbiAgICAgICAgICAgICAgICAgIGltZy5zZXQoe1xuICAgICAgICAgICAgICAgICAgICBsZWZ0OiBvYmplY3QubGVmdCxcbiAgICAgICAgICAgICAgICAgICAgdG9wOiBvYmplY3QudG9wLFxuICAgICAgICAgICAgICAgICAgICBzY2FsZVg6IHNjYWxlLFxuICAgICAgICAgICAgICAgICAgICBzY2FsZVk6IHNjYWxlLFxuICAgICAgICAgICAgICAgICAgICBvcmlnaW5YOiBvYmplY3Qub3JpZ2luWCxcbiAgICAgICAgICAgICAgICAgICAgb3JpZ2luWTogb2JqZWN0Lm9yaWdpblksXG4gICAgICAgICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgICAgICAgLy8gUmVwbGFjZSB0aGUgb2JqZWN0IHdpdGggdGhlIG5ldyBpbWFnZVxuICAgICAgICAgICAgICAgICAgZmFicmljQ2FudmFzUmVmLmN1cnJlbnQucmVtb3ZlKG9iamVjdCk7XG4gICAgICAgICAgICAgICAgICBmYWJyaWNDYW52YXNSZWYuY3VycmVudC5hZGQoaW1nKTtcbiAgICAgICAgICAgICAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50LnJlbmRlckFsbCgpO1xuICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVQcmV2aWV3RnJvbUNhbnZhcygpO1xuICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhcHBseWluZyBpbWFnZSBjdXN0b21pemF0aW9uOicsIGVycm9yKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICByZXR1cm47IC8vIFNraXAgdGhlIHJlbmRlckFsbCBiZWxvdyBzaW5jZSBpdCdzIGhhbmRsZWQgaW4gdGhlIGNhbGxiYWNrXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xuICAgICAgZ2VuZXJhdGVQcmV2aWV3RnJvbUNhbnZhcygpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhcHBseWluZyBjdXN0b21pemF0aW9uczonLCBlcnJvcik7XG4gICAgfVxuICB9LCBbY3VzdG9taXphdGlvbnMsIHRlbXBsYXRlLCBnZW5lcmF0ZVByZXZpZXdGcm9tQ2FudmFzXSk7XG5cbiAgY29uc3QgZ2VuZXJhdGVQcmV2aWV3ID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzR2VuZXJhdGluZ1ByZXZpZXcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdlbmVyYXRlIHByZXZpZXcgZnJvbSBjdXJyZW50IGNhbnZhcyBzdGF0ZVxuICAgICAgZ2VuZXJhdGVQcmV2aWV3RnJvbUNhbnZhcygpO1xuICAgICAgLy8gU21hbGwgZGVsYXkgdG8gc2hvdyBsb2FkaW5nIHN0YXRlXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwKSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2VuZXJhdGUgcHJldmlldzonLCBlcnIpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0dlbmVyYXRpbmdQcmV2aWV3KGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZG93bmxvYWRDdXN0b21pemVkID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzRG93bmxvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFRoaXMgd291bGQgY2FsbCBhbiBBUEkgdG8gZ2VuZXJhdGUgYW5kIGRvd25sb2FkIHRoZSBjdXN0b21pemVkIGRlc2lnblxuICAgICAgLy8gRm9yIG5vdywgd2UnbGwgc2ltdWxhdGUgaXRcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAzMDAwKSk7XG4gICAgICBcbiAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgeW91J2QgZ2V0IGEgZG93bmxvYWQgVVJMIGZyb20gdGhlIEFQSVxuICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgICAgIGxpbmsuaHJlZiA9IHRlbXBsYXRlPy50aHVtYm5haWxVcmwgfHwgJyc7XG4gICAgICBsaW5rLmRvd25sb2FkID0gYGN1c3RvbWl6ZWQtJHt0ZW1wbGF0ZT8ubmFtZSB8fCAnZGVzaWduJ30ucG5nYDtcbiAgICAgIGxpbmsuY2xpY2soKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBkb3dubG9hZDonLCBlcnIpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0Rvd25sb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTggdy04IGFuaW1hdGUtc3BpbiB0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmIChlcnJvciB8fCAhdGVtcGxhdGUpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICAgIHtlcnJvciB8fCBcIlRlbXBsYXRlIG5vdCBmb3VuZFwifVxuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goXCIvcHVibGljXCIpfSB2YXJpYW50PVwib3V0bGluZVwiPlxuICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIEJhY2sgdG8gR2FsbGVyeVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goYC9wdWJsaWMvJHtwYXJhbXMucHJvamVjdElkfWApfSBcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIiBcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIEJhY2tcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02IHctcHggYmctZ3JheS0zMDBcIiAvPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgQ3VzdG9taXplOiB7dGVtcGxhdGUubmFtZX1cbiAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgTWFrZSB0aGlzIHRlbXBsYXRlIHlvdXIgb3duXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dlbmVyYXRlUHJldmlld31cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNHZW5lcmF0aW5nUHJldmlld31cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNHZW5lcmF0aW5nUHJldmlldyA/IChcbiAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtNCB3LTQgYW5pbWF0ZS1zcGluIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICkgOiBudWxsfVxuICAgICAgICAgICAgICAgIFByZXZpZXdcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtkb3dubG9hZEN1c3RvbWl6ZWR9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRG93bmxvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHVycGxlLTYwMCBob3ZlcjpiZy1wdXJwbGUtNzAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc0Rvd25sb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC00IHctNCBhbmltYXRlLXNwaW4gbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgRG93bmxvYWRcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICB7LyogQ3VzdG9taXphdGlvbiBQYW5lbCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTFcIj5cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj5DdXN0b21pemUgRWxlbWVudHM8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAge3RlbXBsYXRlLmVkaXRhYmxlTGF5ZXJzLm1hcCgobGF5ZXIpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtsYXllci5pZH0gY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9e2xheWVyLnR5cGUgPT09ICd0ZXh0JyA/ICdkZWZhdWx0JyA6ICdzZWNvbmRhcnknfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtsYXllci50eXBlID09PSAndGV4dCcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBlIGNsYXNzTmFtZT1cImgtMyB3LTMgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW1hZ2VJY29uIGNsYXNzTmFtZT1cImgtMyB3LTMgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAge2xheWVyLnR5cGV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+e2xheWVyLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7bGF5ZXIudHlwZSA9PT0gJ3RleHQnID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtsYXllci5wbGFjZWhvbGRlciB8fCAnRW50ZXIgdGV4dCd9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjdXN0b21pemF0aW9uc1tsYXllci5pZF0gfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlVGV4dENoYW5nZShsYXllci5pZCwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17bGF5ZXIucGxhY2Vob2xkZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1heExlbmd0aD17bGF5ZXIuY29uc3RyYWludHM/Lm1heExlbmd0aH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAge2xheWVyLmNvbnN0cmFpbnRzPy5tYXhMZW5ndGggJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjdXN0b21pemF0aW9uc1tsYXllci5pZF0/Lmxlbmd0aCB8fCAwfS97bGF5ZXIuY29uc3RyYWludHMubWF4TGVuZ3RofSBjaGFyYWN0ZXJzXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgVXBsb2FkIHlvdXIgaW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEgc3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY2NlcHQ9e2xheWVyLmNvbnN0cmFpbnRzPy5hbGxvd2VkRm9ybWF0cz8ubWFwKGYgPT4gYC4ke2Z9YCkuam9pbignLCcpIHx8ICdpbWFnZS8qJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGUgPSBlLnRhcmdldC5maWxlcz8uWzBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSW1hZ2VVcGxvYWQobGF5ZXIuaWQsIGZpbGUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdy1mdWxsIHRleHQtc20gdGV4dC1ncmF5LTUwMCBmaWxlOm1yLTQgZmlsZTpweS0yIGZpbGU6cHgtNCBmaWxlOnJvdW5kZWQtZnVsbCBmaWxlOmJvcmRlci0wIGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtc2VtaWJvbGQgZmlsZTpiZy1wdXJwbGUtNTAgZmlsZTp0ZXh0LXB1cnBsZS03MDAgaG92ZXI6ZmlsZTpiZy1wdXJwbGUtMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogU21hbGwgcHJldmlldyBvZiB1cGxvYWRlZCBpbWFnZSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2N1c3RvbWl6YXRpb25zW2xheWVyLmlkXSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtjdXN0b21pemF0aW9uc1tsYXllci5pZF19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD1cIlVwbG9hZGVkIHByZXZpZXdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgb2JqZWN0LWNvdmVyIHJvdW5kZWQgYm9yZGVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5JbWFnZSB1cGxvYWRlZDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEN1c3RvbWl6YXRpb25zKHByZXYgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkID0geyAuLi5wcmV2IH07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlbGV0ZSB1cGRhdGVkW2xheWVyLmlkXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHVwZGF0ZWQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1yZWQtNTAwIGhvdmVyOnRleHQtcmVkLTcwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZW1vdmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bGF5ZXIuY29uc3RyYWludHM/Lm1heEZpbGVTaXplICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1heCBzaXplOiB7TWF0aC5yb3VuZChsYXllci5jb25zdHJhaW50cy5tYXhGaWxlU2l6ZSAvIDEwMjQgLyAxMDI0KX1NQlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUHJldmlldyBQYW5lbCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj5QcmV2aWV3PC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS0xMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgb3ZlcmZsb3ctaGlkZGVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBhc3BlY3RSYXRpbzogYCR7dGVtcGxhdGUud2lkdGh9LyR7dGVtcGxhdGUuaGVpZ2h0fWAsXG4gICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6IFwiMTAwJVwiLFxuICAgICAgICAgICAgICAgICAgICAgIG1heEhlaWdodDogXCI1MDBweFwiLFxuICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiBcImF1dG9cIixcbiAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IFwiYXV0b1wiXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtwcmV2aWV3VXJsIHx8IHRlbXBsYXRlLnRodW1ibmFpbFVybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICBzcmM9e3ByZXZpZXdVcmwgfHwgdGVtcGxhdGUudGh1bWJuYWlsVXJsIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiVGVtcGxhdGUgcHJldmlld1wiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy1mdWxsIG1heC1oLWZ1bGwgb2JqZWN0LWNvbnRhaW5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IFwiYXV0b1wiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IFwiYXV0b1wiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDogXCIxMDAlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1heEhlaWdodDogXCIxMDAlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWdyYXktNTAgdy1mdWxsIGgtZnVsbCBtaW4taC1bMjAwcHhdXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SW1hZ2VJY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogSGlkZGVuIGNhbnZhcyBmb3IgcHJldmlldyBnZW5lcmF0aW9uICovfVxuICAgICAgPGNhbnZhc1xuICAgICAgICByZWY9e2NhbnZhc1JlZn1cbiAgICAgICAgc3R5bGU9e3sgZGlzcGxheTogJ25vbmUnIH19XG4gICAgICAgIHdpZHRoPXt0ZW1wbGF0ZT8ud2lkdGggfHwgODAwfVxuICAgICAgICBoZWlnaHQ9e3RlbXBsYXRlPy5oZWlnaHQgfHwgNjAwfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUNhbGxiYWNrIiwidXNlUGFyYW1zIiwidXNlUm91dGVyIiwiTG9hZGVyMiIsIkFycm93TGVmdCIsIkRvd25sb2FkIiwiVHlwZSIsIkltYWdlIiwiSW1hZ2VJY29uIiwiZmFicmljIiwiQnV0dG9uIiwiSW5wdXQiLCJMYWJlbCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCYWRnZSIsIkN1c3RvbWl6ZVRlbXBsYXRlUGFnZSIsInBhcmFtcyIsInJvdXRlciIsInRlbXBsYXRlIiwic2V0VGVtcGxhdGUiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJjdXN0b21pemF0aW9ucyIsInNldEN1c3RvbWl6YXRpb25zIiwicHJldmlld1VybCIsInNldFByZXZpZXdVcmwiLCJpc0dlbmVyYXRpbmdQcmV2aWV3Iiwic2V0SXNHZW5lcmF0aW5nUHJldmlldyIsImlzRG93bmxvYWRpbmciLCJzZXRJc0Rvd25sb2FkaW5nIiwiY2FudmFzUmVmIiwiZmFicmljQ2FudmFzUmVmIiwiZmV0Y2hUZW1wbGF0ZSIsInJlc3BvbnNlIiwiZmV0Y2giLCJwcm9qZWN0SWQiLCJvayIsInN0YXR1cyIsImRhdGEiLCJqc29uIiwidGVtcGxhdGVEYXRhIiwiaXNDdXN0b21pemFibGUiLCJlZGl0YWJsZUxheWVycyIsIkpTT04iLCJwYXJzZSIsImluaXRpYWxDdXN0b21pemF0aW9ucyIsImZvckVhY2giLCJsYXllciIsImlkIiwib3JpZ2luYWxWYWx1ZSIsImVyciIsImhhbmRsZVRleHRDaGFuZ2UiLCJsYXllcklkIiwidmFsdWUiLCJwcmV2IiwiaGFuZGxlSW1hZ2VVcGxvYWQiLCJmaWxlIiwiaW1hZ2VVcmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJjdXJyZW50IiwiZGlzcG9zZSIsImNvbnNvbGUiLCJ3YXJuIiwiaW5pdENhbnZhcyIsImNhbnZhc0VsZW1lbnQiLCJnZXRDb250ZXh0IiwidGVzdENvbnRleHQiLCJjYW52YXMiLCJDYW52YXMiLCJ3aWR0aCIsImhlaWdodCIsInNlbGVjdGlvbiIsInByZXNlcnZlT2JqZWN0U3RhY2tpbmciLCJnZXRFbGVtZW50IiwidGVtcGxhdGVKc29uIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJsb2FkRnJvbUpTT04iLCJFcnJvciIsInNldERpbWVuc2lvbnMiLCJyZW5kZXJBbGwiLCJnZW5lcmF0ZVByZXZpZXdGcm9tQ2FudmFzIiwianNvbkVycm9yIiwidGh1bWJuYWlsVXJsIiwidGltZW91dElkIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImRhdGFVUkwiLCJ0b0RhdGFVUkwiLCJmb3JtYXQiLCJxdWFsaXR5IiwibXVsdGlwbGllciIsIk9iamVjdCIsImVudHJpZXMiLCJvYmplY3QiLCJnZXRPYmplY3RzIiwiZmluZCIsIm9iaiIsInR5cGUiLCJzZXQiLCJzdGFydHNXaXRoIiwiZnJvbVVSTCIsImltZyIsInNjYWxlWCIsInNjYWxlWSIsInNjYWxlIiwiTWF0aCIsIm1pbiIsImxlZnQiLCJ0b3AiLCJvcmlnaW5YIiwib3JpZ2luWSIsInJlbW92ZSIsImFkZCIsImdlbmVyYXRlUHJldmlldyIsImRvd25sb2FkQ3VzdG9taXplZCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJuYW1lIiwiY2xpY2siLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsIm9uQ2xpY2siLCJwdXNoIiwidmFyaWFudCIsInNpemUiLCJwIiwiZGlzYWJsZWQiLCJtYXAiLCJzcGFuIiwicGxhY2Vob2xkZXIiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJtYXhMZW5ndGgiLCJjb25zdHJhaW50cyIsImxlbmd0aCIsImlucHV0IiwiYWNjZXB0IiwiYWxsb3dlZEZvcm1hdHMiLCJmIiwiam9pbiIsImZpbGVzIiwic3JjIiwiYWx0IiwiYnV0dG9uIiwidXBkYXRlZCIsIm1heEZpbGVTaXplIiwicm91bmQiLCJzdHlsZSIsImFzcGVjdFJhdGlvIiwibWF4V2lkdGgiLCJtYXhIZWlnaHQiLCJyZWYiLCJkaXNwbGF5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ })

});