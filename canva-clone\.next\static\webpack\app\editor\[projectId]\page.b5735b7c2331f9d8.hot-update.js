"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/editor.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Editor: function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-thumbnail-generator */ \"(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/components/navbar */ \"(app-pages-browser)/./src/features/editor/components/navbar.tsx\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/components/sidebar */ \"(app-pages-browser)/./src/features/editor/components/sidebar.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* harmony import */ var _features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/editor/components/shape-sidebar */ \"(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/editor/components/fill-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/fill-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/features/editor/components/stroke-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/editor/components/stroke-width-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-width-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/features/editor/components/opacity-sidebar */ \"(app-pages-browser)/./src/features/editor/components/opacity-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/features/editor/components/text-sidebar */ \"(app-pages-browser)/./src/features/editor/components/text-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/editor/components/font-sidebar */ \"(app-pages-browser)/./src/features/editor/components/font-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/editor/components/image-sidebar */ \"(app-pages-browser)/./src/features/editor/components/image-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/editor/components/filter-sidebar */ \"(app-pages-browser)/./src/features/editor/components/filter-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/editor/components/draw-sidebar */ \"(app-pages-browser)/./src/features/editor/components/draw-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/editor/components/ai-sidebar */ \"(app-pages-browser)/./src/features/editor/components/ai-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/editor/components/template-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/features/editor/components/remove-bg-sidebar */ \"(app-pages-browser)/./src/features/editor/components/remove-bg-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/editor/components/settings-sidebar */ \"(app-pages-browser)/./src/features/editor/components/settings-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/editor/components/template-config-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Editor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Editor = (param)=>{\n    let { initialData, initialActiveTool = \"select\" } = param;\n    _s();\n    const { mutate } = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject)(initialData.id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()((values)=>{\n        mutate(values);\n    }, 3000 // Increased from 500ms to 3 seconds\n    ), [\n        mutate\n    ]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"select\");\n    const onClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        if (_features_editor_types__WEBPACK_IMPORTED_MODULE_6__.selectionDependentTools.includes(activeTool)) {\n            setActiveTool(\"select\");\n        }\n    }, [\n        activeTool\n    ]);\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor)({\n        defaultState: initialData.json,\n        defaultWidth: initialData.width,\n        defaultHeight: initialData.height,\n        clearSelectionCallback: onClearSelection,\n        saveCallback: debouncedSave\n    });\n    const handleManualSave = ()=>{\n        if (editor === null || editor === void 0 ? void 0 : editor.canvas) {\n            const workspace = editor.canvas.getObjects().find((object)=>object.name === \"clip\");\n            const height = (workspace === null || workspace === void 0 ? void 0 : workspace.height) || initialData.height;\n            const width = (workspace === null || workspace === void 0 ? void 0 : workspace.width) || initialData.width;\n            const json = JSON.stringify(editor.canvas.toJSON());\n            mutate({\n                json,\n                height,\n                width\n            });\n        }\n    };\n    // Generate thumbnails automatically when the canvas changes\n    const { debouncedGenerateThumbnail } = (0,_features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator)({\n        editor,\n        projectId: initialData.id\n    });\n    // Trigger thumbnail generation when editor changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (editor) {\n            debouncedGenerateThumbnail();\n        }\n    }, [\n        editor,\n        debouncedGenerateThumbnail\n    ]);\n    const onChangeActiveTool = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((tool)=>{\n        if (tool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.enableDrawingMode();\n        }\n        if (activeTool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.disableDrawingMode();\n        }\n        if (tool === activeTool) {\n            return setActiveTool(\"select\");\n        }\n        setActiveTool(tool);\n    }, [\n        activeTool,\n        editor\n    ]);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const canvas = new fabric__WEBPACK_IMPORTED_MODULE_1__.fabric.Canvas(canvasRef.current, {\n            controlsAboveOverlay: true,\n            preserveObjectStacking: true\n        });\n        init({\n            initialCanvas: canvas,\n            initialContainer: containerRef.current\n        });\n        return ()=>{\n            canvas.dispose();\n        };\n    }, [\n        init\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__.Navbar, {\n                id: initialData.id,\n                editor: editor,\n                activeTool: activeTool,\n                onChangeActiveTool: onChangeActiveTool,\n                onSave: handleManualSave,\n                initialData: {\n                    name: initialData.name,\n                    isCustomizable: initialData.isCustomizable\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute h-[calc(100%-68px)] w-full top-[68px] flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__.ShapeSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__.FillColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__.StrokeColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__.StrokeWidthSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__.OpacitySidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__.TextSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__.FontSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__.ImageSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__.TemplateSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__.FilterSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__.AiSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__.RemoveBgSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__.DrawSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__.SettingsSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__.TemplateConfigSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool,\n                        projectId: initialData.id,\n                        initialData: {\n                            isCustomizable: initialData.isCustomizable,\n                            editableLayers: initialData.editableLayers\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-muted flex-1 overflow-auto relative flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__.Toolbar, {\n                                editor: editor,\n                                activeTool: activeTool,\n                                onChangeActiveTool: onChangeActiveTool\n                            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                                ref: containerRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                    ref: canvasRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__.Footer, {\n                                editor: editor\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Editor, \"74z/qZAt4Wo1k+syyS2i9LDaVik=\", false, function() {\n    return [\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject,\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor,\n        _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator\n    ];\n});\n_c = Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx\n"));

/***/ })

});