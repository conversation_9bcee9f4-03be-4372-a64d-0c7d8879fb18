"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx":
/*!********************************************************************!*\
  !*** ./src/features/editor/components/template-config-sidebar.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateConfigSidebar: function() { return /* binding */ TemplateConfigSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/projects/api/use-update-template-config */ \"(app-pages-browser)/./src/features/projects/api/use-update-template-config.ts\");\n/* __next_internal_client_entry_do_not_use__ TemplateConfigSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst TemplateConfigSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool, projectId, initialData } = param;\n    _s();\n    const [editableLayers, setEditableLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCustomizable, setIsCustomizable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasLoadedInitialData, setHasLoadedInitialData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateTemplateConfig = (0,_features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig)(projectId || \"\");\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    const canvas = editor === null || editor === void 0 ? void 0 : editor.canvas;\n    const selectedObjects = (editor === null || editor === void 0 ? void 0 : editor.selectedObjects) || [];\n    // Load existing template configuration from database (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData && !hasLoadedInitialData) {\n            setIsCustomizable(initialData.isCustomizable || false);\n            if (initialData.editableLayers) {\n                try {\n                    const parsedLayers = JSON.parse(initialData.editableLayers);\n                    setEditableLayers(parsedLayers);\n                } catch (error) {\n                    console.error(\"Failed to parse editable layers:\", error);\n                }\n            }\n            setHasLoadedInitialData(true);\n        }\n    }, [\n        initialData,\n        hasLoadedInitialData\n    ]);\n    // Apply editable properties to canvas objects when canvas is ready and layers are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (canvas && editableLayers.length > 0 && hasLoadedInitialData) {\n            editableLayers.forEach((layer)=>{\n                const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n                if (canvasObject) {\n                    canvasObject.isEditable = true;\n                    canvasObject.editableType = layer.type;\n                    canvasObject.editableName = layer.name;\n                    canvasObject.editablePlaceholder = layer.placeholder;\n                    canvasObject.editableConstraints = layer.constraints;\n                }\n            });\n            canvas.renderAll();\n        }\n    }, [\n        canvas,\n        editableLayers,\n        hasLoadedInitialData\n    ]);\n    // Load existing editable layers from canvas objects (fallback)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvas || editableLayers.length > 0) return;\n        const layers = [];\n        canvas.getObjects().forEach((obj)=>{\n            const editableObj = obj;\n            if (editableObj.isEditable) {\n                layers.push({\n                    id: editableObj.id || \"\",\n                    type: editableObj.editableType || \"text\",\n                    name: editableObj.editableName || \"Unnamed Layer\",\n                    originalValue: editableObj.type === \"textbox\" ? editableObj.text : \"\",\n                    placeholder: editableObj.editablePlaceholder,\n                    constraints: editableObj.editableConstraints\n                });\n            }\n        });\n        if (layers.length > 0) {\n            setEditableLayers(layers);\n            setIsCustomizable(true);\n        }\n    }, [\n        canvas,\n        editableLayers.length\n    ]);\n    const makeLayerEditable = (type)=>{\n        if (!canvas || selectedObjects.length === 0) return;\n        const selectedObject = selectedObjects[0];\n        // Validate object type\n        if (type === \"text\" && selectedObject.type !== \"textbox\") {\n            alert(\"Please select a text object to make it editable\");\n            return;\n        }\n        if (type === \"image\" && ![\n            \"image\",\n            \"rect\",\n            \"circle\"\n        ].includes(selectedObject.type || \"\")) {\n            alert(\"Please select an image or shape to make it editable\");\n            return;\n        }\n        // Mark object as editable\n        selectedObject.isEditable = true;\n        selectedObject.editableType = type;\n        selectedObject.editableName = \"\".concat(type === \"text\" ? \"Text\" : \"Image\", \" \").concat(editableLayers.length + 1);\n        if (type === \"text\") {\n            selectedObject.editablePlaceholder = \"Enter your text here...\";\n        }\n        // Add to editable layers list\n        const newLayer = {\n            id: selectedObject.id || \"layer_\".concat(Date.now()),\n            type,\n            name: selectedObject.editableName,\n            originalValue: type === \"text\" ? selectedObject.text : \"\",\n            placeholder: selectedObject.editablePlaceholder,\n            constraints: {\n                maxLength: type === \"text\" ? 100 : undefined,\n                allowedFormats: type === \"image\" ? [\n                    \"jpg\",\n                    \"jpeg\",\n                    \"png\",\n                    \"gif\"\n                ] : undefined,\n                maxFileSize: type === \"image\" ? 5 * 1024 * 1024 : undefined\n            }\n        };\n        // Assign ID if not exists\n        if (!selectedObject.id) {\n            selectedObject.id = newLayer.id;\n        }\n        setEditableLayers([\n            ...editableLayers,\n            newLayer\n        ]);\n        // Don't automatically enable the toggle - let user control it manually\n        canvas.renderAll();\n    };\n    const removeEditableLayer = (layerId)=>{\n        // Find and update the canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.isEditable = false;\n            delete canvasObject.editableType;\n            delete canvasObject.editableName;\n            delete canvasObject.editablePlaceholder;\n            delete canvasObject.editableConstraints;\n        }\n        // Remove from layers list\n        const updatedLayers = editableLayers.filter((layer)=>layer.id !== layerId);\n        setEditableLayers(updatedLayers);\n        // Don't automatically disable the toggle - let user control it manually\n        canvas === null || canvas === void 0 ? void 0 : canvas.renderAll();\n    };\n    const updateLayerName = (layerId, name)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                name\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editableName = name;\n        }\n    };\n    const updateLayerPlaceholder = (layerId, placeholder)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                placeholder\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editablePlaceholder = placeholder;\n        }\n    };\n    const saveTemplateConfig = ()=>{\n        if (!projectId) {\n            alert(\"Project ID is required to save template configuration\");\n            return;\n        }\n        updateTemplateConfig.mutate({\n            isCustomizable,\n            editableLayers: JSON.stringify(editableLayers)\n        }, {\n            onSuccess: ()=>{\n            // Don't close sidebar automatically - let user see the success state\n            // They can close it manually if needed\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"template-config\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"Template Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: \"Configure which elements users can customize\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Template Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isCustomizable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                            variant: \"default\",\n                                            className: \"ml-2\",\n                                            children: \"Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"customizable\",\n                                                className: \"text-sm\",\n                                                children: \"Make Customizable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                                id: \"customizable\",\n                                                checked: isCustomizable,\n                                                onCheckedChange: setIsCustomizable\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: isCustomizable ? \"Template is customizable with \".concat(editableLayers.length, \" editable element\").concat(editableLayers.length !== 1 ? \"s\" : \"\") : \"Allow public users to customize this template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: \"Add Editable Elements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"text\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Text Editable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"image\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Image Replaceable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    selectedObjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Select an element on the canvas first\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, undefined),\n                    editableLayers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: [\n                                        \"Editable Elements (\",\n                                        editableLayers.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-3\",\n                                children: editableLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                        variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                        children: [\n                                                            layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            layer.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeEditableLayer(layer.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Display Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.name,\n                                                                onChange: (e)=>updateLayerName(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"e.g., Your Name, Profile Photo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    layer.type === \"text\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Placeholder Text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.placeholder || \"\",\n                                                                onChange: (e)=>updateLayerPlaceholder(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"Enter placeholder text...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, layer.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: saveTemplateConfig,\n                    className: \"w-full\",\n                    disabled: updateTemplateConfig.isPending,\n                    variant: updateTemplateConfig.isSuccess ? \"default\" : \"default\",\n                    children: updateTemplateConfig.isPending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 15\n                            }, undefined),\n                            \"Saving...\"\n                        ]\n                    }, void 0, true) : updateTemplateConfig.isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 15\n                            }, undefined),\n                            \"Saved Successfully!\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 15\n                            }, undefined),\n                            \"Save Template Config\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 383,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateConfigSidebar, \"jMWZ7D+oEsDeK2D8A3TiN5W25SE=\", false, function() {\n    return [\n        _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig\n    ];\n});\n_c = TemplateConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TemplateConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvY29tcG9uZW50cy90ZW1wbGF0ZS1jb25maWctc2lkZWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQVl0QjtBQUUwQjtBQUNGO0FBQ0E7QUFDRTtBQUVnQztBQUNsQztBQUVXO0FBQ3hCO0FBSTREO0FBYXRGLE1BQU1xQix3QkFBd0I7UUFBQyxFQUNwQ0MsTUFBTSxFQUNOQyxVQUFVLEVBQ1ZDLGtCQUFrQixFQUNsQkMsU0FBUyxFQUNUQyxXQUFXLEVBQ2dCOztJQUMzQixNQUFNLENBQUNDLGdCQUFnQkMsa0JBQWtCLEdBQUc1QiwrQ0FBUUEsQ0FBa0IsRUFBRTtJQUN4RSxNQUFNLENBQUM2QixnQkFBZ0JDLGtCQUFrQixHQUFHOUIsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDK0Isc0JBQXNCQyx3QkFBd0IsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU1pQyx1QkFBdUJiLDJHQUF1QkEsQ0FBQ0ssYUFBYTtJQUVsRSxNQUFNUyxVQUFVO1FBQ2RWLG1CQUFtQjtJQUNyQjtJQUVBLE1BQU1XLFNBQVNiLG1CQUFBQSw2QkFBQUEsT0FBUWEsTUFBTTtJQUM3QixNQUFNQyxrQkFBa0JkLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUWMsZUFBZSxLQUFJLEVBQUU7SUFFckQsaUVBQWlFO0lBQ2pFbkMsZ0RBQVNBLENBQUM7UUFDUixJQUFJeUIsZUFBZSxDQUFDSyxzQkFBc0I7WUFDeENELGtCQUFrQkosWUFBWUcsY0FBYyxJQUFJO1lBRWhELElBQUlILFlBQVlDLGNBQWMsRUFBRTtnQkFDOUIsSUFBSTtvQkFDRixNQUFNVSxlQUFnQ0MsS0FBS0MsS0FBSyxDQUFDYixZQUFZQyxjQUFjO29CQUMzRUMsa0JBQWtCUztnQkFDcEIsRUFBRSxPQUFPRyxPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsb0NBQW9DQTtnQkFDcEQ7WUFDRjtZQUVBUix3QkFBd0I7UUFDMUI7SUFDRixHQUFHO1FBQUNOO1FBQWFLO0tBQXFCO0lBRXRDLHlGQUF5RjtJQUN6RjlCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWtDLFVBQVVSLGVBQWVlLE1BQU0sR0FBRyxLQUFLWCxzQkFBc0I7WUFDL0RKLGVBQWVnQixPQUFPLENBQUMsQ0FBQ0M7Z0JBQ3RCLE1BQU1DLGVBQWVWLE9BQU9XLFVBQVUsR0FBR0MsSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxFQUFFLEtBQUtMLE1BQU1LLEVBQUU7Z0JBQ3hFLElBQUlKLGNBQWM7b0JBQ2hCQSxhQUFhSyxVQUFVLEdBQUc7b0JBQzFCTCxhQUFhTSxZQUFZLEdBQUdQLE1BQU1RLElBQUk7b0JBQ3RDUCxhQUFhUSxZQUFZLEdBQUdULE1BQU1VLElBQUk7b0JBQ3RDVCxhQUFhVSxtQkFBbUIsR0FBR1gsTUFBTVksV0FBVztvQkFDcERYLGFBQWFZLG1CQUFtQixHQUFHYixNQUFNYyxXQUFXO2dCQUN0RDtZQUNGO1lBQ0F2QixPQUFPd0IsU0FBUztRQUNsQjtJQUNGLEdBQUc7UUFBQ3hCO1FBQVFSO1FBQWdCSTtLQUFxQjtJQUVqRCwrREFBK0Q7SUFDL0Q5QixnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ2tDLFVBQVVSLGVBQWVlLE1BQU0sR0FBRyxHQUFHO1FBRTFDLE1BQU1rQixTQUEwQixFQUFFO1FBQ2xDekIsT0FBT1csVUFBVSxHQUFHSCxPQUFPLENBQUMsQ0FBQ0s7WUFDM0IsTUFBTWEsY0FBY2I7WUFDcEIsSUFBSWEsWUFBWVgsVUFBVSxFQUFFO2dCQUMxQlUsT0FBT0UsSUFBSSxDQUFDO29CQUNWYixJQUFJWSxZQUFZWixFQUFFLElBQUk7b0JBQ3RCRyxNQUFNUyxZQUFZVixZQUFZLElBQUk7b0JBQ2xDRyxNQUFNTyxZQUFZUixZQUFZLElBQUk7b0JBQ2xDVSxlQUFlRixZQUFZVCxJQUFJLEtBQUssWUFBWSxZQUFnQ1ksSUFBSSxHQUFHO29CQUN2RlIsYUFBYUssWUFBWU4sbUJBQW1CO29CQUM1Q0csYUFBYUcsWUFBWUosbUJBQW1CO2dCQUM5QztZQUNGO1FBQ0Y7UUFFQSxJQUFJRyxPQUFPbEIsTUFBTSxHQUFHLEdBQUc7WUFDckJkLGtCQUFrQmdDO1lBQ2xCOUIsa0JBQWtCO1FBQ3BCO0lBQ0YsR0FBRztRQUFDSztRQUFRUixlQUFlZSxNQUFNO0tBQUM7SUFFbEMsTUFBTXVCLG9CQUFvQixDQUFDYjtRQUN6QixJQUFJLENBQUNqQixVQUFVQyxnQkFBZ0JNLE1BQU0sS0FBSyxHQUFHO1FBRTdDLE1BQU13QixpQkFBaUI5QixlQUFlLENBQUMsRUFBRTtRQUV6Qyx1QkFBdUI7UUFDdkIsSUFBSWdCLFNBQVMsVUFBVWMsZUFBZWQsSUFBSSxLQUFLLFdBQVc7WUFDeERlLE1BQU07WUFDTjtRQUNGO1FBRUEsSUFBSWYsU0FBUyxXQUFXLENBQUM7WUFBQztZQUFTO1lBQVE7U0FBUyxDQUFDZ0IsUUFBUSxDQUFDRixlQUFlZCxJQUFJLElBQUksS0FBSztZQUN4RmUsTUFBTTtZQUNOO1FBQ0Y7UUFFQSwwQkFBMEI7UUFDMUJELGVBQWVoQixVQUFVLEdBQUc7UUFDNUJnQixlQUFlZixZQUFZLEdBQUdDO1FBQzlCYyxlQUFlYixZQUFZLEdBQUcsR0FBeUMxQixPQUF0Q3lCLFNBQVMsU0FBUyxTQUFTLFNBQVEsS0FBNkIsT0FBMUJ6QixlQUFlZSxNQUFNLEdBQUc7UUFFL0YsSUFBSVUsU0FBUyxRQUFRO1lBQ25CYyxlQUFlWCxtQkFBbUIsR0FBRztRQUN2QztRQUVBLDhCQUE4QjtRQUM5QixNQUFNYyxXQUEwQjtZQUM5QnBCLElBQUlpQixlQUFlakIsRUFBRSxJQUFJLFNBQW9CLE9BQVhxQixLQUFLQyxHQUFHO1lBQzFDbkI7WUFDQUUsTUFBTVksZUFBZWIsWUFBWTtZQUNqQ1UsZUFBZVgsU0FBUyxTQUFTLGVBQW1DWSxJQUFJLEdBQUc7WUFDM0VSLGFBQWFVLGVBQWVYLG1CQUFtQjtZQUMvQ0csYUFBYTtnQkFDWGMsV0FBV3BCLFNBQVMsU0FBUyxNQUFNcUI7Z0JBQ25DQyxnQkFBZ0J0QixTQUFTLFVBQVU7b0JBQUM7b0JBQU87b0JBQVE7b0JBQU87aUJBQU0sR0FBR3FCO2dCQUNuRUUsYUFBYXZCLFNBQVMsVUFBVSxJQUFJLE9BQU8sT0FBT3FCO1lBQ3BEO1FBQ0Y7UUFFQSwwQkFBMEI7UUFDMUIsSUFBSSxDQUFDUCxlQUFlakIsRUFBRSxFQUFFO1lBQ3RCaUIsZUFBZWpCLEVBQUUsR0FBR29CLFNBQVNwQixFQUFFO1FBQ2pDO1FBRUFyQixrQkFBa0I7ZUFBSUQ7WUFBZ0IwQztTQUFTO1FBQy9DLHVFQUF1RTtRQUN2RWxDLE9BQU93QixTQUFTO0lBQ2xCO0lBRUEsTUFBTWlCLHNCQUFzQixDQUFDQztRQUMzQixvQ0FBb0M7UUFDcEMsTUFBTWhDLGVBQWVWLG1CQUFBQSw2QkFBQUEsT0FBUVcsVUFBVSxHQUFHQyxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLEVBQUUsS0FBSzRCO1FBQ2pFLElBQUloQyxjQUFjO1lBQ2hCQSxhQUFhSyxVQUFVLEdBQUc7WUFDMUIsT0FBT0wsYUFBYU0sWUFBWTtZQUNoQyxPQUFPTixhQUFhUSxZQUFZO1lBQ2hDLE9BQU9SLGFBQWFVLG1CQUFtQjtZQUN2QyxPQUFPVixhQUFhWSxtQkFBbUI7UUFDekM7UUFFQSwwQkFBMEI7UUFDMUIsTUFBTXFCLGdCQUFnQm5ELGVBQWVvRCxNQUFNLENBQUNuQyxDQUFBQSxRQUFTQSxNQUFNSyxFQUFFLEtBQUs0QjtRQUNsRWpELGtCQUFrQmtEO1FBQ2xCLHdFQUF3RTtRQUN4RTNDLG1CQUFBQSw2QkFBQUEsT0FBUXdCLFNBQVM7SUFDbkI7SUFFQSxNQUFNcUIsa0JBQWtCLENBQUNILFNBQWlCdkI7UUFDeEMsTUFBTXdCLGdCQUFnQm5ELGVBQWVzRCxHQUFHLENBQUNyQyxDQUFBQSxRQUN2Q0EsTUFBTUssRUFBRSxLQUFLNEIsVUFBVTtnQkFBRSxHQUFHakMsS0FBSztnQkFBRVU7WUFBSyxJQUFJVjtRQUU5Q2hCLGtCQUFrQmtEO1FBRWxCLHVCQUF1QjtRQUN2QixNQUFNakMsZUFBZVYsbUJBQUFBLDZCQUFBQSxPQUFRVyxVQUFVLEdBQUdDLElBQUksQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUMsRUFBRSxLQUFLNEI7UUFDakUsSUFBSWhDLGNBQWM7WUFDaEJBLGFBQWFRLFlBQVksR0FBR0M7UUFDOUI7SUFDRjtJQUVBLE1BQU00Qix5QkFBeUIsQ0FBQ0wsU0FBaUJyQjtRQUMvQyxNQUFNc0IsZ0JBQWdCbkQsZUFBZXNELEdBQUcsQ0FBQ3JDLENBQUFBLFFBQ3ZDQSxNQUFNSyxFQUFFLEtBQUs0QixVQUFVO2dCQUFFLEdBQUdqQyxLQUFLO2dCQUFFWTtZQUFZLElBQUlaO1FBRXJEaEIsa0JBQWtCa0Q7UUFFbEIsdUJBQXVCO1FBQ3ZCLE1BQU1qQyxlQUFlVixtQkFBQUEsNkJBQUFBLE9BQVFXLFVBQVUsR0FBR0MsSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxFQUFFLEtBQUs0QjtRQUNqRSxJQUFJaEMsY0FBYztZQUNoQkEsYUFBYVUsbUJBQW1CLEdBQUdDO1FBQ3JDO0lBQ0Y7SUFFQSxNQUFNMkIscUJBQXFCO1FBQ3pCLElBQUksQ0FBQzFELFdBQVc7WUFDZDBDLE1BQU07WUFDTjtRQUNGO1FBRUFsQyxxQkFBcUJtRCxNQUFNLENBQUM7WUFDMUJ2RDtZQUNBRixnQkFBZ0JXLEtBQUsrQyxTQUFTLENBQUMxRDtRQUNqQyxHQUFHO1lBQ0QyRCxXQUFXO1lBQ1QscUVBQXFFO1lBQ3JFLHVDQUF1QztZQUN6QztRQUNGO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFDQ0MsV0FBV3JFLDhDQUFFQSxDQUNYLG9FQUNBSSxlQUFlLG9CQUFvQixZQUFZOzswQkFJakQsOERBQUNnRTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDdEYsd0hBQVFBO3dDQUFDc0YsV0FBVTs7Ozs7O2tEQUNwQiw4REFBQ0M7d0NBQUdELFdBQVU7a0RBQThCOzs7Ozs7Ozs7Ozs7MENBRTlDLDhEQUFDL0UseURBQU1BO2dDQUFDaUYsU0FBUTtnQ0FBUUMsTUFBSztnQ0FBS0MsU0FBUzFEOzBDQUFTOzs7Ozs7Ozs7Ozs7a0NBSXRELDhEQUFDMkQ7d0JBQUVMLFdBQVU7a0NBQTZCOzs7Ozs7Ozs7Ozs7MEJBSzVDLDhEQUFDdEUsa0VBQVVBO2dCQUFDc0UsV0FBVTs7a0NBRXBCLDhEQUFDM0UscURBQUlBO3dCQUFDMkUsV0FBVTs7MENBQ2QsOERBQUN6RSwyREFBVUE7Z0NBQUN5RSxXQUFVOzBDQUNwQiw0RUFBQ3hFLDBEQUFTQTtvQ0FBQ3dFLFdBQVU7O3NEQUNuQiw4REFBQ2pGLHdIQUFLQTs0Q0FBQ2lGLFdBQVU7Ozs7OztzREFDakIsOERBQUNNO3NEQUFLOzs7Ozs7d0NBQ0xqRSxnQ0FDQyw4REFBQ1osdURBQUtBOzRDQUFDeUUsU0FBUTs0Q0FBVUYsV0FBVTtzREFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTWhELDhEQUFDMUUsNERBQVdBOztrREFDViw4REFBQ3lFO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzdFLHVEQUFLQTtnREFBQ29GLFNBQVE7Z0RBQWVQLFdBQVU7MERBQVU7Ozs7OzswREFHbEQsOERBQUM1RSx5REFBTUE7Z0RBQ0xxQyxJQUFHO2dEQUNIK0MsU0FBU25FO2dEQUNUb0UsaUJBQWlCbkU7Ozs7Ozs7Ozs7OztrREFHckIsOERBQUMrRDt3Q0FBRUwsV0FBVTtrREFDVjNELGlCQUNHLGlDQUEwRUYsT0FBekNBLGVBQWVlLE1BQU0sRUFBQyxxQkFBMEQsT0FBdkNmLGVBQWVlLE1BQU0sS0FBSyxJQUFJLE1BQU0sTUFDOUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPViw4REFBQzdCLHFEQUFJQTt3QkFBQzJFLFdBQVU7OzBDQUNkLDhEQUFDekUsMkRBQVVBO2dDQUFDeUUsV0FBVTswQ0FDcEIsNEVBQUN4RSwwREFBU0E7b0NBQUN3RSxXQUFVOzhDQUFVOzs7Ozs7Ozs7OzswQ0FFakMsOERBQUMxRSw0REFBV0E7Z0NBQUMwRSxXQUFVOztrREFDckIsOERBQUMvRSx5REFBTUE7d0NBQ0xpRixTQUFRO3dDQUNSQyxNQUFLO3dDQUNMSCxXQUFVO3dDQUNWSSxTQUFTLElBQU0zQixrQkFBa0I7d0NBQ2pDaUMsVUFBVTlELGdCQUFnQk0sTUFBTSxLQUFLOzswREFFckMsOERBQUN2Qyx3SEFBSUE7Z0RBQUNxRixXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7O2tEQUduQyw4REFBQy9FLHlEQUFNQTt3Q0FDTGlGLFNBQVE7d0NBQ1JDLE1BQUs7d0NBQ0xILFdBQVU7d0NBQ1ZJLFNBQVMsSUFBTTNCLGtCQUFrQjt3Q0FDakNpQyxVQUFVOUQsZ0JBQWdCTSxNQUFNLEtBQUs7OzBEQUVyQyw4REFBQ3RDLHdIQUFLQTtnREFBQ29GLFdBQVU7Ozs7Ozs0Q0FBaUI7Ozs7Ozs7b0NBR25DcEQsZ0JBQWdCTSxNQUFNLEtBQUssbUJBQzFCLDhEQUFDbUQ7d0NBQUVMLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBUTFDN0QsZUFBZWUsTUFBTSxHQUFHLG1CQUN2Qiw4REFBQzdCLHFEQUFJQTs7MENBQ0gsOERBQUNFLDJEQUFVQTtnQ0FBQ3lFLFdBQVU7MENBQ3BCLDRFQUFDeEUsMERBQVNBO29DQUFDd0UsV0FBVTs7d0NBQVU7d0NBQ1Q3RCxlQUFlZSxNQUFNO3dDQUFDOzs7Ozs7Ozs7Ozs7MENBRzlDLDhEQUFDNUIsNERBQVdBO2dDQUFDMEUsV0FBVTswQ0FDcEI3RCxlQUFlc0QsR0FBRyxDQUFDLENBQUNyQyxzQkFDbkIsOERBQUMyQzt3Q0FBbUJDLFdBQVU7OzBEQUM1Qiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDdkUsdURBQUtBO3dEQUFDeUUsU0FBUzlDLE1BQU1RLElBQUksS0FBSyxTQUFTLFlBQVk7OzREQUNqRFIsTUFBTVEsSUFBSSxLQUFLLHVCQUNkLDhEQUFDakQsd0hBQUlBO2dFQUFDcUYsV0FBVTs7Ozs7MEZBRWhCLDhEQUFDcEYsd0hBQUtBO2dFQUFDb0YsV0FBVTs7Ozs7OzREQUVsQjVDLE1BQU1RLElBQUk7Ozs7Ozs7a0VBRWIsOERBQUMzQyx5REFBTUE7d0RBQ0xpRixTQUFRO3dEQUNSQyxNQUFLO3dEQUNMQyxTQUFTLElBQU1oQixvQkFBb0JoQyxNQUFNSyxFQUFFO2tFQUUzQyw0RUFBQzVDLHdIQUFNQTs0REFBQ21GLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUl0Qiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDs7MEVBQ0MsOERBQUM1RSx1REFBS0E7Z0VBQUM2RSxXQUFVOzBFQUFVOzs7Ozs7MEVBQzNCLDhEQUFDOUUsdURBQUtBO2dFQUNKeUYsT0FBT3ZELE1BQU1VLElBQUk7Z0VBQ2pCOEMsVUFBVSxDQUFDQyxJQUFNckIsZ0JBQWdCcEMsTUFBTUssRUFBRSxFQUFFb0QsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dFQUN6RFgsV0FBVTtnRUFDVmhDLGFBQVk7Ozs7Ozs7Ozs7OztvREFJZlosTUFBTVEsSUFBSSxLQUFLLHdCQUNkLDhEQUFDbUM7OzBFQUNDLDhEQUFDNUUsdURBQUtBO2dFQUFDNkUsV0FBVTswRUFBVTs7Ozs7OzBFQUMzQiw4REFBQzlFLHVEQUFLQTtnRUFDSnlGLE9BQU92RCxNQUFNWSxXQUFXLElBQUk7Z0VBQzVCNEMsVUFBVSxDQUFDQyxJQUFNbkIsdUJBQXVCdEMsTUFBTUssRUFBRSxFQUFFb0QsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dFQUNoRVgsV0FBVTtnRUFDVmhDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBckNaWixNQUFNSyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQWtENUIsOERBQUNzQztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQy9FLHlEQUFNQTtvQkFDTG1GLFNBQVNUO29CQUNUSyxXQUFVO29CQUNWVSxVQUFVakUscUJBQXFCc0UsU0FBUztvQkFDeENiLFNBQVN6RCxxQkFBcUJ1RSxTQUFTLEdBQUcsWUFBWTs4QkFFckR2RSxxQkFBcUJzRSxTQUFTLGlCQUM3Qjs7MENBQ0UsOERBQUNqRyx3SEFBSUE7Z0NBQUNrRixXQUFVOzs7Ozs7NEJBQThCOzt1Q0FHOUN2RCxxQkFBcUJ1RSxTQUFTLGlCQUNoQzs7MENBQ0UsOERBQUNoRyx3SEFBS0E7Z0NBQUNnRixXQUFVOzs7Ozs7NEJBQWlCOztxREFJcEM7OzBDQUNFLDhEQUFDbEYsd0hBQUlBO2dDQUFDa0YsV0FBVTs7Ozs7OzRCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVEvQyxFQUFFO0dBL1dXbkU7O1FBVWtCRCx1R0FBdUJBOzs7S0FWekNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvY29tcG9uZW50cy90ZW1wbGF0ZS1jb25maWctc2lkZWJhci50c3g/ZDZmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHtcbiAgU2V0dGluZ3MsXG4gIFR5cGUsXG4gIEltYWdlLFxuICBQbHVzLFxuICBUcmFzaDIsXG4gIEV5ZSxcbiAgRXllT2ZmLFxuICBTYXZlLFxuICBHbG9iZSxcbiAgQ2hlY2tcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiO1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCI7XG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvbGFiZWxcIjtcbmltcG9ydCB7IFN3aXRjaCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc3dpdGNoXCI7XG5pbXBvcnQgeyBUZXh0YXJlYSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGV4dGFyZWFcIjtcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIjtcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiO1xuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zZXBhcmF0b3JcIjtcbmltcG9ydCB7IFNjcm9sbEFyZWEgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3Njcm9sbC1hcmVhXCI7XG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xuXG5pbXBvcnQgeyBBY3RpdmVUb29sLCBFZGl0b3IgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvdHlwZXNcIjtcbmltcG9ydCB7IEVkaXRhYmxlTGF5ZXIsIEVkaXRhYmxlRmFicmljT2JqZWN0IH0gZnJvbSBcIkAvdHlwZXMvdGVtcGxhdGVcIjtcbmltcG9ydCB7IHVzZVVwZGF0ZVRlbXBsYXRlQ29uZmlnIH0gZnJvbSBcIkAvZmVhdHVyZXMvcHJvamVjdHMvYXBpL3VzZS11cGRhdGUtdGVtcGxhdGUtY29uZmlnXCI7XG5cbmludGVyZmFjZSBUZW1wbGF0ZUNvbmZpZ1NpZGViYXJQcm9wcyB7XG4gIGVkaXRvcjogRWRpdG9yIHwgdW5kZWZpbmVkO1xuICBhY3RpdmVUb29sOiBBY3RpdmVUb29sO1xuICBvbkNoYW5nZUFjdGl2ZVRvb2w6ICh0b29sOiBBY3RpdmVUb29sKSA9PiB2b2lkO1xuICBwcm9qZWN0SWQ/OiBzdHJpbmc7XG4gIGluaXRpYWxEYXRhPzoge1xuICAgIGlzQ3VzdG9taXphYmxlPzogYm9vbGVhbjtcbiAgICBlZGl0YWJsZUxheWVycz86IHN0cmluZyB8IG51bGw7XG4gIH07XG59XG5cbmV4cG9ydCBjb25zdCBUZW1wbGF0ZUNvbmZpZ1NpZGViYXIgPSAoe1xuICBlZGl0b3IsXG4gIGFjdGl2ZVRvb2wsXG4gIG9uQ2hhbmdlQWN0aXZlVG9vbCxcbiAgcHJvamVjdElkLFxuICBpbml0aWFsRGF0YVxufTogVGVtcGxhdGVDb25maWdTaWRlYmFyUHJvcHMpID0+IHtcbiAgY29uc3QgW2VkaXRhYmxlTGF5ZXJzLCBzZXRFZGl0YWJsZUxheWVyc10gPSB1c2VTdGF0ZTxFZGl0YWJsZUxheWVyW10+KFtdKTtcbiAgY29uc3QgW2lzQ3VzdG9taXphYmxlLCBzZXRJc0N1c3RvbWl6YWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtoYXNMb2FkZWRJbml0aWFsRGF0YSwgc2V0SGFzTG9hZGVkSW5pdGlhbERhdGFdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCB1cGRhdGVUZW1wbGF0ZUNvbmZpZyA9IHVzZVVwZGF0ZVRlbXBsYXRlQ29uZmlnKHByb2plY3RJZCB8fCBcIlwiKTtcblxuICBjb25zdCBvbkNsb3NlID0gKCkgPT4ge1xuICAgIG9uQ2hhbmdlQWN0aXZlVG9vbChcInNlbGVjdFwiKTtcbiAgfTtcblxuICBjb25zdCBjYW52YXMgPSBlZGl0b3I/LmNhbnZhcztcbiAgY29uc3Qgc2VsZWN0ZWRPYmplY3RzID0gZWRpdG9yPy5zZWxlY3RlZE9iamVjdHMgfHwgW107XG5cbiAgLy8gTG9hZCBleGlzdGluZyB0ZW1wbGF0ZSBjb25maWd1cmF0aW9uIGZyb20gZGF0YWJhc2UgKG9ubHkgb25jZSlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaW5pdGlhbERhdGEgJiYgIWhhc0xvYWRlZEluaXRpYWxEYXRhKSB7XG4gICAgICBzZXRJc0N1c3RvbWl6YWJsZShpbml0aWFsRGF0YS5pc0N1c3RvbWl6YWJsZSB8fCBmYWxzZSk7XG5cbiAgICAgIGlmIChpbml0aWFsRGF0YS5lZGl0YWJsZUxheWVycykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHBhcnNlZExheWVyczogRWRpdGFibGVMYXllcltdID0gSlNPTi5wYXJzZShpbml0aWFsRGF0YS5lZGl0YWJsZUxheWVycyk7XG4gICAgICAgICAgc2V0RWRpdGFibGVMYXllcnMocGFyc2VkTGF5ZXJzKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gcGFyc2UgZWRpdGFibGUgbGF5ZXJzOicsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBzZXRIYXNMb2FkZWRJbml0aWFsRGF0YSh0cnVlKTtcbiAgICB9XG4gIH0sIFtpbml0aWFsRGF0YSwgaGFzTG9hZGVkSW5pdGlhbERhdGFdKTtcblxuICAvLyBBcHBseSBlZGl0YWJsZSBwcm9wZXJ0aWVzIHRvIGNhbnZhcyBvYmplY3RzIHdoZW4gY2FudmFzIGlzIHJlYWR5IGFuZCBsYXllcnMgYXJlIGxvYWRlZFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChjYW52YXMgJiYgZWRpdGFibGVMYXllcnMubGVuZ3RoID4gMCAmJiBoYXNMb2FkZWRJbml0aWFsRGF0YSkge1xuICAgICAgZWRpdGFibGVMYXllcnMuZm9yRWFjaCgobGF5ZXIpID0+IHtcbiAgICAgICAgY29uc3QgY2FudmFzT2JqZWN0ID0gY2FudmFzLmdldE9iamVjdHMoKS5maW5kKG9iaiA9PiBvYmouaWQgPT09IGxheWVyLmlkKSBhcyBFZGl0YWJsZUZhYnJpY09iamVjdDtcbiAgICAgICAgaWYgKGNhbnZhc09iamVjdCkge1xuICAgICAgICAgIGNhbnZhc09iamVjdC5pc0VkaXRhYmxlID0gdHJ1ZTtcbiAgICAgICAgICBjYW52YXNPYmplY3QuZWRpdGFibGVUeXBlID0gbGF5ZXIudHlwZTtcbiAgICAgICAgICBjYW52YXNPYmplY3QuZWRpdGFibGVOYW1lID0gbGF5ZXIubmFtZTtcbiAgICAgICAgICBjYW52YXNPYmplY3QuZWRpdGFibGVQbGFjZWhvbGRlciA9IGxheWVyLnBsYWNlaG9sZGVyO1xuICAgICAgICAgIGNhbnZhc09iamVjdC5lZGl0YWJsZUNvbnN0cmFpbnRzID0gbGF5ZXIuY29uc3RyYWludHM7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xuICAgIH1cbiAgfSwgW2NhbnZhcywgZWRpdGFibGVMYXllcnMsIGhhc0xvYWRlZEluaXRpYWxEYXRhXSk7XG5cbiAgLy8gTG9hZCBleGlzdGluZyBlZGl0YWJsZSBsYXllcnMgZnJvbSBjYW52YXMgb2JqZWN0cyAoZmFsbGJhY2spXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFjYW52YXMgfHwgZWRpdGFibGVMYXllcnMubGVuZ3RoID4gMCkgcmV0dXJuO1xuXG4gICAgY29uc3QgbGF5ZXJzOiBFZGl0YWJsZUxheWVyW10gPSBbXTtcbiAgICBjYW52YXMuZ2V0T2JqZWN0cygpLmZvckVhY2goKG9iaikgPT4ge1xuICAgICAgY29uc3QgZWRpdGFibGVPYmogPSBvYmogYXMgRWRpdGFibGVGYWJyaWNPYmplY3Q7XG4gICAgICBpZiAoZWRpdGFibGVPYmouaXNFZGl0YWJsZSkge1xuICAgICAgICBsYXllcnMucHVzaCh7XG4gICAgICAgICAgaWQ6IGVkaXRhYmxlT2JqLmlkIHx8ICcnLFxuICAgICAgICAgIHR5cGU6IGVkaXRhYmxlT2JqLmVkaXRhYmxlVHlwZSB8fCAndGV4dCcsXG4gICAgICAgICAgbmFtZTogZWRpdGFibGVPYmouZWRpdGFibGVOYW1lIHx8ICdVbm5hbWVkIExheWVyJyxcbiAgICAgICAgICBvcmlnaW5hbFZhbHVlOiBlZGl0YWJsZU9iai50eXBlID09PSAndGV4dGJveCcgPyAoZWRpdGFibGVPYmogYXMgZmFicmljLlRleHRib3gpLnRleHQgOiAnJyxcbiAgICAgICAgICBwbGFjZWhvbGRlcjogZWRpdGFibGVPYmouZWRpdGFibGVQbGFjZWhvbGRlcixcbiAgICAgICAgICBjb25zdHJhaW50czogZWRpdGFibGVPYmouZWRpdGFibGVDb25zdHJhaW50cyxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBpZiAobGF5ZXJzLmxlbmd0aCA+IDApIHtcbiAgICAgIHNldEVkaXRhYmxlTGF5ZXJzKGxheWVycyk7XG4gICAgICBzZXRJc0N1c3RvbWl6YWJsZSh0cnVlKTtcbiAgICB9XG4gIH0sIFtjYW52YXMsIGVkaXRhYmxlTGF5ZXJzLmxlbmd0aF0pO1xuXG4gIGNvbnN0IG1ha2VMYXllckVkaXRhYmxlID0gKHR5cGU6ICd0ZXh0JyB8ICdpbWFnZScpID0+IHtcbiAgICBpZiAoIWNhbnZhcyB8fCBzZWxlY3RlZE9iamVjdHMubGVuZ3RoID09PSAwKSByZXR1cm47XG5cbiAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXSBhcyBFZGl0YWJsZUZhYnJpY09iamVjdDtcbiAgICBcbiAgICAvLyBWYWxpZGF0ZSBvYmplY3QgdHlwZVxuICAgIGlmICh0eXBlID09PSAndGV4dCcgJiYgc2VsZWN0ZWRPYmplY3QudHlwZSAhPT0gJ3RleHRib3gnKSB7XG4gICAgICBhbGVydCgnUGxlYXNlIHNlbGVjdCBhIHRleHQgb2JqZWN0IHRvIG1ha2UgaXQgZWRpdGFibGUnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgXG4gICAgaWYgKHR5cGUgPT09ICdpbWFnZScgJiYgIVsnaW1hZ2UnLCAncmVjdCcsICdjaXJjbGUnXS5pbmNsdWRlcyhzZWxlY3RlZE9iamVjdC50eXBlIHx8ICcnKSkge1xuICAgICAgYWxlcnQoJ1BsZWFzZSBzZWxlY3QgYW4gaW1hZ2Ugb3Igc2hhcGUgdG8gbWFrZSBpdCBlZGl0YWJsZScpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIE1hcmsgb2JqZWN0IGFzIGVkaXRhYmxlXG4gICAgc2VsZWN0ZWRPYmplY3QuaXNFZGl0YWJsZSA9IHRydWU7XG4gICAgc2VsZWN0ZWRPYmplY3QuZWRpdGFibGVUeXBlID0gdHlwZTtcbiAgICBzZWxlY3RlZE9iamVjdC5lZGl0YWJsZU5hbWUgPSBgJHt0eXBlID09PSAndGV4dCcgPyAnVGV4dCcgOiAnSW1hZ2UnfSAke2VkaXRhYmxlTGF5ZXJzLmxlbmd0aCArIDF9YDtcbiAgICBcbiAgICBpZiAodHlwZSA9PT0gJ3RleHQnKSB7XG4gICAgICBzZWxlY3RlZE9iamVjdC5lZGl0YWJsZVBsYWNlaG9sZGVyID0gJ0VudGVyIHlvdXIgdGV4dCBoZXJlLi4uJztcbiAgICB9XG5cbiAgICAvLyBBZGQgdG8gZWRpdGFibGUgbGF5ZXJzIGxpc3RcbiAgICBjb25zdCBuZXdMYXllcjogRWRpdGFibGVMYXllciA9IHtcbiAgICAgIGlkOiBzZWxlY3RlZE9iamVjdC5pZCB8fCBgbGF5ZXJfJHtEYXRlLm5vdygpfWAsXG4gICAgICB0eXBlLFxuICAgICAgbmFtZTogc2VsZWN0ZWRPYmplY3QuZWRpdGFibGVOYW1lLFxuICAgICAgb3JpZ2luYWxWYWx1ZTogdHlwZSA9PT0gJ3RleHQnID8gKHNlbGVjdGVkT2JqZWN0IGFzIGZhYnJpYy5UZXh0Ym94KS50ZXh0IDogJycsXG4gICAgICBwbGFjZWhvbGRlcjogc2VsZWN0ZWRPYmplY3QuZWRpdGFibGVQbGFjZWhvbGRlcixcbiAgICAgIGNvbnN0cmFpbnRzOiB7XG4gICAgICAgIG1heExlbmd0aDogdHlwZSA9PT0gJ3RleHQnID8gMTAwIDogdW5kZWZpbmVkLFxuICAgICAgICBhbGxvd2VkRm9ybWF0czogdHlwZSA9PT0gJ2ltYWdlJyA/IFsnanBnJywgJ2pwZWcnLCAncG5nJywgJ2dpZiddIDogdW5kZWZpbmVkLFxuICAgICAgICBtYXhGaWxlU2l6ZTogdHlwZSA9PT0gJ2ltYWdlJyA/IDUgKiAxMDI0ICogMTAyNCA6IHVuZGVmaW5lZCwgLy8gNU1CXG4gICAgICB9LFxuICAgIH07XG5cbiAgICAvLyBBc3NpZ24gSUQgaWYgbm90IGV4aXN0c1xuICAgIGlmICghc2VsZWN0ZWRPYmplY3QuaWQpIHtcbiAgICAgIHNlbGVjdGVkT2JqZWN0LmlkID0gbmV3TGF5ZXIuaWQ7XG4gICAgfVxuXG4gICAgc2V0RWRpdGFibGVMYXllcnMoWy4uLmVkaXRhYmxlTGF5ZXJzLCBuZXdMYXllcl0pO1xuICAgIC8vIERvbid0IGF1dG9tYXRpY2FsbHkgZW5hYmxlIHRoZSB0b2dnbGUgLSBsZXQgdXNlciBjb250cm9sIGl0IG1hbnVhbGx5XG4gICAgY2FudmFzLnJlbmRlckFsbCgpO1xuICB9O1xuXG4gIGNvbnN0IHJlbW92ZUVkaXRhYmxlTGF5ZXIgPSAobGF5ZXJJZDogc3RyaW5nKSA9PiB7XG4gICAgLy8gRmluZCBhbmQgdXBkYXRlIHRoZSBjYW52YXMgb2JqZWN0XG4gICAgY29uc3QgY2FudmFzT2JqZWN0ID0gY2FudmFzPy5nZXRPYmplY3RzKCkuZmluZChvYmogPT4gb2JqLmlkID09PSBsYXllcklkKSBhcyBFZGl0YWJsZUZhYnJpY09iamVjdDtcbiAgICBpZiAoY2FudmFzT2JqZWN0KSB7XG4gICAgICBjYW52YXNPYmplY3QuaXNFZGl0YWJsZSA9IGZhbHNlO1xuICAgICAgZGVsZXRlIGNhbnZhc09iamVjdC5lZGl0YWJsZVR5cGU7XG4gICAgICBkZWxldGUgY2FudmFzT2JqZWN0LmVkaXRhYmxlTmFtZTtcbiAgICAgIGRlbGV0ZSBjYW52YXNPYmplY3QuZWRpdGFibGVQbGFjZWhvbGRlcjtcbiAgICAgIGRlbGV0ZSBjYW52YXNPYmplY3QuZWRpdGFibGVDb25zdHJhaW50cztcbiAgICB9XG5cbiAgICAvLyBSZW1vdmUgZnJvbSBsYXllcnMgbGlzdFxuICAgIGNvbnN0IHVwZGF0ZWRMYXllcnMgPSBlZGl0YWJsZUxheWVycy5maWx0ZXIobGF5ZXIgPT4gbGF5ZXIuaWQgIT09IGxheWVySWQpO1xuICAgIHNldEVkaXRhYmxlTGF5ZXJzKHVwZGF0ZWRMYXllcnMpO1xuICAgIC8vIERvbid0IGF1dG9tYXRpY2FsbHkgZGlzYWJsZSB0aGUgdG9nZ2xlIC0gbGV0IHVzZXIgY29udHJvbCBpdCBtYW51YWxseVxuICAgIGNhbnZhcz8ucmVuZGVyQWxsKCk7XG4gIH07XG5cbiAgY29uc3QgdXBkYXRlTGF5ZXJOYW1lID0gKGxheWVySWQ6IHN0cmluZywgbmFtZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlZExheWVycyA9IGVkaXRhYmxlTGF5ZXJzLm1hcChsYXllciA9PlxuICAgICAgbGF5ZXIuaWQgPT09IGxheWVySWQgPyB7IC4uLmxheWVyLCBuYW1lIH0gOiBsYXllclxuICAgICk7XG4gICAgc2V0RWRpdGFibGVMYXllcnModXBkYXRlZExheWVycyk7XG5cbiAgICAvLyBVcGRhdGUgY2FudmFzIG9iamVjdFxuICAgIGNvbnN0IGNhbnZhc09iamVjdCA9IGNhbnZhcz8uZ2V0T2JqZWN0cygpLmZpbmQob2JqID0+IG9iai5pZCA9PT0gbGF5ZXJJZCkgYXMgRWRpdGFibGVGYWJyaWNPYmplY3Q7XG4gICAgaWYgKGNhbnZhc09iamVjdCkge1xuICAgICAgY2FudmFzT2JqZWN0LmVkaXRhYmxlTmFtZSA9IG5hbWU7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZUxheWVyUGxhY2Vob2xkZXIgPSAobGF5ZXJJZDogc3RyaW5nLCBwbGFjZWhvbGRlcjogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlZExheWVycyA9IGVkaXRhYmxlTGF5ZXJzLm1hcChsYXllciA9PlxuICAgICAgbGF5ZXIuaWQgPT09IGxheWVySWQgPyB7IC4uLmxheWVyLCBwbGFjZWhvbGRlciB9IDogbGF5ZXJcbiAgICApO1xuICAgIHNldEVkaXRhYmxlTGF5ZXJzKHVwZGF0ZWRMYXllcnMpO1xuXG4gICAgLy8gVXBkYXRlIGNhbnZhcyBvYmplY3RcbiAgICBjb25zdCBjYW52YXNPYmplY3QgPSBjYW52YXM/LmdldE9iamVjdHMoKS5maW5kKG9iaiA9PiBvYmouaWQgPT09IGxheWVySWQpIGFzIEVkaXRhYmxlRmFicmljT2JqZWN0O1xuICAgIGlmIChjYW52YXNPYmplY3QpIHtcbiAgICAgIGNhbnZhc09iamVjdC5lZGl0YWJsZVBsYWNlaG9sZGVyID0gcGxhY2Vob2xkZXI7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHNhdmVUZW1wbGF0ZUNvbmZpZyA9ICgpID0+IHtcbiAgICBpZiAoIXByb2plY3RJZCkge1xuICAgICAgYWxlcnQoJ1Byb2plY3QgSUQgaXMgcmVxdWlyZWQgdG8gc2F2ZSB0ZW1wbGF0ZSBjb25maWd1cmF0aW9uJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdXBkYXRlVGVtcGxhdGVDb25maWcubXV0YXRlKHtcbiAgICAgIGlzQ3VzdG9taXphYmxlLFxuICAgICAgZWRpdGFibGVMYXllcnM6IEpTT04uc3RyaW5naWZ5KGVkaXRhYmxlTGF5ZXJzKSxcbiAgICB9LCB7XG4gICAgICBvblN1Y2Nlc3M6ICgpID0+IHtcbiAgICAgICAgLy8gRG9uJ3QgY2xvc2Ugc2lkZWJhciBhdXRvbWF0aWNhbGx5IC0gbGV0IHVzZXIgc2VlIHRoZSBzdWNjZXNzIHN0YXRlXG4gICAgICAgIC8vIFRoZXkgY2FuIGNsb3NlIGl0IG1hbnVhbGx5IGlmIG5lZWRlZFxuICAgICAgfVxuICAgIH0pO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJiZy13aGl0ZSByZWxhdGl2ZSBib3JkZXItciB6LVs0MF0gdy1bMzYwcHhdIGgtZnVsbCBmbGV4IGZsZXgtY29sXCIsXG4gICAgICAgIGFjdGl2ZVRvb2wgPT09IFwidGVtcGxhdGUtY29uZmlnXCIgPyBcInZpc2libGVcIiA6IFwiaGlkZGVuXCIsXG4gICAgICApfVxuICAgID5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wdXJwbGUtNjAwXCIgLz5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5UZW1wbGF0ZSBTZXR0aW5nczwvaDI+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwic21cIiBvbkNsaWNrPXtvbkNsb3NlfT5cbiAgICAgICAgICAgIMOXXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbXQtMVwiPlxuICAgICAgICAgIENvbmZpZ3VyZSB3aGljaCBlbGVtZW50cyB1c2VycyBjYW4gY3VzdG9taXplXG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8U2Nyb2xsQXJlYSBjbGFzc05hbWU9XCJmbGV4LTEgcC00XCI+XG4gICAgICAgIHsvKiBUZW1wbGF0ZSBTdGF0dXMgKi99XG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxHbG9iZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4+VGVtcGxhdGUgU3RhdHVzPC9zcGFuPlxuICAgICAgICAgICAgICB7aXNDdXN0b21pemFibGUgJiYgKFxuICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwiZGVmYXVsdFwiIGNsYXNzTmFtZT1cIm1sLTJcIj5cbiAgICAgICAgICAgICAgICAgIEFjdGl2ZVxuICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdXN0b21pemFibGVcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgTWFrZSBDdXN0b21pemFibGVcbiAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgICAgIGlkPVwiY3VzdG9taXphYmxlXCJcbiAgICAgICAgICAgICAgICBjaGVja2VkPXtpc0N1c3RvbWl6YWJsZX1cbiAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9e3NldElzQ3VzdG9taXphYmxlfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMlwiPlxuICAgICAgICAgICAgICB7aXNDdXN0b21pemFibGVcbiAgICAgICAgICAgICAgICA/IGBUZW1wbGF0ZSBpcyBjdXN0b21pemFibGUgd2l0aCAke2VkaXRhYmxlTGF5ZXJzLmxlbmd0aH0gZWRpdGFibGUgZWxlbWVudCR7ZWRpdGFibGVMYXllcnMubGVuZ3RoICE9PSAxID8gJ3MnIDogJyd9YFxuICAgICAgICAgICAgICAgIDogXCJBbGxvdyBwdWJsaWMgdXNlcnMgdG8gY3VzdG9taXplIHRoaXMgdGVtcGxhdGVcIlxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBBZGQgRWRpdGFibGUgRWxlbWVudHMgKi99XG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc21cIj5BZGQgRWRpdGFibGUgRWxlbWVudHM8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBqdXN0aWZ5LXN0YXJ0XCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gbWFrZUxheWVyRWRpdGFibGUoJ3RleHQnKX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3NlbGVjdGVkT2JqZWN0cy5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxUeXBlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIE1ha2UgVGV4dCBFZGl0YWJsZVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGp1c3RpZnktc3RhcnRcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBtYWtlTGF5ZXJFZGl0YWJsZSgnaW1hZ2UnKX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3NlbGVjdGVkT2JqZWN0cy5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxJbWFnZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBNYWtlIEltYWdlIFJlcGxhY2VhYmxlXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIHtzZWxlY3RlZE9iamVjdHMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgU2VsZWN0IGFuIGVsZW1lbnQgb24gdGhlIGNhbnZhcyBmaXJzdFxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7LyogRWRpdGFibGUgTGF5ZXJzIExpc3QgKi99XG4gICAgICAgIHtlZGl0YWJsZUxheWVycy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTNcIj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgRWRpdGFibGUgRWxlbWVudHMgKHtlZGl0YWJsZUxheWVycy5sZW5ndGh9KVxuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAge2VkaXRhYmxlTGF5ZXJzLm1hcCgobGF5ZXIpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17bGF5ZXIuaWR9IGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD17bGF5ZXIudHlwZSA9PT0gJ3RleHQnID8gJ2RlZmF1bHQnIDogJ3NlY29uZGFyeSd9PlxuICAgICAgICAgICAgICAgICAgICAgIHtsYXllci50eXBlID09PSAndGV4dCcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwZSBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8SW1hZ2UgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIHtsYXllci50eXBlfVxuICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZUVkaXRhYmxlTGF5ZXIobGF5ZXIuaWQpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC14c1wiPkRpc3BsYXkgTmFtZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bGF5ZXIubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlTGF5ZXJOYW1lKGxheWVyLmlkLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIFlvdXIgTmFtZSwgUHJvZmlsZSBQaG90b1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICB7bGF5ZXIudHlwZSA9PT0gJ3RleHQnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQteHNcIj5QbGFjZWhvbGRlciBUZXh0PC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bGF5ZXIucGxhY2Vob2xkZXIgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlTGF5ZXJQbGFjZWhvbGRlcihsYXllci5pZCwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgcGxhY2Vob2xkZXIgdGV4dC4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICl9XG4gICAgICA8L1Njcm9sbEFyZWE+XG5cbiAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPEJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e3NhdmVUZW1wbGF0ZUNvbmZpZ31cbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgIGRpc2FibGVkPXt1cGRhdGVUZW1wbGF0ZUNvbmZpZy5pc1BlbmRpbmd9XG4gICAgICAgICAgdmFyaWFudD17dXBkYXRlVGVtcGxhdGVDb25maWcuaXNTdWNjZXNzID8gXCJkZWZhdWx0XCIgOiBcImRlZmF1bHRcIn1cbiAgICAgICAgPlxuICAgICAgICAgIHt1cGRhdGVUZW1wbGF0ZUNvbmZpZy5pc1BlbmRpbmcgPyAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8U2F2ZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTIgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgU2F2aW5nLi4uXG4gICAgICAgICAgICA8Lz5cbiAgICAgICAgICApIDogdXBkYXRlVGVtcGxhdGVDb25maWcuaXNTdWNjZXNzID8gKFxuICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgPENoZWNrIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIFNhdmVkIFN1Y2Nlc3NmdWxseSFcbiAgICAgICAgICAgIDwvPlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8U2F2ZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBTYXZlIFRlbXBsYXRlIENvbmZpZ1xuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTZXR0aW5ncyIsIlR5cGUiLCJJbWFnZSIsIlRyYXNoMiIsIlNhdmUiLCJHbG9iZSIsIkNoZWNrIiwiQnV0dG9uIiwiSW5wdXQiLCJMYWJlbCIsIlN3aXRjaCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCYWRnZSIsIlNjcm9sbEFyZWEiLCJjbiIsInVzZVVwZGF0ZVRlbXBsYXRlQ29uZmlnIiwiVGVtcGxhdGVDb25maWdTaWRlYmFyIiwiZWRpdG9yIiwiYWN0aXZlVG9vbCIsIm9uQ2hhbmdlQWN0aXZlVG9vbCIsInByb2plY3RJZCIsImluaXRpYWxEYXRhIiwiZWRpdGFibGVMYXllcnMiLCJzZXRFZGl0YWJsZUxheWVycyIsImlzQ3VzdG9taXphYmxlIiwic2V0SXNDdXN0b21pemFibGUiLCJoYXNMb2FkZWRJbml0aWFsRGF0YSIsInNldEhhc0xvYWRlZEluaXRpYWxEYXRhIiwidXBkYXRlVGVtcGxhdGVDb25maWciLCJvbkNsb3NlIiwiY2FudmFzIiwic2VsZWN0ZWRPYmplY3RzIiwicGFyc2VkTGF5ZXJzIiwiSlNPTiIsInBhcnNlIiwiZXJyb3IiLCJjb25zb2xlIiwibGVuZ3RoIiwiZm9yRWFjaCIsImxheWVyIiwiY2FudmFzT2JqZWN0IiwiZ2V0T2JqZWN0cyIsImZpbmQiLCJvYmoiLCJpZCIsImlzRWRpdGFibGUiLCJlZGl0YWJsZVR5cGUiLCJ0eXBlIiwiZWRpdGFibGVOYW1lIiwibmFtZSIsImVkaXRhYmxlUGxhY2Vob2xkZXIiLCJwbGFjZWhvbGRlciIsImVkaXRhYmxlQ29uc3RyYWludHMiLCJjb25zdHJhaW50cyIsInJlbmRlckFsbCIsImxheWVycyIsImVkaXRhYmxlT2JqIiwicHVzaCIsIm9yaWdpbmFsVmFsdWUiLCJ0ZXh0IiwibWFrZUxheWVyRWRpdGFibGUiLCJzZWxlY3RlZE9iamVjdCIsImFsZXJ0IiwiaW5jbHVkZXMiLCJuZXdMYXllciIsIkRhdGUiLCJub3ciLCJtYXhMZW5ndGgiLCJ1bmRlZmluZWQiLCJhbGxvd2VkRm9ybWF0cyIsIm1heEZpbGVTaXplIiwicmVtb3ZlRWRpdGFibGVMYXllciIsImxheWVySWQiLCJ1cGRhdGVkTGF5ZXJzIiwiZmlsdGVyIiwidXBkYXRlTGF5ZXJOYW1lIiwibWFwIiwidXBkYXRlTGF5ZXJQbGFjZWhvbGRlciIsInNhdmVUZW1wbGF0ZUNvbmZpZyIsIm11dGF0ZSIsInN0cmluZ2lmeSIsIm9uU3VjY2VzcyIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwicCIsInNwYW4iLCJodG1sRm9yIiwiY2hlY2tlZCIsIm9uQ2hlY2tlZENoYW5nZSIsImRpc2FibGVkIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJpc1BlbmRpbmciLCJpc1N1Y2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\n"));

/***/ })

});