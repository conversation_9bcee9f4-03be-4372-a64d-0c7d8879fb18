"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [canvasInitialized, setCanvasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Simplified approach - no canvas refs needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers\n                const editableLayers = JSON.parse(templateData.editableLayers);\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleTextChange = (layerId, value)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        // Create object URL for immediate preview\n        const imageUrl = URL.createObjectURL(file);\n        // Update customizations immediately\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: imageUrl\n            }));\n    // In a real implementation, you'd upload to your storage service here\n    // const uploadedUrl = await uploadToStorage(file);\n    // setCustomizations(prev => ({ ...prev, [layerId]: uploadedUrl }));\n    };\n    // Initialize preview - use simple approach without Fabric.js canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!template) return;\n        // Simply use the template thumbnail as preview\n        // This avoids all the Fabric.js canvas issues\n        if (template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n            setCanvasInitialized(true);\n        }\n    // For now, we'll skip the complex canvas rendering to avoid errors\n    // In a production app, you might want to use a server-side rendering service\n    // or a simpler canvas approach that doesn't rely on Fabric.js\n    }, [\n        template\n    ]);\n    // Simple preview generation - just use template thumbnail\n    const generatePreviewFromCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // For now, always use the template thumbnail\n        // This avoids all canvas-related errors\n        if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n        }\n    }, [\n        template\n    ]);\n    // Handle customizations - simplified approach to avoid canvas errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // For now, we'll just use the template thumbnail regardless of customizations\n        // This completely avoids all Fabric.js canvas issues\n        // In a production app, you'd want to implement server-side rendering\n        // or use a more reliable canvas approach\n        if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n        }\n    }, [\n        customizations,\n        template\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Generate preview from current canvas state\n            generatePreviewFromCanvas();\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // In a real implementation, you'd get a download URL from the API\n            const link = document.createElement(\"a\");\n            link.href = (template === null || template === void 0 ? void 0 : template.thumbnailUrl) || \"\";\n            link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n            link.click();\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Customize Elements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: customizations[layer.id] || \"\",\n                                                                onChange: (e)=>handleTextChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 314,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 321,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 319,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center bg-gray-100 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-lg overflow-hidden flex items-center justify-center\",\n                                                style: {\n                                                    aspectRatio: \"\".concat(template.width, \"/\").concat(template.height),\n                                                    maxWidth: \"100%\",\n                                                    maxHeight: \"500px\",\n                                                    width: \"auto\",\n                                                    height: \"auto\"\n                                                },\n                                                children: previewUrl || template.thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: previewUrl || template.thumbnailUrl || \"\",\n                                                    alt: \"Template preview\",\n                                                    className: \"max-w-full max-h-full object-contain\",\n                                                    style: {\n                                                        width: \"auto\",\n                                                        height: \"auto\",\n                                                        maxWidth: \"100%\",\n                                                        maxHeight: \"100%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center bg-gray-50 w-full h-full min-h-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"bDAnX+1m+b6zO+JfNpWoxt0v8PE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ })

});