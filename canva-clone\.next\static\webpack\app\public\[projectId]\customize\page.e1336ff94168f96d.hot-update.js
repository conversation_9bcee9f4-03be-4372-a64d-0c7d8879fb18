"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated, activeLayerId: externalActiveLayerId, onLayerActivation } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize editor with proper callbacks\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{\n            onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n        },\n        saveCallback: ()=>{\n            // Generate preview when canvas changes\n            generatePreview();\n        }\n    });\n    // Initialize canvas (same as main editor)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                initializeCanvas();\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Lock non-editable objects and setup customization mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up customization mode:\", {\n            editableLayerIds,\n            totalObjects: canvas.getObjects().length,\n            editableLayers: templateData.editableLayers\n        });\n        // Lock all objects except editable ones\n        canvas.getObjects().forEach((obj)=>{\n            console.log(\"Processing object:\", {\n                id: obj.id,\n                type: obj.type,\n                name: obj.name,\n                isEditable: editableLayerIds.includes(obj.id)\n            });\n            if (obj.name === \"clip\") {\n                // Keep workspace as is but make it non-selectable\n                obj.set({\n                    selectable: false,\n                    evented: false\n                });\n                return;\n            }\n            if (editableLayerIds.includes(obj.id)) {\n                console.log(\"Making object editable:\", obj.id, obj.type);\n                // Make editable objects selectable and editable\n                obj.set({\n                    selectable: true,\n                    hasControls: false,\n                    hasBorders: true,\n                    borderColor: \"#3b82f6\",\n                    borderScaleFactor: 2,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true,\n                    editable: obj.type === \"textbox\" ? true : false,\n                    hoverCursor: \"pointer\",\n                    moveCursor: \"pointer\"\n                });\n                // Add visual indicator for editable elements\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: \"rgba(59, 130, 246, 0.1)\"\n                    });\n                }\n            } else {\n                // Lock non-editable objects completely\n                obj.set({\n                    selectable: false,\n                    evented: false,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n            }\n        });\n        canvas.renderAll();\n    }, [\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Update visual feedback for editable objects\n    const updateEditableObjectsVisuals = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        editor.canvas.getObjects().forEach((obj)=>{\n            if (editableLayerIds.includes(obj.id)) {\n                const isActive = obj.id === activeLayerId;\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: isActive ? \"rgba(59, 130, 246, 0.2)\" // Darker blue when active\n                         : \"rgba(59, 130, 246, 0.1)\",\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2\n                    });\n                } else {\n                    // For image layers\n                    obj.set({\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2,\n                        opacity: isActive ? 1 : 0.8\n                    });\n                }\n            }\n        });\n        editor.canvas.renderAll();\n    }, [\n        editor,\n        templateData.editableLayers,\n        activeLayerId\n    ]);\n    // Update visuals when active layer changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateEditableObjectsVisuals();\n    }, [\n        activeLayerId,\n        updateEditableObjectsVisuals\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue || customValue === layer.originalValue) return;\n            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                canvasObject.set(\"text\", customValue);\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        selectable: true,\n                        hasControls: false,\n                        lockMovementX: true,\n                        lockMovementY: true,\n                        lockRotation: true,\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true\n                    });\n                    // Set the ID using a custom property\n                    img.id = layer.id;\n                    canvas.remove(canvasObject);\n                    canvas.add(img);\n                    canvas.renderAll();\n                    generatePreview();\n                });\n                return;\n            }\n        });\n        canvas.renderAll();\n        generatePreview();\n    }, [\n        customizations,\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Generate preview from canvas\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            // Create a temporary canvas with just the workspace content\n            const tempCanvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(null, {\n                width: workspace.width,\n                height: workspace.height\n            });\n            // Clone all objects except the workspace itself\n            const objectsToClone = editor.canvas.getObjects().filter((obj)=>obj.name !== \"clip\");\n            objectsToClone.forEach((obj)=>{\n                obj.clone((cloned)=>{\n                    // Adjust position relative to workspace\n                    cloned.set({\n                        left: (cloned.left || 0) - (workspace.left || 0),\n                        top: (cloned.top || 0) - (workspace.top || 0)\n                    });\n                    tempCanvas.add(cloned);\n                });\n            });\n            // Generate preview after a short delay to ensure all objects are added\n            setTimeout(()=>{\n                const dataUrl = tempCanvas.toDataURL({\n                    format: \"png\",\n                    quality: 0.9,\n                    multiplier: 1\n                });\n                onPreviewGenerated(dataUrl);\n                tempCanvas.dispose();\n            }, 100);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Handle text editing\n    const handleTextEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((layerId, value)=>{\n        onCustomizationChange(layerId, value);\n    }, [\n        onCustomizationChange\n    ]);\n    // Handle click events and layer activation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleObjectSelection = (e)=>{\n            const target = e.target;\n            if (!target) {\n                setActiveLayerId(null);\n                return;\n            }\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer) {\n                console.log(\"Layer activated:\", layer);\n                setActiveLayerId(layerId);\n                onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(layerId);\n                // Update visual feedback for all editable objects\n                updateEditableObjectsVisuals();\n            } else {\n                setActiveLayerId(null);\n                onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n            }\n        };\n        const handleCanvasClick = (e)=>{\n            if (!e.target) {\n                setActiveLayerId(null);\n                onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n                updateEditableObjectsVisuals();\n            }\n        };\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (!layer || layer.type !== \"text\") return;\n            const currentText = target.text || \"\";\n            handleTextEdit(layerId, currentText);\n            generatePreview();\n        };\n        const handleDoubleClick = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer && layer.type === \"text\") {\n                setActiveLayerId(layerId);\n                // Enter editing mode\n                target.enterEditing();\n                target.selectAll();\n            }\n        };\n        editor.canvas.on(\"selection:created\", handleObjectSelection);\n        editor.canvas.on(\"selection:updated\", handleObjectSelection);\n        editor.canvas.on(\"selection:cleared\", ()=>setActiveLayerId(null));\n        editor.canvas.on(\"mouse:down\", handleCanvasClick);\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        editor.canvas.on(\"mouse:dblclick\", handleDoubleClick);\n        return ()=>{\n            editor.canvas.off(\"selection:created\", handleObjectSelection);\n            editor.canvas.off(\"selection:updated\", handleObjectSelection);\n            editor.canvas.off(\"selection:cleared\", ()=>setActiveLayerId(null));\n            editor.canvas.off(\"mouse:down\", handleCanvasClick);\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n            editor.canvas.off(\"mouse:dblclick\", handleDoubleClick);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        handleTextEdit,\n        generatePreview\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    ref: containerRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 393,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"VZ473vYDOFkAUsM4Ydchl1DJH7w=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvY29tcG9uZW50cy9jdXN0b21pemF0aW9uLWVkaXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUVpRTtBQUNqQztBQUMrQjtBQUNGO0FBb0J0RCxNQUFNTSxzQkFBc0I7UUFBQyxFQUNsQ0MsWUFBWSxFQUNaQyxjQUFjLEVBQ2RDLHFCQUFxQixFQUNyQkMsa0JBQWtCLEVBQ2xCQyxlQUFlQyxxQkFBcUIsRUFDcENDLGlCQUFpQixFQUNROztJQUN6QixNQUFNQyxZQUFZYiw2Q0FBTUEsQ0FBb0I7SUFDNUMsTUFBTWMsZUFBZWQsNkNBQU1BLENBQWlCO0lBRTVDLDBDQUEwQztJQUMxQyxNQUFNLEVBQUVlLElBQUksRUFBRUMsTUFBTSxFQUFFLEdBQUdiLDRFQUFTQSxDQUFDO1FBQ2pDYyxjQUFjWCxhQUFhWSxJQUFJO1FBQy9CQyxjQUFjYixhQUFhYyxLQUFLO1FBQ2hDQyxlQUFlZixhQUFhZ0IsTUFBTTtRQUNsQ0Msd0JBQXdCO1lBQ3RCWCw4QkFBQUEsd0NBQUFBLGtCQUFvQjtRQUN0QjtRQUNBWSxjQUFjO1lBQ1osdUNBQXVDO1lBQ3ZDQztRQUNGO0lBQ0Y7SUFFQSwwQ0FBMEM7SUFDMUMxQixnREFBU0EsQ0FBQztRQUNSLE1BQU0yQixtQkFBbUI7WUFDdkIsSUFBSSxDQUFDWixhQUFhYSxPQUFPLElBQUksQ0FBQ2QsVUFBVWMsT0FBTyxFQUFFLE9BQU87WUFFeEQsTUFBTUMsWUFBWWQsYUFBYWEsT0FBTztZQUN0QyxNQUFNRSxpQkFBaUJELFVBQVVFLFdBQVc7WUFDNUMsTUFBTUMsa0JBQWtCSCxVQUFVSSxZQUFZO1lBRTlDLElBQUlILG1CQUFtQixLQUFLRSxvQkFBb0IsR0FBRztnQkFDakQsT0FBTztZQUNUO1lBRUEsTUFBTUUsU0FBUyxJQUFJL0IsMENBQU1BLENBQUNnQyxNQUFNLENBQUNyQixVQUFVYyxPQUFPLEVBQUU7Z0JBQ2xEUSxzQkFBc0I7Z0JBQ3RCQyx3QkFBd0I7WUFDMUI7WUFFQXJCLEtBQUs7Z0JBQ0hzQixlQUFlSjtnQkFDZkssa0JBQWtCVjtZQUNwQjtZQUVBLE9BQU9LO1FBQ1Q7UUFFQSxNQUFNQSxTQUFTUDtRQUVmLElBQUksQ0FBQ08sUUFBUTtZQUNYLE1BQU1NLFlBQVlDLFdBQVc7Z0JBQzNCZDtZQUNGLEdBQUc7WUFFSCxPQUFPLElBQU1lLGFBQWFGO1FBQzVCO1FBRUEsT0FBTztZQUNMLElBQUlOLFFBQVE7Z0JBQ1ZBLE9BQU9TLE9BQU87WUFDaEI7UUFDRjtJQUNGLEdBQUc7UUFBQzNCO0tBQUs7SUFFVCx5REFBeUQ7SUFDekRoQixnREFBU0EsQ0FBQztRQUNSLElBQUksRUFBQ2lCLG1CQUFBQSw2QkFBQUEsT0FBUWlCLE1BQU0sS0FBSSxDQUFDVSxlQUFlO1FBRXZDLE1BQU1WLFNBQVNqQixPQUFPaUIsTUFBTTtRQUM1QixNQUFNVyxtQkFBbUJ0QyxhQUFhdUMsY0FBYyxDQUFDQyxHQUFHLENBQUNDLENBQUFBLFFBQVNBLE1BQU1DLEVBQUU7UUFFMUVDLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0M7WUFDNUNOO1lBQ0FPLGNBQWNsQixPQUFPbUIsVUFBVSxHQUFHQyxNQUFNO1lBQ3hDUixnQkFBZ0J2QyxhQUFhdUMsY0FBYztRQUM3QztRQUVBLHdDQUF3QztRQUN4Q1osT0FBT21CLFVBQVUsR0FBR0UsT0FBTyxDQUFDLENBQUNDO1lBQzNCTixRQUFRQyxHQUFHLENBQUMsc0JBQXNCO2dCQUNoQ0YsSUFBSU8sSUFBSVAsRUFBRTtnQkFDVlEsTUFBTUQsSUFBSUMsSUFBSTtnQkFDZEMsTUFBTUYsSUFBSUUsSUFBSTtnQkFDZEMsWUFBWWQsaUJBQWlCZSxRQUFRLENBQUNKLElBQUlQLEVBQUU7WUFDOUM7WUFFQSxJQUFJTyxJQUFJRSxJQUFJLEtBQUssUUFBUTtnQkFDdkIsa0RBQWtEO2dCQUNsREYsSUFBSUssR0FBRyxDQUFDO29CQUNOQyxZQUFZO29CQUNaQyxTQUFTO2dCQUNYO2dCQUNBO1lBQ0Y7WUFFQSxJQUFJbEIsaUJBQWlCZSxRQUFRLENBQUNKLElBQUlQLEVBQUUsR0FBRztnQkFDckNDLFFBQVFDLEdBQUcsQ0FBQywyQkFBMkJLLElBQUlQLEVBQUUsRUFBRU8sSUFBSUMsSUFBSTtnQkFDdkQsZ0RBQWdEO2dCQUNoREQsSUFBSUssR0FBRyxDQUFDO29CQUNOQyxZQUFZO29CQUNaRSxhQUFhO29CQUNiQyxZQUFZO29CQUNaQyxhQUFhO29CQUNiQyxtQkFBbUI7b0JBQ25CQyxlQUFlO29CQUNmQyxlQUFlO29CQUNmQyxjQUFjO29CQUNkQyxjQUFjO29CQUNkQyxjQUFjO29CQUNkQyxnQkFBZ0I7b0JBQ2hCQyxVQUFVbEIsSUFBSUMsSUFBSSxLQUFLLFlBQVksT0FBTztvQkFDMUNrQixhQUFhO29CQUNiQyxZQUFZO2dCQUNkO2dCQUVBLDZDQUE2QztnQkFDN0MsSUFBSXBCLElBQUlDLElBQUksS0FBSyxXQUFXO29CQUMxQkQsSUFBSUssR0FBRyxDQUFDO3dCQUNOZ0IsaUJBQWlCO29CQUNuQjtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsdUNBQXVDO2dCQUN2Q3JCLElBQUlLLEdBQUcsQ0FBQztvQkFDTkMsWUFBWTtvQkFDWkMsU0FBUztvQkFDVEssZUFBZTtvQkFDZkMsZUFBZTtvQkFDZkMsY0FBYztvQkFDZEMsY0FBYztvQkFDZEMsY0FBYztvQkFDZEMsZ0JBQWdCO2dCQUNsQjtZQUNGO1FBQ0Y7UUFFQXZDLE9BQU80QyxTQUFTO0lBQ2xCLEdBQUc7UUFBQzdEO1FBQVEyQjtRQUFlckMsYUFBYXVDLGNBQWM7S0FBQztJQUV2RCw4Q0FBOEM7SUFDOUMsTUFBTWlDLCtCQUErQjdFLGtEQUFXQSxDQUFDO1FBQy9DLElBQUksRUFBQ2UsbUJBQUFBLDZCQUFBQSxPQUFRaUIsTUFBTSxHQUFFO1FBRXJCLE1BQU1XLG1CQUFtQnRDLGFBQWF1QyxjQUFjLENBQUNDLEdBQUcsQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTUMsRUFBRTtRQUUxRWhDLE9BQU9pQixNQUFNLENBQUNtQixVQUFVLEdBQUdFLE9BQU8sQ0FBQyxDQUFDQztZQUNsQyxJQUFJWCxpQkFBaUJlLFFBQVEsQ0FBQ0osSUFBSVAsRUFBRSxHQUFHO2dCQUNyQyxNQUFNK0IsV0FBV3hCLElBQUlQLEVBQUUsS0FBS3RDO2dCQUU1QixJQUFJNkMsSUFBSUMsSUFBSSxLQUFLLFdBQVc7b0JBQzFCRCxJQUFJSyxHQUFHLENBQUM7d0JBQ05nQixpQkFBaUJHLFdBQ2IsMEJBQTBCLDBCQUEwQjsyQkFDcEQ7d0JBQ0pkLGFBQWFjLFdBQVcsWUFBWTt3QkFDcENiLG1CQUFtQmEsV0FBVyxJQUFJO29CQUNwQztnQkFDRixPQUFPO29CQUNMLG1CQUFtQjtvQkFDbkJ4QixJQUFJSyxHQUFHLENBQUM7d0JBQ05LLGFBQWFjLFdBQVcsWUFBWTt3QkFDcENiLG1CQUFtQmEsV0FBVyxJQUFJO3dCQUNsQ0MsU0FBU0QsV0FBVyxJQUFJO29CQUMxQjtnQkFDRjtZQUNGO1FBQ0Y7UUFFQS9ELE9BQU9pQixNQUFNLENBQUM0QyxTQUFTO0lBQ3pCLEdBQUc7UUFBQzdEO1FBQVFWLGFBQWF1QyxjQUFjO1FBQUVuQztLQUFjO0lBRXZELDJDQUEyQztJQUMzQ1gsZ0RBQVNBLENBQUM7UUFDUitFO0lBQ0YsR0FBRztRQUFDcEU7UUFBZW9FO0tBQTZCO0lBRWhELGlDQUFpQztJQUNqQy9FLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxFQUFDaUIsbUJBQUFBLDZCQUFBQSxPQUFRaUIsTUFBTSxLQUFJLENBQUNVLGVBQWU7UUFFdkMsTUFBTVYsU0FBU2pCLE9BQU9pQixNQUFNO1FBRTVCM0IsYUFBYXVDLGNBQWMsQ0FBQ1MsT0FBTyxDQUFDUCxDQUFBQTtZQUNsQyxNQUFNa0MsY0FBYzFFLGNBQWMsQ0FBQ3dDLE1BQU1DLEVBQUUsQ0FBQztZQUM1QyxJQUFJLENBQUNpQyxlQUFlQSxnQkFBZ0JsQyxNQUFNbUMsYUFBYSxFQUFFO1lBRXpELE1BQU1DLGVBQWVsRCxPQUFPbUIsVUFBVSxHQUFHZ0MsSUFBSSxDQUFDLENBQUM3QixNQUFhQSxJQUFJUCxFQUFFLEtBQUtELE1BQU1DLEVBQUU7WUFDL0UsSUFBSSxDQUFDbUMsY0FBYztZQUVuQixJQUFJcEMsTUFBTVMsSUFBSSxLQUFLLFVBQVUyQixhQUFhM0IsSUFBSSxLQUFLLFdBQVc7Z0JBQzNEMkIsYUFBZ0N2QixHQUFHLENBQUMsUUFBUXFCO1lBQy9DLE9BQU8sSUFBSWxDLE1BQU1TLElBQUksS0FBSyxXQUFXeUIsWUFBWUksVUFBVSxDQUFDLFVBQVU7Z0JBQ3BFbkYsMENBQU1BLENBQUNvRixLQUFLLENBQUNDLE9BQU8sQ0FBQ04sYUFBYSxDQUFDTztvQkFDakMsSUFBSSxDQUFDdkQsUUFBUTtvQkFFYix1Q0FBdUM7b0JBQ3ZDLE1BQU13RCxTQUFTTixhQUFhL0QsS0FBSyxHQUFJb0UsSUFBSXBFLEtBQUs7b0JBQzlDLE1BQU1zRSxTQUFTUCxhQUFhN0QsTUFBTSxHQUFJa0UsSUFBSWxFLE1BQU07b0JBQ2hELE1BQU1xRSxRQUFRQyxLQUFLQyxHQUFHLENBQUNKLFFBQVFDO29CQUUvQkYsSUFBSTVCLEdBQUcsQ0FBQzt3QkFDTmtDLE1BQU1YLGFBQWFXLElBQUk7d0JBQ3ZCQyxLQUFLWixhQUFhWSxHQUFHO3dCQUNyQk4sUUFBUUU7d0JBQ1JELFFBQVFDO3dCQUNSSyxTQUFTYixhQUFhYSxPQUFPO3dCQUM3QkMsU0FBU2QsYUFBYWMsT0FBTzt3QkFDN0JwQyxZQUFZO3dCQUNaRSxhQUFhO3dCQUNiSSxlQUFlO3dCQUNmQyxlQUFlO3dCQUNmQyxjQUFjO3dCQUNkQyxjQUFjO3dCQUNkQyxjQUFjO3dCQUNkQyxnQkFBZ0I7b0JBQ2xCO29CQUVBLHFDQUFxQztvQkFDcENnQixJQUFZeEMsRUFBRSxHQUFHRCxNQUFNQyxFQUFFO29CQUUxQmYsT0FBT2lFLE1BQU0sQ0FBQ2Y7b0JBQ2RsRCxPQUFPa0UsR0FBRyxDQUFDWDtvQkFDWHZELE9BQU80QyxTQUFTO29CQUNoQnBEO2dCQUNGO2dCQUNBO1lBQ0Y7UUFDRjtRQUVBUSxPQUFPNEMsU0FBUztRQUNoQnBEO0lBQ0YsR0FBRztRQUFDbEI7UUFBZ0JTO1FBQVEyQjtRQUFlckMsYUFBYXVDLGNBQWM7S0FBQztJQUV2RSwrQkFBK0I7SUFDL0IsTUFBTXBCLGtCQUFrQnhCLGtEQUFXQSxDQUFDO1FBQ2xDLElBQUksRUFBQ2UsbUJBQUFBLDZCQUFBQSxPQUFRaUIsTUFBTSxHQUFFO1FBRXJCLElBQUk7WUFDRixNQUFNbUUsWUFBWXBGLE9BQU9pQixNQUFNLENBQUNtQixVQUFVLEdBQUdnQyxJQUFJLENBQUMsQ0FBQzdCLE1BQWFBLElBQUlFLElBQUksS0FBSztZQUM3RSxJQUFJLENBQUMyQyxXQUFXO1lBRWhCLDREQUE0RDtZQUM1RCxNQUFNQyxhQUFhLElBQUluRywwQ0FBTUEsQ0FBQ2dDLE1BQU0sQ0FBQyxNQUFNO2dCQUN6Q2QsT0FBT2dGLFVBQVVoRixLQUFLO2dCQUN0QkUsUUFBUThFLFVBQVU5RSxNQUFNO1lBQzFCO1lBRUEsZ0RBQWdEO1lBQ2hELE1BQU1nRixpQkFBaUJ0RixPQUFPaUIsTUFBTSxDQUFDbUIsVUFBVSxHQUFHbUQsTUFBTSxDQUFDLENBQUNoRCxNQUFhQSxJQUFJRSxJQUFJLEtBQUs7WUFFcEY2QyxlQUFlaEQsT0FBTyxDQUFDLENBQUNDO2dCQUN0QkEsSUFBSWlELEtBQUssQ0FBQyxDQUFDQztvQkFDVCx3Q0FBd0M7b0JBQ3hDQSxPQUFPN0MsR0FBRyxDQUFDO3dCQUNUa0MsTUFBTSxDQUFDVyxPQUFPWCxJQUFJLElBQUksS0FBTU0sQ0FBQUEsVUFBVU4sSUFBSSxJQUFJO3dCQUM5Q0MsS0FBSyxDQUFDVSxPQUFPVixHQUFHLElBQUksS0FBTUssQ0FBQUEsVUFBVUwsR0FBRyxJQUFJO29CQUM3QztvQkFDQU0sV0FBV0YsR0FBRyxDQUFDTTtnQkFDakI7WUFDRjtZQUVBLHVFQUF1RTtZQUN2RWpFLFdBQVc7Z0JBQ1QsTUFBTWtFLFVBQVVMLFdBQVdNLFNBQVMsQ0FBQztvQkFDbkNDLFFBQVE7b0JBQ1JDLFNBQVM7b0JBQ1RDLFlBQVk7Z0JBQ2Q7Z0JBQ0FyRyxtQkFBbUJpRztnQkFDbkJMLFdBQVczRCxPQUFPO1lBQ3BCLEdBQUc7UUFDTCxFQUFFLE9BQU9xRSxPQUFPO1lBQ2Q5RCxRQUFROEQsS0FBSyxDQUFDLCtCQUErQkE7UUFDL0M7SUFDRixHQUFHO1FBQUMvRjtRQUFRUDtLQUFtQjtJQUUvQixzQkFBc0I7SUFDdEIsTUFBTXVHLGlCQUFpQi9HLGtEQUFXQSxDQUFDLENBQUNnSCxTQUFpQkM7UUFDbkQxRyxzQkFBc0J5RyxTQUFTQztJQUNqQyxHQUFHO1FBQUMxRztLQUFzQjtJQUkxQiwyQ0FBMkM7SUFDM0NULGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxFQUFDaUIsbUJBQUFBLDZCQUFBQSxPQUFRaUIsTUFBTSxHQUFFO1FBRXJCLE1BQU1rRix3QkFBd0IsQ0FBQ0M7WUFDN0IsTUFBTUMsU0FBU0QsRUFBRUMsTUFBTTtZQUN2QixJQUFJLENBQUNBLFFBQVE7Z0JBQ1hDLGlCQUFpQjtnQkFDakI7WUFDRjtZQUVBLE1BQU1MLFVBQVUsT0FBZ0JqRSxFQUFFO1lBQ2xDLE1BQU1ELFFBQVF6QyxhQUFhdUMsY0FBYyxDQUFDdUMsSUFBSSxDQUFDbUMsQ0FBQUEsSUFBS0EsRUFBRXZFLEVBQUUsS0FBS2lFO1lBRTdELElBQUlsRSxPQUFPO2dCQUNURSxRQUFRQyxHQUFHLENBQUMsb0JBQW9CSDtnQkFDaEN1RSxpQkFBaUJMO2dCQUNqQnJHLDhCQUFBQSx3Q0FBQUEsa0JBQW9CcUc7Z0JBRXBCLGtEQUFrRDtnQkFDbERuQztZQUNGLE9BQU87Z0JBQ0x3QyxpQkFBaUI7Z0JBQ2pCMUcsOEJBQUFBLHdDQUFBQSxrQkFBb0I7WUFDdEI7UUFDRjtRQUVBLE1BQU00RyxvQkFBb0IsQ0FBQ0o7WUFDekIsSUFBSSxDQUFDQSxFQUFFQyxNQUFNLEVBQUU7Z0JBQ2JDLGlCQUFpQjtnQkFDakIxRyw4QkFBQUEsd0NBQUFBLGtCQUFvQjtnQkFDcEJrRTtZQUNGO1FBQ0Y7UUFFQSxNQUFNMkMsb0JBQW9CLENBQUNMO1lBQ3pCLE1BQU1DLFNBQVNELEVBQUVDLE1BQU07WUFDdkIsSUFBSSxDQUFDQSxVQUFVQSxPQUFPN0QsSUFBSSxLQUFLLFdBQVc7WUFFMUMsTUFBTXlELFVBQVUsT0FBZ0JqRSxFQUFFO1lBQ2xDLE1BQU1ELFFBQVF6QyxhQUFhdUMsY0FBYyxDQUFDdUMsSUFBSSxDQUFDbUMsQ0FBQUEsSUFBS0EsRUFBRXZFLEVBQUUsS0FBS2lFO1lBQzdELElBQUksQ0FBQ2xFLFNBQVNBLE1BQU1TLElBQUksS0FBSyxRQUFRO1lBRXJDLE1BQU1rRSxjQUFjLE9BQTJCQyxJQUFJLElBQUk7WUFDdkRYLGVBQWVDLFNBQVNTO1lBQ3hCakc7UUFDRjtRQUVBLE1BQU1tRyxvQkFBb0IsQ0FBQ1I7WUFDekIsTUFBTUMsU0FBU0QsRUFBRUMsTUFBTTtZQUN2QixJQUFJLENBQUNBLFVBQVVBLE9BQU83RCxJQUFJLEtBQUssV0FBVztZQUUxQyxNQUFNeUQsVUFBVSxPQUFnQmpFLEVBQUU7WUFDbEMsTUFBTUQsUUFBUXpDLGFBQWF1QyxjQUFjLENBQUN1QyxJQUFJLENBQUNtQyxDQUFBQSxJQUFLQSxFQUFFdkUsRUFBRSxLQUFLaUU7WUFDN0QsSUFBSWxFLFNBQVNBLE1BQU1TLElBQUksS0FBSyxRQUFRO2dCQUNsQzhELGlCQUFpQkw7Z0JBQ2pCLHFCQUFxQjtnQkFDcEJJLE9BQTBCUSxZQUFZO2dCQUN0Q1IsT0FBMEJTLFNBQVM7WUFDdEM7UUFDRjtRQUVBOUcsT0FBT2lCLE1BQU0sQ0FBQzhGLEVBQUUsQ0FBQyxxQkFBcUJaO1FBQ3RDbkcsT0FBT2lCLE1BQU0sQ0FBQzhGLEVBQUUsQ0FBQyxxQkFBcUJaO1FBQ3RDbkcsT0FBT2lCLE1BQU0sQ0FBQzhGLEVBQUUsQ0FBQyxxQkFBcUIsSUFBTVQsaUJBQWlCO1FBQzdEdEcsT0FBT2lCLE1BQU0sQ0FBQzhGLEVBQUUsQ0FBQyxjQUFjUDtRQUMvQnhHLE9BQU9pQixNQUFNLENBQUM4RixFQUFFLENBQUMsZ0JBQWdCTjtRQUNqQ3pHLE9BQU9pQixNQUFNLENBQUM4RixFQUFFLENBQUMsa0JBQWtCSDtRQUVuQyxPQUFPO1lBQ0w1RyxPQUFPaUIsTUFBTSxDQUFDK0YsR0FBRyxDQUFDLHFCQUFxQmI7WUFDdkNuRyxPQUFPaUIsTUFBTSxDQUFDK0YsR0FBRyxDQUFDLHFCQUFxQmI7WUFDdkNuRyxPQUFPaUIsTUFBTSxDQUFDK0YsR0FBRyxDQUFDLHFCQUFxQixJQUFNVixpQkFBaUI7WUFDOUR0RyxPQUFPaUIsTUFBTSxDQUFDK0YsR0FBRyxDQUFDLGNBQWNSO1lBQ2hDeEcsT0FBT2lCLE1BQU0sQ0FBQytGLEdBQUcsQ0FBQyxnQkFBZ0JQO1lBQ2xDekcsT0FBT2lCLE1BQU0sQ0FBQytGLEdBQUcsQ0FBQyxrQkFBa0JKO1FBQ3RDO0lBQ0YsR0FBRztRQUFDNUc7UUFBUVYsYUFBYXVDLGNBQWM7UUFBRW1FO1FBQWdCdkY7S0FBZ0I7SUFFekUscUJBQ0UsOERBQUN3RztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVO29CQUFtQkMsS0FBS3JIOzhCQUNyQyw0RUFBQ21CO3dCQUFPa0csS0FBS3RIOzs7Ozs7Ozs7Ozs7Ozs7OzBCQUdqQiw4REFBQ1Qsc0VBQU1BO2dCQUFDWSxRQUFRQTs7Ozs7Ozs7Ozs7O0FBR3RCLEVBQUU7R0F4WFdYOztRQVljRix3RUFBU0E7OztLQVp2QkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2ZlYXR1cmVzL2VkaXRvci9jb21wb25lbnRzL2N1c3RvbWl6YXRpb24tZWRpdG9yLnRzeD85NDRiIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBmYWJyaWMgfSBmcm9tIFwiZmFicmljXCI7XG5pbXBvcnQgeyB1c2VFZGl0b3IgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLWVkaXRvclwiO1xuaW1wb3J0IHsgRm9vdGVyIH0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL2NvbXBvbmVudHMvZm9vdGVyXCI7XG5pbXBvcnQgeyBUb29sYmFyIH0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL2NvbXBvbmVudHMvdG9vbGJhclwiO1xuaW1wb3J0IHsgRWRpdGFibGVMYXllciB9IGZyb20gXCJAL3R5cGVzL3RlbXBsYXRlXCI7XG5cbmludGVyZmFjZSBDdXN0b21pemF0aW9uRWRpdG9yUHJvcHMge1xuICB0ZW1wbGF0ZURhdGE6IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIG5hbWU6IHN0cmluZztcbiAgICB3aWR0aDogbnVtYmVyO1xuICAgIGhlaWdodDogbnVtYmVyO1xuICAgIGpzb246IHN0cmluZztcbiAgICBlZGl0YWJsZUxheWVyczogRWRpdGFibGVMYXllcltdO1xuICB9O1xuICBjdXN0b21pemF0aW9uczogUmVjb3JkPHN0cmluZywgc3RyaW5nPjtcbiAgb25DdXN0b21pemF0aW9uQ2hhbmdlOiAobGF5ZXJJZDogc3RyaW5nLCB2YWx1ZTogc3RyaW5nKSA9PiB2b2lkO1xuICBvblByZXZpZXdHZW5lcmF0ZWQ6IChkYXRhVXJsOiBzdHJpbmcpID0+IHZvaWQ7XG4gIGFjdGl2ZUxheWVySWQ/OiBzdHJpbmcgfCBudWxsO1xuICBvbkxheWVyQWN0aXZhdGlvbj86IChsYXllcklkOiBzdHJpbmcgfCBudWxsKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgY29uc3QgQ3VzdG9taXphdGlvbkVkaXRvciA9ICh7XG4gIHRlbXBsYXRlRGF0YSxcbiAgY3VzdG9taXphdGlvbnMsXG4gIG9uQ3VzdG9taXphdGlvbkNoYW5nZSxcbiAgb25QcmV2aWV3R2VuZXJhdGVkLFxuICBhY3RpdmVMYXllcklkOiBleHRlcm5hbEFjdGl2ZUxheWVySWQsXG4gIG9uTGF5ZXJBY3RpdmF0aW9uLFxufTogQ3VzdG9taXphdGlvbkVkaXRvclByb3BzKSA9PiB7XG4gIGNvbnN0IGNhbnZhc1JlZiA9IHVzZVJlZjxIVE1MQ2FudmFzRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IGNvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG5cbiAgLy8gSW5pdGlhbGl6ZSBlZGl0b3Igd2l0aCBwcm9wZXIgY2FsbGJhY2tzXG4gIGNvbnN0IHsgaW5pdCwgZWRpdG9yIH0gPSB1c2VFZGl0b3Ioe1xuICAgIGRlZmF1bHRTdGF0ZTogdGVtcGxhdGVEYXRhLmpzb24sXG4gICAgZGVmYXVsdFdpZHRoOiB0ZW1wbGF0ZURhdGEud2lkdGgsXG4gICAgZGVmYXVsdEhlaWdodDogdGVtcGxhdGVEYXRhLmhlaWdodCxcbiAgICBjbGVhclNlbGVjdGlvbkNhbGxiYWNrOiAoKSA9PiB7XG4gICAgICBvbkxheWVyQWN0aXZhdGlvbj8uKG51bGwpO1xuICAgIH0sXG4gICAgc2F2ZUNhbGxiYWNrOiAoKSA9PiB7XG4gICAgICAvLyBHZW5lcmF0ZSBwcmV2aWV3IHdoZW4gY2FudmFzIGNoYW5nZXNcbiAgICAgIGdlbmVyYXRlUHJldmlldygpO1xuICAgIH0sXG4gIH0pO1xuXG4gIC8vIEluaXRpYWxpemUgY2FudmFzIChzYW1lIGFzIG1haW4gZWRpdG9yKVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRpYWxpemVDYW52YXMgPSAoKSA9PiB7XG4gICAgICBpZiAoIWNvbnRhaW5lclJlZi5jdXJyZW50IHx8ICFjYW52YXNSZWYuY3VycmVudCkgcmV0dXJuIGZhbHNlO1xuXG4gICAgICBjb25zdCBjb250YWluZXIgPSBjb250YWluZXJSZWYuY3VycmVudDtcbiAgICAgIGNvbnN0IGNvbnRhaW5lcldpZHRoID0gY29udGFpbmVyLm9mZnNldFdpZHRoO1xuICAgICAgY29uc3QgY29udGFpbmVySGVpZ2h0ID0gY29udGFpbmVyLm9mZnNldEhlaWdodDtcblxuICAgICAgaWYgKGNvbnRhaW5lcldpZHRoID09PSAwIHx8IGNvbnRhaW5lckhlaWdodCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGNhbnZhcyA9IG5ldyBmYWJyaWMuQ2FudmFzKGNhbnZhc1JlZi5jdXJyZW50LCB7XG4gICAgICAgIGNvbnRyb2xzQWJvdmVPdmVybGF5OiB0cnVlLFxuICAgICAgICBwcmVzZXJ2ZU9iamVjdFN0YWNraW5nOiB0cnVlLFxuICAgICAgfSk7XG5cbiAgICAgIGluaXQoe1xuICAgICAgICBpbml0aWFsQ2FudmFzOiBjYW52YXMsXG4gICAgICAgIGluaXRpYWxDb250YWluZXI6IGNvbnRhaW5lcixcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gY2FudmFzO1xuICAgIH07XG5cbiAgICBjb25zdCBjYW52YXMgPSBpbml0aWFsaXplQ2FudmFzKCk7XG5cbiAgICBpZiAoIWNhbnZhcykge1xuICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGluaXRpYWxpemVDYW52YXMoKTtcbiAgICAgIH0sIDEwMCk7XG5cbiAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKTtcbiAgICB9XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKGNhbnZhcykge1xuICAgICAgICBjYW52YXMuZGlzcG9zZSgpO1xuICAgICAgfVxuICAgIH07XG4gIH0sIFtpbml0XSk7XG5cbiAgLy8gTG9jayBub24tZWRpdGFibGUgb2JqZWN0cyBhbmQgc2V0dXAgY3VzdG9taXphdGlvbiBtb2RlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFlZGl0b3I/LmNhbnZhcyB8fCAhaXNJbml0aWFsaXplZCkgcmV0dXJuO1xuXG4gICAgY29uc3QgY2FudmFzID0gZWRpdG9yLmNhbnZhcztcbiAgICBjb25zdCBlZGl0YWJsZUxheWVySWRzID0gdGVtcGxhdGVEYXRhLmVkaXRhYmxlTGF5ZXJzLm1hcChsYXllciA9PiBsYXllci5pZCk7XG5cbiAgICBjb25zb2xlLmxvZygnU2V0dGluZyB1cCBjdXN0b21pemF0aW9uIG1vZGU6Jywge1xuICAgICAgZWRpdGFibGVMYXllcklkcyxcbiAgICAgIHRvdGFsT2JqZWN0czogY2FudmFzLmdldE9iamVjdHMoKS5sZW5ndGgsXG4gICAgICBlZGl0YWJsZUxheWVyczogdGVtcGxhdGVEYXRhLmVkaXRhYmxlTGF5ZXJzXG4gICAgfSk7XG5cbiAgICAvLyBMb2NrIGFsbCBvYmplY3RzIGV4Y2VwdCBlZGl0YWJsZSBvbmVzXG4gICAgY2FudmFzLmdldE9iamVjdHMoKS5mb3JFYWNoKChvYmo6IGFueSkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1Byb2Nlc3Npbmcgb2JqZWN0OicsIHtcbiAgICAgICAgaWQ6IG9iai5pZCxcbiAgICAgICAgdHlwZTogb2JqLnR5cGUsXG4gICAgICAgIG5hbWU6IG9iai5uYW1lLFxuICAgICAgICBpc0VkaXRhYmxlOiBlZGl0YWJsZUxheWVySWRzLmluY2x1ZGVzKG9iai5pZClcbiAgICAgIH0pO1xuXG4gICAgICBpZiAob2JqLm5hbWUgPT09IFwiY2xpcFwiKSB7XG4gICAgICAgIC8vIEtlZXAgd29ya3NwYWNlIGFzIGlzIGJ1dCBtYWtlIGl0IG5vbi1zZWxlY3RhYmxlXG4gICAgICAgIG9iai5zZXQoe1xuICAgICAgICAgIHNlbGVjdGFibGU6IGZhbHNlLFxuICAgICAgICAgIGV2ZW50ZWQ6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBpZiAoZWRpdGFibGVMYXllcklkcy5pbmNsdWRlcyhvYmouaWQpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdNYWtpbmcgb2JqZWN0IGVkaXRhYmxlOicsIG9iai5pZCwgb2JqLnR5cGUpO1xuICAgICAgICAvLyBNYWtlIGVkaXRhYmxlIG9iamVjdHMgc2VsZWN0YWJsZSBhbmQgZWRpdGFibGVcbiAgICAgICAgb2JqLnNldCh7XG4gICAgICAgICAgc2VsZWN0YWJsZTogdHJ1ZSxcbiAgICAgICAgICBoYXNDb250cm9sczogZmFsc2UsIC8vIFJlbW92ZSBhbGwgY29udHJvbHMgZm9yIGNsZWFuZXIgbG9va1xuICAgICAgICAgIGhhc0JvcmRlcnM6IHRydWUsXG4gICAgICAgICAgYm9yZGVyQ29sb3I6ICcjM2I4MmY2JyxcbiAgICAgICAgICBib3JkZXJTY2FsZUZhY3RvcjogMixcbiAgICAgICAgICBsb2NrTW92ZW1lbnRYOiB0cnVlLCAvLyBMb2NrIG1vdmVtZW50IGZvciBhbGwgZWRpdGFibGUgb2JqZWN0c1xuICAgICAgICAgIGxvY2tNb3ZlbWVudFk6IHRydWUsXG4gICAgICAgICAgbG9ja1JvdGF0aW9uOiB0cnVlLFxuICAgICAgICAgIGxvY2tTY2FsaW5nWDogdHJ1ZSxcbiAgICAgICAgICBsb2NrU2NhbGluZ1k6IHRydWUsXG4gICAgICAgICAgbG9ja1VuaVNjYWxpbmc6IHRydWUsXG4gICAgICAgICAgZWRpdGFibGU6IG9iai50eXBlID09PSAndGV4dGJveCcgPyB0cnVlIDogZmFsc2UsIC8vIEVuYWJsZSB0ZXh0IGVkaXRpbmcgZm9yIHRleHRcbiAgICAgICAgICBob3ZlckN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgIG1vdmVDdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gQWRkIHZpc3VhbCBpbmRpY2F0b3IgZm9yIGVkaXRhYmxlIGVsZW1lbnRzXG4gICAgICAgIGlmIChvYmoudHlwZSA9PT0gJ3RleHRib3gnKSB7XG4gICAgICAgICAgb2JqLnNldCh7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDU5LCAxMzAsIDI0NiwgMC4xKScsIC8vIExpZ2h0IGJsdWUgYmFja2dyb3VuZFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBMb2NrIG5vbi1lZGl0YWJsZSBvYmplY3RzIGNvbXBsZXRlbHlcbiAgICAgICAgb2JqLnNldCh7XG4gICAgICAgICAgc2VsZWN0YWJsZTogZmFsc2UsXG4gICAgICAgICAgZXZlbnRlZDogZmFsc2UsXG4gICAgICAgICAgbG9ja01vdmVtZW50WDogdHJ1ZSxcbiAgICAgICAgICBsb2NrTW92ZW1lbnRZOiB0cnVlLFxuICAgICAgICAgIGxvY2tSb3RhdGlvbjogdHJ1ZSxcbiAgICAgICAgICBsb2NrU2NhbGluZ1g6IHRydWUsXG4gICAgICAgICAgbG9ja1NjYWxpbmdZOiB0cnVlLFxuICAgICAgICAgIGxvY2tVbmlTY2FsaW5nOiB0cnVlLFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcbiAgfSwgW2VkaXRvciwgaXNJbml0aWFsaXplZCwgdGVtcGxhdGVEYXRhLmVkaXRhYmxlTGF5ZXJzXSk7XG5cbiAgLy8gVXBkYXRlIHZpc3VhbCBmZWVkYmFjayBmb3IgZWRpdGFibGUgb2JqZWN0c1xuICBjb25zdCB1cGRhdGVFZGl0YWJsZU9iamVjdHNWaXN1YWxzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghZWRpdG9yPy5jYW52YXMpIHJldHVybjtcblxuICAgIGNvbnN0IGVkaXRhYmxlTGF5ZXJJZHMgPSB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMubWFwKGxheWVyID0+IGxheWVyLmlkKTtcblxuICAgIGVkaXRvci5jYW52YXMuZ2V0T2JqZWN0cygpLmZvckVhY2goKG9iajogYW55KSA9PiB7XG4gICAgICBpZiAoZWRpdGFibGVMYXllcklkcy5pbmNsdWRlcyhvYmouaWQpKSB7XG4gICAgICAgIGNvbnN0IGlzQWN0aXZlID0gb2JqLmlkID09PSBhY3RpdmVMYXllcklkO1xuXG4gICAgICAgIGlmIChvYmoudHlwZSA9PT0gJ3RleHRib3gnKSB7XG4gICAgICAgICAgb2JqLnNldCh7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGlzQWN0aXZlXG4gICAgICAgICAgICAgID8gJ3JnYmEoNTksIDEzMCwgMjQ2LCAwLjIpJyAvLyBEYXJrZXIgYmx1ZSB3aGVuIGFjdGl2ZVxuICAgICAgICAgICAgICA6ICdyZ2JhKDU5LCAxMzAsIDI0NiwgMC4xKScsIC8vIExpZ2h0IGJsdWUgd2hlbiBpbmFjdGl2ZVxuICAgICAgICAgICAgYm9yZGVyQ29sb3I6IGlzQWN0aXZlID8gJyMzYjgyZjYnIDogJyM5NGEzYjgnLFxuICAgICAgICAgICAgYm9yZGVyU2NhbGVGYWN0b3I6IGlzQWN0aXZlID8gMyA6IDIsXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gRm9yIGltYWdlIGxheWVyc1xuICAgICAgICAgIG9iai5zZXQoe1xuICAgICAgICAgICAgYm9yZGVyQ29sb3I6IGlzQWN0aXZlID8gJyMzYjgyZjYnIDogJyM5NGEzYjgnLFxuICAgICAgICAgICAgYm9yZGVyU2NhbGVGYWN0b3I6IGlzQWN0aXZlID8gMyA6IDIsXG4gICAgICAgICAgICBvcGFjaXR5OiBpc0FjdGl2ZSA/IDEgOiAwLjgsXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGVkaXRvci5jYW52YXMucmVuZGVyQWxsKCk7XG4gIH0sIFtlZGl0b3IsIHRlbXBsYXRlRGF0YS5lZGl0YWJsZUxheWVycywgYWN0aXZlTGF5ZXJJZF0pO1xuXG4gIC8vIFVwZGF0ZSB2aXN1YWxzIHdoZW4gYWN0aXZlIGxheWVyIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICB1cGRhdGVFZGl0YWJsZU9iamVjdHNWaXN1YWxzKCk7XG4gIH0sIFthY3RpdmVMYXllcklkLCB1cGRhdGVFZGl0YWJsZU9iamVjdHNWaXN1YWxzXSk7XG5cbiAgLy8gQXBwbHkgY3VzdG9taXphdGlvbnMgdG8gY2FudmFzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFlZGl0b3I/LmNhbnZhcyB8fCAhaXNJbml0aWFsaXplZCkgcmV0dXJuO1xuXG4gICAgY29uc3QgY2FudmFzID0gZWRpdG9yLmNhbnZhcztcblxuICAgIHRlbXBsYXRlRGF0YS5lZGl0YWJsZUxheWVycy5mb3JFYWNoKGxheWVyID0+IHtcbiAgICAgIGNvbnN0IGN1c3RvbVZhbHVlID0gY3VzdG9taXphdGlvbnNbbGF5ZXIuaWRdO1xuICAgICAgaWYgKCFjdXN0b21WYWx1ZSB8fCBjdXN0b21WYWx1ZSA9PT0gbGF5ZXIub3JpZ2luYWxWYWx1ZSkgcmV0dXJuO1xuXG4gICAgICBjb25zdCBjYW52YXNPYmplY3QgPSBjYW52YXMuZ2V0T2JqZWN0cygpLmZpbmQoKG9iajogYW55KSA9PiBvYmouaWQgPT09IGxheWVyLmlkKTtcbiAgICAgIGlmICghY2FudmFzT2JqZWN0KSByZXR1cm47XG5cbiAgICAgIGlmIChsYXllci50eXBlID09PSAndGV4dCcgJiYgY2FudmFzT2JqZWN0LnR5cGUgPT09ICd0ZXh0Ym94Jykge1xuICAgICAgICAoY2FudmFzT2JqZWN0IGFzIGZhYnJpYy5UZXh0Ym94KS5zZXQoJ3RleHQnLCBjdXN0b21WYWx1ZSk7XG4gICAgICB9IGVsc2UgaWYgKGxheWVyLnR5cGUgPT09ICdpbWFnZScgJiYgY3VzdG9tVmFsdWUuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgICBmYWJyaWMuSW1hZ2UuZnJvbVVSTChjdXN0b21WYWx1ZSwgKGltZykgPT4ge1xuICAgICAgICAgIGlmICghY2FudmFzKSByZXR1cm47XG5cbiAgICAgICAgICAvLyBTY2FsZSBpbWFnZSB0byBmaXQgdGhlIG9iamVjdCBib3VuZHNcbiAgICAgICAgICBjb25zdCBzY2FsZVggPSBjYW52YXNPYmplY3Qud2lkdGghIC8gaW1nLndpZHRoITtcbiAgICAgICAgICBjb25zdCBzY2FsZVkgPSBjYW52YXNPYmplY3QuaGVpZ2h0ISAvIGltZy5oZWlnaHQhO1xuICAgICAgICAgIGNvbnN0IHNjYWxlID0gTWF0aC5taW4oc2NhbGVYLCBzY2FsZVkpO1xuXG4gICAgICAgICAgaW1nLnNldCh7XG4gICAgICAgICAgICBsZWZ0OiBjYW52YXNPYmplY3QubGVmdCxcbiAgICAgICAgICAgIHRvcDogY2FudmFzT2JqZWN0LnRvcCxcbiAgICAgICAgICAgIHNjYWxlWDogc2NhbGUsXG4gICAgICAgICAgICBzY2FsZVk6IHNjYWxlLFxuICAgICAgICAgICAgb3JpZ2luWDogY2FudmFzT2JqZWN0Lm9yaWdpblgsXG4gICAgICAgICAgICBvcmlnaW5ZOiBjYW52YXNPYmplY3Qub3JpZ2luWSxcbiAgICAgICAgICAgIHNlbGVjdGFibGU6IHRydWUsXG4gICAgICAgICAgICBoYXNDb250cm9sczogZmFsc2UsXG4gICAgICAgICAgICBsb2NrTW92ZW1lbnRYOiB0cnVlLFxuICAgICAgICAgICAgbG9ja01vdmVtZW50WTogdHJ1ZSxcbiAgICAgICAgICAgIGxvY2tSb3RhdGlvbjogdHJ1ZSxcbiAgICAgICAgICAgIGxvY2tTY2FsaW5nWDogdHJ1ZSxcbiAgICAgICAgICAgIGxvY2tTY2FsaW5nWTogdHJ1ZSxcbiAgICAgICAgICAgIGxvY2tVbmlTY2FsaW5nOiB0cnVlLFxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgLy8gU2V0IHRoZSBJRCB1c2luZyBhIGN1c3RvbSBwcm9wZXJ0eVxuICAgICAgICAgIChpbWcgYXMgYW55KS5pZCA9IGxheWVyLmlkO1xuXG4gICAgICAgICAgY2FudmFzLnJlbW92ZShjYW52YXNPYmplY3QpO1xuICAgICAgICAgIGNhbnZhcy5hZGQoaW1nKTtcbiAgICAgICAgICBjYW52YXMucmVuZGVyQWxsKCk7XG4gICAgICAgICAgZ2VuZXJhdGVQcmV2aWV3KCk7XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBjYW52YXMucmVuZGVyQWxsKCk7XG4gICAgZ2VuZXJhdGVQcmV2aWV3KCk7XG4gIH0sIFtjdXN0b21pemF0aW9ucywgZWRpdG9yLCBpc0luaXRpYWxpemVkLCB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnNdKTtcblxuICAvLyBHZW5lcmF0ZSBwcmV2aWV3IGZyb20gY2FudmFzXG4gIGNvbnN0IGdlbmVyYXRlUHJldmlldyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoIWVkaXRvcj8uY2FudmFzKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3Qgd29ya3NwYWNlID0gZWRpdG9yLmNhbnZhcy5nZXRPYmplY3RzKCkuZmluZCgob2JqOiBhbnkpID0+IG9iai5uYW1lID09PSBcImNsaXBcIik7XG4gICAgICBpZiAoIXdvcmtzcGFjZSkgcmV0dXJuO1xuXG4gICAgICAvLyBDcmVhdGUgYSB0ZW1wb3JhcnkgY2FudmFzIHdpdGgganVzdCB0aGUgd29ya3NwYWNlIGNvbnRlbnRcbiAgICAgIGNvbnN0IHRlbXBDYW52YXMgPSBuZXcgZmFicmljLkNhbnZhcyhudWxsLCB7XG4gICAgICAgIHdpZHRoOiB3b3Jrc3BhY2Uud2lkdGgsXG4gICAgICAgIGhlaWdodDogd29ya3NwYWNlLmhlaWdodCxcbiAgICAgIH0pO1xuXG4gICAgICAvLyBDbG9uZSBhbGwgb2JqZWN0cyBleGNlcHQgdGhlIHdvcmtzcGFjZSBpdHNlbGZcbiAgICAgIGNvbnN0IG9iamVjdHNUb0Nsb25lID0gZWRpdG9yLmNhbnZhcy5nZXRPYmplY3RzKCkuZmlsdGVyKChvYmo6IGFueSkgPT4gb2JqLm5hbWUgIT09IFwiY2xpcFwiKTtcbiAgICAgIFxuICAgICAgb2JqZWN0c1RvQ2xvbmUuZm9yRWFjaCgob2JqKSA9PiB7XG4gICAgICAgIG9iai5jbG9uZSgoY2xvbmVkOiBmYWJyaWMuT2JqZWN0KSA9PiB7XG4gICAgICAgICAgLy8gQWRqdXN0IHBvc2l0aW9uIHJlbGF0aXZlIHRvIHdvcmtzcGFjZVxuICAgICAgICAgIGNsb25lZC5zZXQoe1xuICAgICAgICAgICAgbGVmdDogKGNsb25lZC5sZWZ0IHx8IDApIC0gKHdvcmtzcGFjZS5sZWZ0IHx8IDApLFxuICAgICAgICAgICAgdG9wOiAoY2xvbmVkLnRvcCB8fCAwKSAtICh3b3Jrc3BhY2UudG9wIHx8IDApLFxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHRlbXBDYW52YXMuYWRkKGNsb25lZCk7XG4gICAgICAgIH0pO1xuICAgICAgfSk7XG5cbiAgICAgIC8vIEdlbmVyYXRlIHByZXZpZXcgYWZ0ZXIgYSBzaG9ydCBkZWxheSB0byBlbnN1cmUgYWxsIG9iamVjdHMgYXJlIGFkZGVkXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgY29uc3QgZGF0YVVybCA9IHRlbXBDYW52YXMudG9EYXRhVVJMKHtcbiAgICAgICAgICBmb3JtYXQ6ICdwbmcnLFxuICAgICAgICAgIHF1YWxpdHk6IDAuOSxcbiAgICAgICAgICBtdWx0aXBsaWVyOiAxLFxuICAgICAgICB9KTtcbiAgICAgICAgb25QcmV2aWV3R2VuZXJhdGVkKGRhdGFVcmwpO1xuICAgICAgICB0ZW1wQ2FudmFzLmRpc3Bvc2UoKTtcbiAgICAgIH0sIDEwMCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZW5lcmF0ZSBwcmV2aWV3OicsIGVycm9yKTtcbiAgICB9XG4gIH0sIFtlZGl0b3IsIG9uUHJldmlld0dlbmVyYXRlZF0pO1xuXG4gIC8vIEhhbmRsZSB0ZXh0IGVkaXRpbmdcbiAgY29uc3QgaGFuZGxlVGV4dEVkaXQgPSB1c2VDYWxsYmFjaygobGF5ZXJJZDogc3RyaW5nLCB2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgb25DdXN0b21pemF0aW9uQ2hhbmdlKGxheWVySWQsIHZhbHVlKTtcbiAgfSwgW29uQ3VzdG9taXphdGlvbkNoYW5nZV0pO1xuXG5cblxuICAvLyBIYW5kbGUgY2xpY2sgZXZlbnRzIGFuZCBsYXllciBhY3RpdmF0aW9uXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFlZGl0b3I/LmNhbnZhcykgcmV0dXJuO1xuXG4gICAgY29uc3QgaGFuZGxlT2JqZWN0U2VsZWN0aW9uID0gKGU6IGZhYnJpYy5JRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0O1xuICAgICAgaWYgKCF0YXJnZXQpIHtcbiAgICAgICAgc2V0QWN0aXZlTGF5ZXJJZChudWxsKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBsYXllcklkID0gKHRhcmdldCBhcyBhbnkpLmlkO1xuICAgICAgY29uc3QgbGF5ZXIgPSB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMuZmluZChsID0+IGwuaWQgPT09IGxheWVySWQpO1xuXG4gICAgICBpZiAobGF5ZXIpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0xheWVyIGFjdGl2YXRlZDonLCBsYXllcik7XG4gICAgICAgIHNldEFjdGl2ZUxheWVySWQobGF5ZXJJZCk7XG4gICAgICAgIG9uTGF5ZXJBY3RpdmF0aW9uPy4obGF5ZXJJZCk7XG5cbiAgICAgICAgLy8gVXBkYXRlIHZpc3VhbCBmZWVkYmFjayBmb3IgYWxsIGVkaXRhYmxlIG9iamVjdHNcbiAgICAgICAgdXBkYXRlRWRpdGFibGVPYmplY3RzVmlzdWFscygpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0QWN0aXZlTGF5ZXJJZChudWxsKTtcbiAgICAgICAgb25MYXllckFjdGl2YXRpb24/LihudWxsKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgY29uc3QgaGFuZGxlQ2FudmFzQ2xpY2sgPSAoZTogZmFicmljLklFdmVudCkgPT4ge1xuICAgICAgaWYgKCFlLnRhcmdldCkge1xuICAgICAgICBzZXRBY3RpdmVMYXllcklkKG51bGwpO1xuICAgICAgICBvbkxheWVyQWN0aXZhdGlvbj8uKG51bGwpO1xuICAgICAgICB1cGRhdGVFZGl0YWJsZU9iamVjdHNWaXN1YWxzKCk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGNvbnN0IGhhbmRsZVRleHRDaGFuZ2VkID0gKGU6IGZhYnJpYy5JRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0O1xuICAgICAgaWYgKCF0YXJnZXQgfHwgdGFyZ2V0LnR5cGUgIT09ICd0ZXh0Ym94JykgcmV0dXJuO1xuXG4gICAgICBjb25zdCBsYXllcklkID0gKHRhcmdldCBhcyBhbnkpLmlkO1xuICAgICAgY29uc3QgbGF5ZXIgPSB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMuZmluZChsID0+IGwuaWQgPT09IGxheWVySWQpO1xuICAgICAgaWYgKCFsYXllciB8fCBsYXllci50eXBlICE9PSAndGV4dCcpIHJldHVybjtcblxuICAgICAgY29uc3QgY3VycmVudFRleHQgPSAodGFyZ2V0IGFzIGZhYnJpYy5UZXh0Ym94KS50ZXh0IHx8ICcnO1xuICAgICAgaGFuZGxlVGV4dEVkaXQobGF5ZXJJZCwgY3VycmVudFRleHQpO1xuICAgICAgZ2VuZXJhdGVQcmV2aWV3KCk7XG4gICAgfTtcblxuICAgIGNvbnN0IGhhbmRsZURvdWJsZUNsaWNrID0gKGU6IGZhYnJpYy5JRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0O1xuICAgICAgaWYgKCF0YXJnZXQgfHwgdGFyZ2V0LnR5cGUgIT09ICd0ZXh0Ym94JykgcmV0dXJuO1xuXG4gICAgICBjb25zdCBsYXllcklkID0gKHRhcmdldCBhcyBhbnkpLmlkO1xuICAgICAgY29uc3QgbGF5ZXIgPSB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMuZmluZChsID0+IGwuaWQgPT09IGxheWVySWQpO1xuICAgICAgaWYgKGxheWVyICYmIGxheWVyLnR5cGUgPT09ICd0ZXh0Jykge1xuICAgICAgICBzZXRBY3RpdmVMYXllcklkKGxheWVySWQpO1xuICAgICAgICAvLyBFbnRlciBlZGl0aW5nIG1vZGVcbiAgICAgICAgKHRhcmdldCBhcyBmYWJyaWMuVGV4dGJveCkuZW50ZXJFZGl0aW5nKCk7XG4gICAgICAgICh0YXJnZXQgYXMgZmFicmljLlRleHRib3gpLnNlbGVjdEFsbCgpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBlZGl0b3IuY2FudmFzLm9uKCdzZWxlY3Rpb246Y3JlYXRlZCcsIGhhbmRsZU9iamVjdFNlbGVjdGlvbik7XG4gICAgZWRpdG9yLmNhbnZhcy5vbignc2VsZWN0aW9uOnVwZGF0ZWQnLCBoYW5kbGVPYmplY3RTZWxlY3Rpb24pO1xuICAgIGVkaXRvci5jYW52YXMub24oJ3NlbGVjdGlvbjpjbGVhcmVkJywgKCkgPT4gc2V0QWN0aXZlTGF5ZXJJZChudWxsKSk7XG4gICAgZWRpdG9yLmNhbnZhcy5vbignbW91c2U6ZG93bicsIGhhbmRsZUNhbnZhc0NsaWNrKTtcbiAgICBlZGl0b3IuY2FudmFzLm9uKCd0ZXh0OmNoYW5nZWQnLCBoYW5kbGVUZXh0Q2hhbmdlZCk7XG4gICAgZWRpdG9yLmNhbnZhcy5vbignbW91c2U6ZGJsY2xpY2snLCBoYW5kbGVEb3VibGVDbGljayk7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZWRpdG9yLmNhbnZhcy5vZmYoJ3NlbGVjdGlvbjpjcmVhdGVkJywgaGFuZGxlT2JqZWN0U2VsZWN0aW9uKTtcbiAgICAgIGVkaXRvci5jYW52YXMub2ZmKCdzZWxlY3Rpb246dXBkYXRlZCcsIGhhbmRsZU9iamVjdFNlbGVjdGlvbik7XG4gICAgICBlZGl0b3IuY2FudmFzLm9mZignc2VsZWN0aW9uOmNsZWFyZWQnLCAoKSA9PiBzZXRBY3RpdmVMYXllcklkKG51bGwpKTtcbiAgICAgIGVkaXRvci5jYW52YXMub2ZmKCdtb3VzZTpkb3duJywgaGFuZGxlQ2FudmFzQ2xpY2spO1xuICAgICAgZWRpdG9yLmNhbnZhcy5vZmYoJ3RleHQ6Y2hhbmdlZCcsIGhhbmRsZVRleHRDaGFuZ2VkKTtcbiAgICAgIGVkaXRvci5jYW52YXMub2ZmKCdtb3VzZTpkYmxjbGljaycsIGhhbmRsZURvdWJsZUNsaWNrKTtcbiAgICB9O1xuICB9LCBbZWRpdG9yLCB0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMsIGhhbmRsZVRleHRFZGl0LCBnZW5lcmF0ZVByZXZpZXddKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggZmxleC1jb2wgYmctbXV0ZWRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiIHJlZj17Y29udGFpbmVyUmVmfT5cbiAgICAgICAgICA8Y2FudmFzIHJlZj17Y2FudmFzUmVmfSAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgPEZvb3RlciBlZGl0b3I9e2VkaXRvcn0gLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlQ2FsbGJhY2siLCJmYWJyaWMiLCJ1c2VFZGl0b3IiLCJGb290ZXIiLCJDdXN0b21pemF0aW9uRWRpdG9yIiwidGVtcGxhdGVEYXRhIiwiY3VzdG9taXphdGlvbnMiLCJvbkN1c3RvbWl6YXRpb25DaGFuZ2UiLCJvblByZXZpZXdHZW5lcmF0ZWQiLCJhY3RpdmVMYXllcklkIiwiZXh0ZXJuYWxBY3RpdmVMYXllcklkIiwib25MYXllckFjdGl2YXRpb24iLCJjYW52YXNSZWYiLCJjb250YWluZXJSZWYiLCJpbml0IiwiZWRpdG9yIiwiZGVmYXVsdFN0YXRlIiwianNvbiIsImRlZmF1bHRXaWR0aCIsIndpZHRoIiwiZGVmYXVsdEhlaWdodCIsImhlaWdodCIsImNsZWFyU2VsZWN0aW9uQ2FsbGJhY2siLCJzYXZlQ2FsbGJhY2siLCJnZW5lcmF0ZVByZXZpZXciLCJpbml0aWFsaXplQ2FudmFzIiwiY3VycmVudCIsImNvbnRhaW5lciIsImNvbnRhaW5lcldpZHRoIiwib2Zmc2V0V2lkdGgiLCJjb250YWluZXJIZWlnaHQiLCJvZmZzZXRIZWlnaHQiLCJjYW52YXMiLCJDYW52YXMiLCJjb250cm9sc0Fib3ZlT3ZlcmxheSIsInByZXNlcnZlT2JqZWN0U3RhY2tpbmciLCJpbml0aWFsQ2FudmFzIiwiaW5pdGlhbENvbnRhaW5lciIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJkaXNwb3NlIiwiaXNJbml0aWFsaXplZCIsImVkaXRhYmxlTGF5ZXJJZHMiLCJlZGl0YWJsZUxheWVycyIsIm1hcCIsImxheWVyIiwiaWQiLCJjb25zb2xlIiwibG9nIiwidG90YWxPYmplY3RzIiwiZ2V0T2JqZWN0cyIsImxlbmd0aCIsImZvckVhY2giLCJvYmoiLCJ0eXBlIiwibmFtZSIsImlzRWRpdGFibGUiLCJpbmNsdWRlcyIsInNldCIsInNlbGVjdGFibGUiLCJldmVudGVkIiwiaGFzQ29udHJvbHMiLCJoYXNCb3JkZXJzIiwiYm9yZGVyQ29sb3IiLCJib3JkZXJTY2FsZUZhY3RvciIsImxvY2tNb3ZlbWVudFgiLCJsb2NrTW92ZW1lbnRZIiwibG9ja1JvdGF0aW9uIiwibG9ja1NjYWxpbmdYIiwibG9ja1NjYWxpbmdZIiwibG9ja1VuaVNjYWxpbmciLCJlZGl0YWJsZSIsImhvdmVyQ3Vyc29yIiwibW92ZUN1cnNvciIsImJhY2tncm91bmRDb2xvciIsInJlbmRlckFsbCIsInVwZGF0ZUVkaXRhYmxlT2JqZWN0c1Zpc3VhbHMiLCJpc0FjdGl2ZSIsIm9wYWNpdHkiLCJjdXN0b21WYWx1ZSIsIm9yaWdpbmFsVmFsdWUiLCJjYW52YXNPYmplY3QiLCJmaW5kIiwic3RhcnRzV2l0aCIsIkltYWdlIiwiZnJvbVVSTCIsImltZyIsInNjYWxlWCIsInNjYWxlWSIsInNjYWxlIiwiTWF0aCIsIm1pbiIsImxlZnQiLCJ0b3AiLCJvcmlnaW5YIiwib3JpZ2luWSIsInJlbW92ZSIsImFkZCIsIndvcmtzcGFjZSIsInRlbXBDYW52YXMiLCJvYmplY3RzVG9DbG9uZSIsImZpbHRlciIsImNsb25lIiwiY2xvbmVkIiwiZGF0YVVybCIsInRvRGF0YVVSTCIsImZvcm1hdCIsInF1YWxpdHkiLCJtdWx0aXBsaWVyIiwiZXJyb3IiLCJoYW5kbGVUZXh0RWRpdCIsImxheWVySWQiLCJ2YWx1ZSIsImhhbmRsZU9iamVjdFNlbGVjdGlvbiIsImUiLCJ0YXJnZXQiLCJzZXRBY3RpdmVMYXllcklkIiwibCIsImhhbmRsZUNhbnZhc0NsaWNrIiwiaGFuZGxlVGV4dENoYW5nZWQiLCJjdXJyZW50VGV4dCIsInRleHQiLCJoYW5kbGVEb3VibGVDbGljayIsImVudGVyRWRpdGluZyIsInNlbGVjdEFsbCIsIm9uIiwib2ZmIiwiZGl2IiwiY2xhc3NOYW1lIiwicmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});