"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/project-card.tsx":
/*!**********************************************!*\
  !*** ./src/app/(dashboard)/project-card.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectCard: function() { return /* binding */ ProjectCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.mjs\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\n\n\n\n\n\nconst ProjectCard = (param)=>{\n    let { id, name, width, height, thumbnailUrl, updatedAt, isPublic = false, isCustomizable = false, onClick, onCopy, onDelete, onTogglePublic, onConfigureTemplate, copyLoading = false, deleteLoading = false, togglePublicLoading = false } = param;\n    // Debug thumbnail data\n    console.log(\"ProjectCard render:\", {\n        name,\n        thumbnailUrl: thumbnailUrl ? \"HAS_THUMBNAIL (\".concat(thumbnailUrl.length, \" chars)\") : \"NO_THUMBNAIL\",\n        thumbnailPreview: thumbnailUrl ? thumbnailUrl.substring(0, 50) + \"...\" : null\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group flex flex-col space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: onClick,\n                className: \"relative w-full cursor-pointer overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.03] hover:border-purple-200\",\n                style: {\n                    aspectRatio: \"\".concat(width, \"/\").concat(height)\n                },\n                children: [\n                    thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: thumbnailUrl,\n                        alt: name,\n                        fill: true,\n                        className: \"object-contain bg-white\",\n                        onError: (e)=>{\n                            console.error(\"Thumbnail failed to load:\", thumbnailUrl);\n                            console.error(\"Error:\", e);\n                        },\n                        onLoad: ()=>{\n                            console.log(\"Thumbnail loaded successfully:\", (thumbnailUrl === null || thumbnailUrl === void 0 ? void 0 : thumbnailUrl.substring(0, 50)) + \"...\");\n                        },\n                        sizes: \"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw\",\n                        quality: 100,\n                        priority: false,\n                        unoptimized: true,\n                        style: {\n                            imageRendering: \"crisp-edges\",\n                            WebkitImageRendering: \"crisp-edges\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full w-full items-center justify-center bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, undefined),\n                    isPublic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Public\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined),\n                    isCustomizable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Template\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/0 to-black/0 transition-all duration-200 group-hover:from-black/5 group-hover:to-black/0 rounded-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-3 opacity-0 transition-all duration-200 group-hover:opacity-100 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs font-semibold text-white truncate\",\n                            children: [\n                                width,\n                                \" \\xd7 \",\n                                height,\n                                \" px\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                onClick: onClick,\n                                className: \"cursor-pointer font-semibold text-sm text-gray-900 truncate hover:text-purple-600 transition-colors leading-tight\",\n                                children: name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 font-medium\",\n                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__.formatDistanceToNow)(updatedAt, {\n                                    addSuffix: true\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenu, {\n                        modal: false,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"icon\",\n                                    variant: \"ghost\",\n                                    className: \"h-8 w-8 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-gray-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuContent, {\n                                align: \"end\",\n                                className: \"w-48\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        disabled: copyLoading,\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onCopy(id);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make a copy\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        disabled: togglePublicLoading,\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onTogglePublic(id, !isPublic);\n                                        },\n                                        children: isPublic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Make private\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Make public\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onConfigureTemplate(id);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Template Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer text-red-600 focus:text-red-600\",\n                                        disabled: deleteLoading,\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onDelete(id);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Delete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ProjectCard;\nvar _c;\n$RefreshReg$(_c, \"ProjectCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/project-card.tsx\n"));

/***/ })

});