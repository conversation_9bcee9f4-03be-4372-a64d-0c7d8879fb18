"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/project-card.tsx":
/*!**********************************************!*\
  !*** ./src/app/(dashboard)/project-card.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectCard: function() { return /* binding */ ProjectCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.mjs\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\n\n\n\n\n\nconst ProjectCard = (param)=>{\n    let { id, name, width, height, thumbnailUrl, updatedAt, isPublic = false, isCustomizable = false, onClick, onCopy, onDelete, onTogglePublic, onConfigureTemplate, copyLoading = false, deleteLoading = false, togglePublicLoading = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group flex flex-col space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: onClick,\n                className: \"relative w-full cursor-pointer overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.03] hover:border-purple-200\",\n                style: {\n                    aspectRatio: \"\".concat(width, \"/\").concat(height)\n                },\n                children: [\n                    thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: thumbnailUrl,\n                        alt: name,\n                        fill: true,\n                        className: \"object-contain bg-white\",\n                        onError: (e)=>{\n                            console.error(\"Thumbnail failed to load:\", thumbnailUrl);\n                            console.error(\"Error:\", e);\n                        },\n                        onLoad: ()=>{\n                            console.log(\"Thumbnail loaded successfully:\", (thumbnailUrl === null || thumbnailUrl === void 0 ? void 0 : thumbnailUrl.substring(0, 50)) + \"...\");\n                        },\n                        sizes: \"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw\",\n                        quality: 100,\n                        priority: false,\n                        unoptimized: false,\n                        style: {\n                            imageRendering: \"crisp-edges\",\n                            WebkitImageRendering: \"crisp-edges\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full w-full items-center justify-center bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined),\n                    isPublic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Public\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, undefined),\n                    isCustomizable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Template\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/0 to-black/0 transition-all duration-200 group-hover:from-black/5 group-hover:to-black/0 rounded-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-3 opacity-0 transition-all duration-200 group-hover:opacity-100 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs font-semibold text-white truncate\",\n                            children: [\n                                width,\n                                \" \\xd7 \",\n                                height,\n                                \" px\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                onClick: onClick,\n                                className: \"cursor-pointer font-semibold text-sm text-gray-900 truncate hover:text-purple-600 transition-colors leading-tight\",\n                                children: name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 font-medium\",\n                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__.formatDistanceToNow)(updatedAt, {\n                                    addSuffix: true\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenu, {\n                        modal: false,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"icon\",\n                                    variant: \"ghost\",\n                                    className: \"h-8 w-8 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-gray-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuContent, {\n                                align: \"end\",\n                                className: \"w-48\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        disabled: copyLoading,\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onCopy(id);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make a copy\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        disabled: togglePublicLoading,\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onTogglePublic(id, !isPublic);\n                                        },\n                                        children: isPublic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Make private\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Make public\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onConfigureTemplate(id);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Template Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer text-red-600 focus:text-red-600\",\n                                        disabled: deleteLoading,\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onDelete(id);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Delete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ProjectCard;\nvar _c;\n$RefreshReg$(_c, \"ProjectCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/project-card.tsx\n"));

/***/ })

});