"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts":
/*!**************************************************************!*\
  !*** ./src/features/editor/hooks/use-thumbnail-generator.ts ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useThumbnailGenerator: function() { return /* binding */ useThumbnailGenerator; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hono__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hono */ \"(app-pages-browser)/./src/lib/hono.ts\");\n\n\n\nconst useThumbnailGenerator = (param)=>{\n    let { editor, projectId, onThumbnailGenerated } = param;\n    const lastThumbnailRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const generateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!editor) {\n            console.warn(\"No editor available for thumbnail generation\");\n            return;\n        }\n        if (!editor.canvas) {\n            console.warn(\"No canvas available for thumbnail generation\");\n            return;\n        }\n        try {\n            console.log(\"Generating thumbnail for project:\", projectId);\n            // Generate thumbnail data URL with ultra-high quality (Canva-level)\n            const thumbnailDataUrl = editor.generateThumbnail({\n                width: 600,\n                height: 400,\n                quality: 1.0\n            });\n            if (!thumbnailDataUrl) {\n                console.error(\"Failed to generate thumbnail data URL\");\n                return;\n            }\n            // Skip if thumbnail hasn't changed significantly\n            if (lastThumbnailRef.current === thumbnailDataUrl) {\n                console.log(\"Thumbnail unchanged, skipping update\");\n                return;\n            }\n            lastThumbnailRef.current = thumbnailDataUrl;\n            console.log(\"Thumbnail generated, updating project...\");\n            // For now, let's store the thumbnail as a data URL directly in the database\n            // This is simpler and doesn't require external file uploads\n            // In production, you might want to upload to a CDN\n            // Update project with thumbnail data URL\n            const response = await _lib_hono__WEBPACK_IMPORTED_MODULE_2__.client.api.projects[\":id\"].$patch({\n                param: {\n                    id: projectId\n                },\n                json: {\n                    thumbnailUrl: thumbnailDataUrl\n                }\n            });\n            if (response.ok) {\n                console.log(\"Thumbnail updated successfully\");\n                onThumbnailGenerated === null || onThumbnailGenerated === void 0 ? void 0 : onThumbnailGenerated(thumbnailDataUrl);\n            } else {\n                console.error(\"Failed to update thumbnail:\", response.status, response.statusText);\n            }\n        } catch (error) {\n            console.error(\"Error generating thumbnail:\", error);\n        }\n    }, [\n        editor,\n        projectId,\n        onThumbnailGenerated\n    ]);\n    // Debounced version to avoid too frequent thumbnail generation\n    const debouncedGenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default()(generateThumbnail, 2000), [\n        generateThumbnail\n    ]);\n    const forceRegenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        // Reset the last thumbnail to force regeneration\n        lastThumbnailRef.current = null;\n        await generateThumbnail();\n    }, [\n        generateThumbnail\n    ]);\n    return {\n        generateThumbnail,\n        debouncedGenerateThumbnail,\n        forceRegenerateThumbnail\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\n"));

/***/ })

});