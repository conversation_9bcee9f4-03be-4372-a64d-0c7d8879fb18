"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx":
/*!********************************************************************!*\
  !*** ./src/features/editor/components/template-config-sidebar.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateConfigSidebar: function() { return /* binding */ TemplateConfigSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/projects/api/use-update-template-config */ \"(app-pages-browser)/./src/features/projects/api/use-update-template-config.ts\");\n/* __next_internal_client_entry_do_not_use__ TemplateConfigSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst TemplateConfigSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool, projectId, initialData } = param;\n    _s();\n    const [editableLayers, setEditableLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCustomizable, setIsCustomizable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasLoadedInitialData, setHasLoadedInitialData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateTemplateConfig = (0,_features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig)(projectId || \"\");\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    const canvas = editor === null || editor === void 0 ? void 0 : editor.canvas;\n    const selectedObjects = (editor === null || editor === void 0 ? void 0 : editor.selectedObjects) || [];\n    // Load existing template configuration from database (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData && !hasLoadedInitialData) {\n            setIsCustomizable(initialData.isCustomizable || false);\n            if (initialData.editableLayers) {\n                try {\n                    const parsedLayers = JSON.parse(initialData.editableLayers);\n                    setEditableLayers(parsedLayers);\n                } catch (error) {\n                    console.error(\"Failed to parse editable layers:\", error);\n                }\n            }\n            setHasLoadedInitialData(true);\n        }\n    }, [\n        initialData,\n        hasLoadedInitialData\n    ]);\n    // Apply editable properties to canvas objects when canvas is ready and layers are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (canvas && editableLayers.length > 0 && hasLoadedInitialData) {\n            editableLayers.forEach((layer)=>{\n                const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n                if (canvasObject) {\n                    canvasObject.isEditable = true;\n                    canvasObject.editableType = layer.type;\n                    canvasObject.editableName = layer.name;\n                    canvasObject.editablePlaceholder = layer.placeholder;\n                    canvasObject.editableConstraints = layer.constraints;\n                }\n            });\n            canvas.renderAll();\n        }\n    }, [\n        canvas,\n        editableLayers,\n        hasLoadedInitialData\n    ]);\n    // Load existing editable layers from canvas objects (fallback)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvas || editableLayers.length > 0) return;\n        const layers = [];\n        canvas.getObjects().forEach((obj)=>{\n            const editableObj = obj;\n            if (editableObj.isEditable) {\n                layers.push({\n                    id: editableObj.id || \"\",\n                    type: editableObj.editableType || \"text\",\n                    name: editableObj.editableName || \"Unnamed Layer\",\n                    originalValue: editableObj.type === \"textbox\" ? editableObj.text : \"\",\n                    placeholder: editableObj.editablePlaceholder,\n                    constraints: editableObj.editableConstraints\n                });\n            }\n        });\n        if (layers.length > 0) {\n            setEditableLayers(layers);\n            setIsCustomizable(true);\n        }\n    }, [\n        canvas,\n        editableLayers.length\n    ]);\n    const makeLayerEditable = (type)=>{\n        if (!canvas || selectedObjects.length === 0) return;\n        const selectedObject = selectedObjects[0];\n        // Validate object type\n        if (type === \"text\" && selectedObject.type !== \"textbox\") {\n            alert(\"Please select a text object to make it editable\");\n            return;\n        }\n        if (type === \"image\" && ![\n            \"image\",\n            \"rect\",\n            \"circle\"\n        ].includes(selectedObject.type || \"\")) {\n            alert(\"Please select an image or shape to make it editable\");\n            return;\n        }\n        // Mark object as editable\n        selectedObject.isEditable = true;\n        selectedObject.editableType = type;\n        selectedObject.editableName = \"\".concat(type === \"text\" ? \"Text\" : \"Image\", \" \").concat(editableLayers.length + 1);\n        if (type === \"text\") {\n            selectedObject.editablePlaceholder = \"Enter your text here...\";\n        }\n        // Add to editable layers list\n        const newLayer = {\n            id: selectedObject.id || \"layer_\".concat(Date.now()),\n            type,\n            name: selectedObject.editableName,\n            originalValue: type === \"text\" ? selectedObject.text : \"\",\n            placeholder: selectedObject.editablePlaceholder,\n            constraints: {\n                maxLength: type === \"text\" ? 100 : undefined,\n                allowedFormats: type === \"image\" ? [\n                    \"jpg\",\n                    \"jpeg\",\n                    \"png\",\n                    \"gif\"\n                ] : undefined,\n                maxFileSize: type === \"image\" ? 5 * 1024 * 1024 : undefined\n            }\n        };\n        // Assign ID if not exists\n        if (!selectedObject.id) {\n            selectedObject.id = newLayer.id;\n        }\n        setEditableLayers([\n            ...editableLayers,\n            newLayer\n        ]);\n        // Don't automatically enable the toggle - let user control it manually\n        canvas.renderAll();\n    };\n    const removeEditableLayer = (layerId)=>{\n        // Find and update the canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.isEditable = false;\n            delete canvasObject.editableType;\n            delete canvasObject.editableName;\n            delete canvasObject.editablePlaceholder;\n            delete canvasObject.editableConstraints;\n        }\n        // Remove from layers list\n        const updatedLayers = editableLayers.filter((layer)=>layer.id !== layerId);\n        setEditableLayers(updatedLayers);\n        // Don't automatically disable the toggle - let user control it manually\n        canvas === null || canvas === void 0 ? void 0 : canvas.renderAll();\n    };\n    const updateLayerName = (layerId, name)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                name\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editableName = name;\n        }\n    };\n    const updateLayerPlaceholder = (layerId, placeholder)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                placeholder\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editablePlaceholder = placeholder;\n        }\n    };\n    const saveTemplateConfig = ()=>{\n        if (!projectId) {\n            alert(\"Project ID is required to save template configuration\");\n            return;\n        }\n        updateTemplateConfig.mutate({\n            isCustomizable,\n            editableLayers: JSON.stringify(editableLayers)\n        }, {\n            onSuccess: ()=>{\n                // Close sidebar after successful save\n                setTimeout(()=>{\n                    onClose();\n                }, 500); // Small delay to ensure state is updated\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"template-config\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"Template Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: \"Configure which elements users can customize\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Template Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isCustomizable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                            variant: \"default\",\n                                            className: \"ml-2\",\n                                            children: \"Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"customizable\",\n                                                className: \"text-sm\",\n                                                children: \"Make Customizable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                                id: \"customizable\",\n                                                checked: isCustomizable,\n                                                onCheckedChange: setIsCustomizable\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: isCustomizable ? \"Template is customizable with \".concat(editableLayers.length, \" editable element\").concat(editableLayers.length !== 1 ? \"s\" : \"\") : \"Allow public users to customize this template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: \"Add Editable Elements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"text\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Text Editable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"image\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Image Replaceable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    selectedObjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Select an element on the canvas first\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, undefined),\n                    editableLayers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: [\n                                        \"Editable Elements (\",\n                                        editableLayers.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-3\",\n                                children: editableLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                        variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                        children: [\n                                                            layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            layer.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeEditableLayer(layer.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Display Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.name,\n                                                                onChange: (e)=>updateLayerName(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"e.g., Your Name, Profile Photo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    layer.type === \"text\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Placeholder Text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.placeholder || \"\",\n                                                                onChange: (e)=>updateLayerPlaceholder(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"Enter placeholder text...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, layer.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: saveTemplateConfig,\n                    className: \"w-full\",\n                    disabled: updateTemplateConfig.isPending,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, undefined),\n                        updateTemplateConfig.isPending ? \"Saving...\" : \"Save Template Config\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateConfigSidebar, \"jMWZ7D+oEsDeK2D8A3TiN5W25SE=\", false, function() {\n    return [\n        _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig\n    ];\n});\n_c = TemplateConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TemplateConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\n"));

/***/ })

});