"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/navbar.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/navbar.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: function() { return /* binding */ Navbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CiFileOn!=!react-icons/ci */ \"(app-pages-browser)/./node_modules/react-icons/ci/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BsCloudCheck_BsCloudSlash_react_icons_bs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BsCloudCheck,BsCloudSlash!=!react-icons/bs */ \"(app-pages-browser)/./node_modules/react-icons/bs/index.mjs\");\n/* harmony import */ var use_file_picker__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-file-picker */ \"(app-pages-browser)/./node_modules/use-file-picker/dist/index.esm.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer-click.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _features_auth_components_user_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/auth/components/user-button */ \"(app-pages-browser)/./src/features/auth/components/user-button.tsx\");\n/* harmony import */ var _features_editor_components_logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/logo */ \"(app-pages-browser)/./src/features/editor/components/logo.tsx\");\n/* harmony import */ var _features_editor_components_project_name__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/project-name */ \"(app-pages-browser)/./src/features/editor/components/project-name.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_hint__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/hint */ \"(app-pages-browser)/./src/components/hint.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Navbar = (param)=>{\n    let { id, editor, activeTool, onChangeActiveTool, onSave, initialData } = param;\n    _s();\n    const data = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useMutationState)({\n        filters: {\n            mutationKey: [\n                \"project\",\n                {\n                    id\n                }\n            ],\n            exact: true\n        },\n        select: (mutation)=>mutation.state.status\n    });\n    const currentStatus = data[data.length - 1];\n    const isError = currentStatus === \"error\";\n    const isPending = currentStatus === \"pending\";\n    const { openFilePicker } = (0,use_file_picker__WEBPACK_IMPORTED_MODULE_1__.useFilePicker)({\n        accept: \".json\",\n        onFilesSuccessfullySelected: (param)=>{\n            let { plainFiles } = param;\n            if (plainFiles && plainFiles.length > 0) {\n                const file = plainFiles[0];\n                const reader = new FileReader();\n                reader.readAsText(file, \"UTF-8\");\n                reader.onload = ()=>{\n                    editor === null || editor === void 0 ? void 0 : editor.loadJson(reader.result);\n                };\n            }\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"w-full flex items-center p-4 h-[68px] gap-x-8 border-b lg:pl-[34px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_logo__WEBPACK_IMPORTED_MODULE_3__.Logo, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex items-center gap-x-1 h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                        modal: false,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    children: [\n                                        \"File\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"size-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                align: \"start\",\n                                className: \"min-w-60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                    onClick: ()=>openFilePicker(),\n                                    className: \"flex items-center gap-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_12__.CiFileOn, {\n                                            className: \"size-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Open\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Open a JSON file\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                        orientation: \"vertical\",\n                        className: \"mx-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_project_name__WEBPACK_IMPORTED_MODULE_4__.ProjectName, {\n                        id: id,\n                        name: initialData.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                        orientation: \"vertical\",\n                        className: \"mx-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Select\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>onChangeActiveTool(\"select\"),\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"select\" && \"bg-gray-100\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Undo\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            disabled: !(editor === null || editor === void 0 ? void 0 : editor.canUndo()),\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.onUndo(),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Redo\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            disabled: !(editor === null || editor === void 0 ? void 0 : editor.canRedo()),\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.onRedo(),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Save\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: onSave,\n                            disabled: !onSave,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                        orientation: \"vertical\",\n                        className: \"mx-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    isPending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"size-4 animate-spin text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Saving...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, undefined),\n                    !isPending && isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsCloudCheck_BsCloudSlash_react_icons_bs__WEBPACK_IMPORTED_MODULE_18__.BsCloudSlash, {\n                                className: \"size-[20px] text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Failed to save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, undefined),\n                    !isPending && !isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsCloudCheck_BsCloudSlash_react_icons_bs__WEBPACK_IMPORTED_MODULE_18__.BsCloudCheck, {\n                                className: \"size-[20px] text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Saved\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-auto flex items-center gap-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                                modal: false,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            size: \"sm\",\n                                            variant: \"ghost\",\n                                            children: [\n                                                \"Export\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"size-4 ml-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"min-w-60\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.saveJson(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_12__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"JSON\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Save for later editing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.savePng(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_12__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"PNG\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Best for sharing on the web\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.saveJpg(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_12__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"JPG\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Best for printing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.saveSvg(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_12__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"SVG\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Best for editing in vector software\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_auth_components_user_button__WEBPACK_IMPORTED_MODULE_2__.UserButton, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"VCYRclSx01n/ShJ89dLY9EBkSlQ=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useMutationState,\n        use_file_picker__WEBPACK_IMPORTED_MODULE_1__.useFilePicker\n    ];\n});\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/navbar.tsx\n"));

/***/ })

});