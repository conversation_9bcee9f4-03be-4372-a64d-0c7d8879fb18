"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/project-card.tsx":
/*!**********************************************!*\
  !*** ./src/app/(dashboard)/project-card.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectCard: function() { return /* binding */ ProjectCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.mjs\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CopyIcon,FileIcon,Globe,Lock,MoreHorizontal,Settings,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\n\n\n\n\n\nconst ProjectCard = (param)=>{\n    let { id, name, width, height, thumbnailUrl, updatedAt, isPublic = false, isCustomizable = false, onClick, onCopy, onDelete, onTogglePublic, onConfigureTemplate, copyLoading = false, deleteLoading = false, togglePublicLoading = false } = param;\n    // Debug thumbnail data\n    console.log(\"ProjectCard render:\", {\n        name,\n        thumbnailUrl: thumbnailUrl ? \"HAS_THUMBNAIL\" : \"NO_THUMBNAIL\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group flex flex-col space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: onClick,\n                className: \"relative w-full cursor-pointer overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.03] hover:border-purple-200\",\n                style: {\n                    aspectRatio: \"\".concat(width, \"/\").concat(height)\n                },\n                children: [\n                    thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: thumbnailUrl,\n                        alt: name,\n                        fill: true,\n                        className: \"object-contain bg-white\",\n                        onError: (e)=>{\n                            console.error(\"Thumbnail failed to load:\", thumbnailUrl);\n                            console.error(\"Error:\", e);\n                        },\n                        onLoad: ()=>{\n                            console.log(\"Thumbnail loaded successfully:\", (thumbnailUrl === null || thumbnailUrl === void 0 ? void 0 : thumbnailUrl.substring(0, 50)) + \"...\");\n                        },\n                        sizes: \"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw\",\n                        quality: 100,\n                        priority: false,\n                        unoptimized: false,\n                        style: {\n                            imageRendering: \"crisp-edges\",\n                            WebkitImageRendering: \"crisp-edges\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full w-full items-center justify-center bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, undefined),\n                    isPublic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Public\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, undefined),\n                    isCustomizable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Template\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/0 to-black/0 transition-all duration-200 group-hover:from-black/5 group-hover:to-black/0 rounded-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-3 opacity-0 transition-all duration-200 group-hover:opacity-100 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs font-semibold text-white truncate\",\n                            children: [\n                                width,\n                                \" \\xd7 \",\n                                height,\n                                \" px\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                onClick: onClick,\n                                className: \"cursor-pointer font-semibold text-sm text-gray-900 truncate hover:text-purple-600 transition-colors leading-tight\",\n                                children: name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 font-medium\",\n                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__.formatDistanceToNow)(updatedAt, {\n                                    addSuffix: true\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenu, {\n                        modal: false,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"icon\",\n                                    variant: \"ghost\",\n                                    className: \"h-8 w-8 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-gray-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuContent, {\n                                align: \"end\",\n                                className: \"w-48\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        disabled: copyLoading,\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onCopy(id);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make a copy\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        disabled: togglePublicLoading,\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onTogglePublic(id, !isPublic);\n                                        },\n                                        children: isPublic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Make private\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Make public\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onConfigureTemplate(id);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Template Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                        className: \"cursor-pointer text-red-600 focus:text-red-600\",\n                                        disabled: deleteLoading,\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            onDelete(id);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CopyIcon_FileIcon_Globe_Lock_MoreHorizontal_Settings_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Delete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\project-card.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ProjectCard;\nvar _c;\n$RefreshReg$(_c, \"ProjectCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/project-card.tsx\n"));

/***/ })

});