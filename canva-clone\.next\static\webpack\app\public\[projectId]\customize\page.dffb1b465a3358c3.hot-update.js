"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _features_editor_components_customization_editor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/components/customization-editor */ \"(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeLayerId, setActiveLayerId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers safely\n                let editableLayers = [];\n                try {\n                    editableLayers = templateData.editableLayers ? JSON.parse(templateData.editableLayers) : [];\n                } catch (error) {\n                    console.error(\"Error parsing editable layers:\", error);\n                    editableLayers = [];\n                }\n                console.log(\"Template loaded:\", {\n                    templateData,\n                    editableLayers,\n                    json: templateData.json ? JSON.parse(templateData.json) : null\n                });\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleCustomizationChange = (layerId, value)=>{\n        console.log(\"Customization change:\", layerId, value);\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        console.log(\"Image upload started for layer:\", layerId, file);\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            console.error(\"Invalid file type:\", file.type);\n            return;\n        }\n        try {\n            // Create object URL for immediate preview\n            const imageUrl = URL.createObjectURL(file);\n            console.log(\"Created object URL:\", imageUrl);\n            // Update customizations to trigger canvas update\n            handleCustomizationChange(layerId, imageUrl);\n            // Auto-select the layer\n            handleLayerActivation(layerId);\n        } catch (error) {\n            console.error(\"Error handling image upload:\", error);\n        }\n    };\n    const handlePreviewGenerated = (dataUrl)=>{\n        setPreviewUrl(dataUrl);\n    };\n    const handleLayerActivation = (layerId)=>{\n        setActiveLayerId(layerId);\n    };\n    // Initialize preview with template thumbnail\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n        }\n    }, [\n        template\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it and download the current preview\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            if (previewUrl) {\n                const link = document.createElement(\"a\");\n                link.href = previewUrl;\n                link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n                link.click();\n            }\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Customization Tools\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Click on elements in the editor to select and customize them\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 p-3 rounded-lg border-2 transition-all cursor-pointer \".concat(activeLayerId === layer.id ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                onClick: ()=>{\n                                                    console.log(\"Clicking layer:\", layer.id, \"Current active:\", activeLayerId);\n                                                    if (activeLayerId === layer.id) {\n                                                        handleLayerActivation(null); // Deselect if already active\n                                                    } else {\n                                                        handleLayerActivation(layer.id); // Select layer\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                variant: activeLayerId === layer.id ? \"default\" : layer.type === \"text\" ? \"outline\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm \".concat(activeLayerId === layer.id ? \"text-blue-900\" : \"text-gray-700\"),\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            activeLayerId === layer.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-auto\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: customizations[layer.id] || layer.originalValue || \"\",\n                                                                onChange: (e)=>handleCustomizationChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1 \".concat(activeLayerId === layer.id ? \"border-blue-500 focus:border-blue-600\" : \"\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            activeLayerId === layer.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 mt-1\",\n                                                                children: \"✓ Selected - Type here or double-click in editor\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                                handleLayerActivation(layer.id); // Auto-select when uploading\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold \".concat(activeLayerId === layer.id ? \"file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\" : \"file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    activeLayerId === layer.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600\",\n                                                                        children: \"✓ Selected - Upload an image to replace\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 365,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 366,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"h-[600px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Template Editor\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Click to select elements • Double-click text to edit • Use toolbar for formatting\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"h-[calc(100%-80px)] p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_customization_editor__WEBPACK_IMPORTED_MODULE_8__.CustomizationEditor, {\n                                            templateData: {\n                                                id: template.id,\n                                                name: template.name,\n                                                width: template.width,\n                                                height: template.height,\n                                                json: template.json,\n                                                editableLayers: template.editableLayers\n                                            },\n                                            customizations: customizations,\n                                            onCustomizationChange: handleCustomizationChange,\n                                            onPreviewGenerated: handlePreviewGenerated,\n                                            activeLayerId: activeLayerId,\n                                            onLayerActivation: handleLayerActivation\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"UqYuE9JKgDmrCOdj/zSgTwB0xHg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ })

});