"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [canvasInitialized, setCanvasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Canvas refs for Fabric.js\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fabricCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers safely\n                let editableLayers = [];\n                try {\n                    editableLayers = templateData.editableLayers ? JSON.parse(templateData.editableLayers) : [];\n                } catch (error) {\n                    console.error(\"Error parsing editable layers:\", error);\n                    editableLayers = [];\n                }\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleTextChange = (layerId, value)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        // Create object URL for immediate preview\n        const imageUrl = URL.createObjectURL(file);\n        // Update customizations immediately\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: imageUrl\n            }));\n    // In a real implementation, you'd upload to your storage service here\n    // const uploadedUrl = await uploadToStorage(file);\n    // setCustomizations(prev => ({ ...prev, [layerId]: uploadedUrl }));\n    };\n    // Initialize Fabric.js canvas with better error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!template || !canvasRef.current) return;\n        // Clean up existing canvas\n        if (fabricCanvasRef.current) {\n            try {\n                fabricCanvasRef.current.dispose();\n            } catch (error) {\n                console.warn(\"Error disposing canvas:\", error);\n            }\n            fabricCanvasRef.current = null;\n        }\n        const initCanvas = async ()=>{\n            if (!canvasRef.current || !template) return;\n            try {\n                // Fix fabric.js textBaseline issue before creating canvas\n                fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Object.prototype.set({\n                    cornerColor: \"#FFF\",\n                    cornerStyle: \"circle\",\n                    borderColor: \"#3b82f6\",\n                    borderScaleFactor: 1.5,\n                    transparentCorners: false,\n                    borderOpacityWhenMoving: 1,\n                    cornerStrokeColor: \"#3b82f6\"\n                });\n                // Fix textBaseline issue\n                fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Text.prototype.set({\n                    textBaseline: \"middle\"\n                });\n                fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Textbox.prototype.set({\n                    textBaseline: \"middle\"\n                });\n                // Create canvas with proper error handling\n                const canvas = new fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Canvas(canvasRef.current, {\n                    width: template.width,\n                    height: template.height,\n                    selection: false,\n                    preserveObjectStacking: true\n                });\n                fabricCanvasRef.current = canvas;\n                // Load template JSON\n                if (template.json) {\n                    try {\n                        const templateJson = JSON.parse(template.json);\n                        await new Promise((resolve, reject)=>{\n                            canvas.loadFromJSON(templateJson, ()=>{\n                                try {\n                                    // Find and handle the workspace/clip object\n                                    const workspace = canvas.getObjects().find((obj)=>obj.name === \"clip\");\n                                    if (workspace) {\n                                        // Set canvas dimensions to match workspace\n                                        canvas.setDimensions({\n                                            width: workspace.width || template.width,\n                                            height: workspace.height || template.height\n                                        });\n                                        // Center the workspace if needed\n                                        workspace.set({\n                                            left: 0,\n                                            top: 0\n                                        });\n                                    }\n                                    canvas.renderAll();\n                                    setCanvasInitialized(true);\n                                    // Delay preview generation to ensure canvas is fully rendered\n                                    setTimeout(()=>{\n                                        generatePreviewFromCanvas();\n                                    }, 100);\n                                    resolve();\n                                } catch (error) {\n                                    reject(error);\n                                }\n                            }, (error)=>{\n                                reject(error);\n                            });\n                        });\n                    } catch (jsonError) {\n                        console.error(\"Error parsing template JSON:\", jsonError);\n                        setCanvasInitialized(true);\n                        generatePreviewFromCanvas();\n                    }\n                } else {\n                    setCanvasInitialized(true);\n                    generatePreviewFromCanvas();\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize canvas:\", error);\n                // Fallback to template thumbnail\n                if (template.thumbnailUrl) {\n                    setPreviewUrl(template.thumbnailUrl);\n                }\n            }\n        };\n        // Delay initialization to ensure DOM is ready\n        const timeoutId = setTimeout(initCanvas, 100);\n        return ()=>{\n            clearTimeout(timeoutId);\n            if (fabricCanvasRef.current) {\n                try {\n                    fabricCanvasRef.current.dispose();\n                } catch (error) {\n                    console.warn(\"Error disposing canvas in cleanup:\", error);\n                }\n                fabricCanvasRef.current = null;\n            }\n        };\n    }, [\n        template\n    ]);\n    // Generate preview from canvas\n    const generatePreviewFromCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!fabricCanvasRef.current || !template) {\n            // Fallback to template thumbnail\n            if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n            return;\n        }\n        try {\n            const canvas = fabricCanvasRef.current;\n            // Calculate appropriate multiplier for good quality but reasonable size\n            const maxPreviewSize = 800; // Max width or height for preview\n            const templateMaxDimension = Math.max(template.width, template.height);\n            const multiplier = Math.min(1, maxPreviewSize / templateMaxDimension);\n            const dataURL = canvas.toDataURL({\n                format: \"png\",\n                quality: 0.9,\n                multiplier: multiplier\n            });\n            setPreviewUrl(dataURL);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n            // Fallback to template thumbnail\n            if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n                setPreviewUrl(template.thumbnailUrl);\n            }\n        }\n    }, [\n        template\n    ]);\n    // Apply customizations to canvas objects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!fabricCanvasRef.current || !canvasInitialized || !template || !template.editableLayers) return;\n        try {\n            const canvas = fabricCanvasRef.current;\n            const objects = canvas.getObjects();\n            // Apply customizations to editable objects\n            template.editableLayers.forEach((layer)=>{\n                const customValue = customizations[layer.id];\n                if (!customValue) return;\n                // Find the canvas object by ID\n                const canvasObject = objects.find((obj)=>obj.id === layer.id);\n                if (!canvasObject) return;\n                if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                    // Apply text customization\n                    canvasObject.set(\"text\", customValue);\n                } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                    // Apply image customization\n                    fabric__WEBPACK_IMPORTED_MODULE_3__.fabric.Image.fromURL(customValue, (img)=>{\n                        if (!fabricCanvasRef.current) return;\n                        try {\n                            // Scale image to fit the object bounds\n                            const scaleX = canvasObject.width / img.width;\n                            const scaleY = canvasObject.height / img.height;\n                            const scale = Math.min(scaleX, scaleY);\n                            img.set({\n                                left: canvasObject.left,\n                                top: canvasObject.top,\n                                scaleX: scale,\n                                scaleY: scale,\n                                originX: canvasObject.originX,\n                                originY: canvasObject.originY,\n                                id: layer.id\n                            });\n                            // Replace the object\n                            fabricCanvasRef.current.remove(canvasObject);\n                            fabricCanvasRef.current.add(img);\n                            fabricCanvasRef.current.renderAll();\n                            generatePreviewFromCanvas();\n                        } catch (error) {\n                            console.error(\"Error applying image customization:\", error);\n                        }\n                    });\n                    return; // Skip the renderAll below since it's handled in the callback\n                }\n            });\n            canvas.renderAll();\n            generatePreviewFromCanvas();\n        } catch (error) {\n            console.error(\"Error applying customizations:\", error);\n        }\n    }, [\n        customizations,\n        canvasInitialized,\n        template,\n        generatePreviewFromCanvas\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Generate preview from current canvas state\n            generatePreviewFromCanvas();\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // In a real implementation, you'd get a download URL from the API\n            const link = document.createElement(\"a\");\n            link.href = (template === null || template === void 0 ? void 0 : template.thumbnailUrl) || \"\";\n            link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n            link.click();\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 363,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 362,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 371,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 370,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Customize Elements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: customizations[layer.id] || \"\",\n                                                                onChange: (e)=>handleTextChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 508,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 509,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 507,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center bg-gray-100 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-lg overflow-hidden flex items-center justify-center\",\n                                                style: {\n                                                    aspectRatio: \"\".concat(template.width, \"/\").concat(template.height),\n                                                    maxWidth: \"100%\",\n                                                    maxHeight: \"500px\",\n                                                    width: \"auto\",\n                                                    height: \"auto\"\n                                                },\n                                                children: previewUrl || template.thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: previewUrl || template.thumbnailUrl || \"\",\n                                                    alt: \"Template preview\",\n                                                    className: \"max-w-full max-h-full object-contain\",\n                                                    style: {\n                                                        width: \"auto\",\n                                                        height: \"auto\",\n                                                        maxWidth: \"100%\",\n                                                        maxHeight: \"100%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center bg-gray-50 w-full h-full min-h-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    display: \"none\"\n                },\n                width: (template === null || template === void 0 ? void 0 : template.width) || 800,\n                height: (template === null || template === void 0 ? void 0 : template.height) || 600\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 583,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 387,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"w0bUFtRIeJ9lE84wprAeFbjL2rs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcHVibGljL1twcm9qZWN0SWRdL2N1c3RvbWl6ZS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWlFO0FBQ1Y7QUFDdUM7QUFDOUQ7QUFFZ0I7QUFDRjtBQUNBO0FBQ2tDO0FBQ2xDO0FBSS9CLFNBQVNxQjs7SUFDdEIsTUFBTUMsU0FBU2xCLDBEQUFTQTtJQUN4QixNQUFNbUIsU0FBU2xCLDBEQUFTQTtJQUN4QixNQUFNLENBQUNtQixVQUFVQyxZQUFZLEdBQUd4QiwrQ0FBUUEsQ0FBMEI7SUFDbEUsTUFBTSxDQUFDeUIsU0FBU0MsV0FBVyxHQUFHMUIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDMkIsT0FBT0MsU0FBUyxHQUFHNUIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQzZCLGdCQUFnQkMsa0JBQWtCLEdBQUc5QiwrQ0FBUUEsQ0FBeUIsQ0FBQztJQUM5RSxNQUFNLENBQUMrQixZQUFZQyxjQUFjLEdBQUdoQywrQ0FBUUEsQ0FBZ0I7SUFDNUQsTUFBTSxDQUFDaUMscUJBQXFCQyx1QkFBdUIsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ21DLGVBQWVDLGlCQUFpQixHQUFHcEMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDcUMsbUJBQW1CQyxxQkFBcUIsR0FBR3RDLCtDQUFRQSxDQUFDO0lBRTNELDRCQUE0QjtJQUM1QixNQUFNdUMsWUFBWXRDLDZDQUFNQSxDQUFvQjtJQUM1QyxNQUFNdUMsa0JBQWtCdkMsNkNBQU1BLENBQXVCO0lBRXJERixnREFBU0EsQ0FBQztRQUNSLE1BQU0wQyxnQkFBZ0I7WUFDcEIsSUFBSTtnQkFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sd0JBQXlDLE9BQWpCdEIsT0FBT3VCLFNBQVM7Z0JBRXJFLElBQUksQ0FBQ0YsU0FBU0csRUFBRSxFQUFFO29CQUNoQixJQUFJSCxTQUFTSSxNQUFNLEtBQUssS0FBSzt3QkFDM0JsQixTQUFTO29CQUNYLE9BQU87d0JBQ0xBLFNBQVM7b0JBQ1g7b0JBQ0E7Z0JBQ0Y7Z0JBRUEsTUFBTW1CLE9BQU8sTUFBTUwsU0FBU00sSUFBSTtnQkFDaEMsTUFBTUMsZUFBZUYsS0FBS0EsSUFBSTtnQkFFOUIsb0NBQW9DO2dCQUNwQyxJQUFJLENBQUNFLGFBQWFDLGNBQWMsSUFBSSxDQUFDRCxhQUFhRSxjQUFjLEVBQUU7b0JBQ2hFdkIsU0FBUztvQkFDVDtnQkFDRjtnQkFFQSwrQkFBK0I7Z0JBQy9CLElBQUl1QixpQkFBa0MsRUFBRTtnQkFDeEMsSUFBSTtvQkFDRkEsaUJBQWlCRixhQUFhRSxjQUFjLEdBQUdDLEtBQUtDLEtBQUssQ0FBQ0osYUFBYUUsY0FBYyxJQUFJLEVBQUU7Z0JBQzdGLEVBQUUsT0FBT3hCLE9BQU87b0JBQ2QyQixRQUFRM0IsS0FBSyxDQUFDLGtDQUFrQ0E7b0JBQ2hEd0IsaUJBQWlCLEVBQUU7Z0JBQ3JCO2dCQUVBM0IsWUFBWTtvQkFDVixHQUFHeUIsWUFBWTtvQkFDZkU7Z0JBQ0Y7Z0JBRUEsaURBQWlEO2dCQUNqRCxNQUFNSSx3QkFBZ0QsQ0FBQztnQkFDdkRKLGVBQWVLLE9BQU8sQ0FBQ0MsQ0FBQUE7b0JBQ3JCRixxQkFBcUIsQ0FBQ0UsTUFBTUMsRUFBRSxDQUFDLEdBQUdELE1BQU1FLGFBQWEsSUFBSTtnQkFDM0Q7Z0JBQ0E3QixrQkFBa0J5QjtZQUVwQixFQUFFLE9BQU9LLEtBQUs7Z0JBQ1poQyxTQUFTO1lBQ1gsU0FBVTtnQkFDUkYsV0FBVztZQUNiO1FBQ0Y7UUFFQSxJQUFJTCxPQUFPdUIsU0FBUyxFQUFFO1lBQ3BCSDtRQUNGO0lBQ0YsR0FBRztRQUFDcEIsT0FBT3VCLFNBQVM7S0FBQztJQUVyQixNQUFNaUIsbUJBQW1CLENBQUNDLFNBQWlCQztRQUN6Q2pDLGtCQUFrQmtDLENBQUFBLE9BQVM7Z0JBQ3pCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0YsUUFBUSxFQUFFQztZQUNiO0lBQ0Y7SUFFQSxNQUFNRSxvQkFBb0IsT0FBT0gsU0FBaUJJO1FBQ2hELDBDQUEwQztRQUMxQyxNQUFNQyxXQUFXQyxJQUFJQyxlQUFlLENBQUNIO1FBRXJDLG9DQUFvQztRQUNwQ3BDLGtCQUFrQmtDLENBQUFBLE9BQVM7Z0JBQ3pCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0YsUUFBUSxFQUFFSztZQUNiO0lBRUEsc0VBQXNFO0lBQ3RFLG1EQUFtRDtJQUNuRCxvRUFBb0U7SUFDdEU7SUFFQSx5REFBeUQ7SUFDekRwRSxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ3dCLFlBQVksQ0FBQ2dCLFVBQVUrQixPQUFPLEVBQUU7UUFFckMsMkJBQTJCO1FBQzNCLElBQUk5QixnQkFBZ0I4QixPQUFPLEVBQUU7WUFDM0IsSUFBSTtnQkFDRjlCLGdCQUFnQjhCLE9BQU8sQ0FBQ0MsT0FBTztZQUNqQyxFQUFFLE9BQU81QyxPQUFPO2dCQUNkMkIsUUFBUWtCLElBQUksQ0FBQywyQkFBMkI3QztZQUMxQztZQUNBYSxnQkFBZ0I4QixPQUFPLEdBQUc7UUFDNUI7UUFFQSxNQUFNRyxhQUFhO1lBQ2pCLElBQUksQ0FBQ2xDLFVBQVUrQixPQUFPLElBQUksQ0FBQy9DLFVBQVU7WUFFckMsSUFBSTtnQkFDRiwwREFBMEQ7Z0JBQzFEWiwwQ0FBTUEsQ0FBQytELE1BQU0sQ0FBQ0MsU0FBUyxDQUFDQyxHQUFHLENBQUM7b0JBQzFCQyxhQUFhO29CQUNiQyxhQUFhO29CQUNiQyxhQUFhO29CQUNiQyxtQkFBbUI7b0JBQ25CQyxvQkFBb0I7b0JBQ3BCQyx5QkFBeUI7b0JBQ3pCQyxtQkFBbUI7Z0JBQ3JCO2dCQUVBLHlCQUF5QjtnQkFDekJ4RSwwQ0FBTUEsQ0FBQ3lFLElBQUksQ0FBQ1QsU0FBUyxDQUFDQyxHQUFHLENBQUM7b0JBQ3hCUyxjQUFjO2dCQUNoQjtnQkFFQTFFLDBDQUFNQSxDQUFDMkUsT0FBTyxDQUFDWCxTQUFTLENBQUNDLEdBQUcsQ0FBQztvQkFDM0JTLGNBQWM7Z0JBQ2hCO2dCQUVBLDJDQUEyQztnQkFDM0MsTUFBTUUsU0FBUyxJQUFJNUUsMENBQU1BLENBQUM2RSxNQUFNLENBQUNqRCxVQUFVK0IsT0FBTyxFQUFFO29CQUNsRG1CLE9BQU9sRSxTQUFTa0UsS0FBSztvQkFDckJDLFFBQVFuRSxTQUFTbUUsTUFBTTtvQkFDdkJDLFdBQVc7b0JBQ1hDLHdCQUF3QjtnQkFDMUI7Z0JBRUFwRCxnQkFBZ0I4QixPQUFPLEdBQUdpQjtnQkFFMUIscUJBQXFCO2dCQUNyQixJQUFJaEUsU0FBU3lCLElBQUksRUFBRTtvQkFDakIsSUFBSTt3QkFDRixNQUFNNkMsZUFBZXpDLEtBQUtDLEtBQUssQ0FBQzlCLFNBQVN5QixJQUFJO3dCQUU3QyxNQUFNLElBQUk4QyxRQUFjLENBQUNDLFNBQVNDOzRCQUNoQ1QsT0FBT1UsWUFBWSxDQUFDSixjQUFjO2dDQUNoQyxJQUFJO29DQUNGLDRDQUE0QztvQ0FDNUMsTUFBTUssWUFBWVgsT0FBT1ksVUFBVSxHQUFHQyxJQUFJLENBQUMsQ0FBQ0MsTUFBYUEsSUFBSUMsSUFBSSxLQUFLO29DQUN0RSxJQUFJSixXQUFXO3dDQUNiLDJDQUEyQzt3Q0FDM0NYLE9BQU9nQixhQUFhLENBQUM7NENBQ25CZCxPQUFPUyxVQUFVVCxLQUFLLElBQUlsRSxTQUFTa0UsS0FBSzs0Q0FDeENDLFFBQVFRLFVBQVVSLE1BQU0sSUFBSW5FLFNBQVNtRSxNQUFNO3dDQUM3Qzt3Q0FFQSxpQ0FBaUM7d0NBQ2pDUSxVQUFVdEIsR0FBRyxDQUFDOzRDQUNaNEIsTUFBTTs0Q0FDTkMsS0FBSzt3Q0FDUDtvQ0FDRjtvQ0FFQWxCLE9BQU9tQixTQUFTO29DQUNoQnBFLHFCQUFxQjtvQ0FFckIsOERBQThEO29DQUM5RHFFLFdBQVc7d0NBQ1RDO29DQUNGLEdBQUc7b0NBRUhiO2dDQUNGLEVBQUUsT0FBT3BFLE9BQU87b0NBQ2RxRSxPQUFPckU7Z0NBQ1Q7NEJBQ0YsR0FBRyxDQUFDQTtnQ0FDRnFFLE9BQU9yRTs0QkFDVDt3QkFDRjtvQkFDRixFQUFFLE9BQU9rRixXQUFXO3dCQUNsQnZELFFBQVEzQixLQUFLLENBQUMsZ0NBQWdDa0Y7d0JBQzlDdkUscUJBQXFCO3dCQUNyQnNFO29CQUNGO2dCQUNGLE9BQU87b0JBQ0x0RSxxQkFBcUI7b0JBQ3JCc0U7Z0JBQ0Y7WUFFRixFQUFFLE9BQU9qRixPQUFPO2dCQUNkMkIsUUFBUTNCLEtBQUssQ0FBQyxnQ0FBZ0NBO2dCQUM5QyxpQ0FBaUM7Z0JBQ2pDLElBQUlKLFNBQVN1RixZQUFZLEVBQUU7b0JBQ3pCOUUsY0FBY1QsU0FBU3VGLFlBQVk7Z0JBQ3JDO1lBQ0Y7UUFDRjtRQUVBLDhDQUE4QztRQUM5QyxNQUFNQyxZQUFZSixXQUFXbEMsWUFBWTtRQUV6QyxPQUFPO1lBQ0x1QyxhQUFhRDtZQUNiLElBQUl2RSxnQkFBZ0I4QixPQUFPLEVBQUU7Z0JBQzNCLElBQUk7b0JBQ0Y5QixnQkFBZ0I4QixPQUFPLENBQUNDLE9BQU87Z0JBQ2pDLEVBQUUsT0FBTzVDLE9BQU87b0JBQ2QyQixRQUFRa0IsSUFBSSxDQUFDLHNDQUFzQzdDO2dCQUNyRDtnQkFDQWEsZ0JBQWdCOEIsT0FBTyxHQUFHO1lBQzVCO1FBQ0Y7SUFDRixHQUFHO1FBQUMvQztLQUFTO0lBRWIsK0JBQStCO0lBQy9CLE1BQU1xRiw0QkFBNEIxRyxrREFBV0EsQ0FBQztRQUM1QyxJQUFJLENBQUNzQyxnQkFBZ0I4QixPQUFPLElBQUksQ0FBQy9DLFVBQVU7WUFDekMsaUNBQWlDO1lBQ2pDLElBQUlBLHFCQUFBQSwrQkFBQUEsU0FBVXVGLFlBQVksRUFBRTtnQkFDMUI5RSxjQUFjVCxTQUFTdUYsWUFBWTtZQUNyQztZQUNBO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTXZCLFNBQVMvQyxnQkFBZ0I4QixPQUFPO1lBRXRDLHdFQUF3RTtZQUN4RSxNQUFNMkMsaUJBQWlCLEtBQUssa0NBQWtDO1lBQzlELE1BQU1DLHVCQUF1QkMsS0FBS0MsR0FBRyxDQUFDN0YsU0FBU2tFLEtBQUssRUFBRWxFLFNBQVNtRSxNQUFNO1lBQ3JFLE1BQU0yQixhQUFhRixLQUFLRyxHQUFHLENBQUMsR0FBR0wsaUJBQWlCQztZQUVoRCxNQUFNSyxVQUFVaEMsT0FBT2lDLFNBQVMsQ0FBQztnQkFDL0JDLFFBQVE7Z0JBQ1JDLFNBQVM7Z0JBQ1RMLFlBQVlBO1lBQ2Q7WUFDQXJGLGNBQWN1RjtRQUNoQixFQUFFLE9BQU81RixPQUFPO1lBQ2QyQixRQUFRM0IsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0MsaUNBQWlDO1lBQ2pDLElBQUlKLHFCQUFBQSwrQkFBQUEsU0FBVXVGLFlBQVksRUFBRTtnQkFDMUI5RSxjQUFjVCxTQUFTdUYsWUFBWTtZQUNyQztRQUNGO0lBQ0YsR0FBRztRQUFDdkY7S0FBUztJQUViLHlDQUF5QztJQUN6Q3hCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDeUMsZ0JBQWdCOEIsT0FBTyxJQUFJLENBQUNqQyxxQkFBcUIsQ0FBQ2QsWUFBWSxDQUFDQSxTQUFTNEIsY0FBYyxFQUFFO1FBRTdGLElBQUk7WUFDRixNQUFNb0MsU0FBUy9DLGdCQUFnQjhCLE9BQU87WUFDdEMsTUFBTXFELFVBQVVwQyxPQUFPWSxVQUFVO1lBRWpDLDJDQUEyQztZQUMzQzVFLFNBQVM0QixjQUFjLENBQUNLLE9BQU8sQ0FBQ0MsQ0FBQUE7Z0JBQzlCLE1BQU1tRSxjQUFjL0YsY0FBYyxDQUFDNEIsTUFBTUMsRUFBRSxDQUFDO2dCQUM1QyxJQUFJLENBQUNrRSxhQUFhO2dCQUVsQiwrQkFBK0I7Z0JBQy9CLE1BQU1DLGVBQWVGLFFBQVF2QixJQUFJLENBQUMsQ0FBQ0MsTUFBYUEsSUFBSTNDLEVBQUUsS0FBS0QsTUFBTUMsRUFBRTtnQkFDbkUsSUFBSSxDQUFDbUUsY0FBYztnQkFFbkIsSUFBSXBFLE1BQU1xRSxJQUFJLEtBQUssVUFBVUQsYUFBYUMsSUFBSSxLQUFLLFdBQVc7b0JBQzVELDJCQUEyQjtvQkFDMUJELGFBQWdDakQsR0FBRyxDQUFDLFFBQVFnRDtnQkFDL0MsT0FBTyxJQUFJbkUsTUFBTXFFLElBQUksS0FBSyxXQUFXRixZQUFZRyxVQUFVLENBQUMsVUFBVTtvQkFDcEUsNEJBQTRCO29CQUM1QnBILDBDQUFNQSxDQUFDRixLQUFLLENBQUN1SCxPQUFPLENBQUNKLGFBQWEsQ0FBQ0s7d0JBQ2pDLElBQUksQ0FBQ3pGLGdCQUFnQjhCLE9BQU8sRUFBRTt3QkFFOUIsSUFBSTs0QkFDRix1Q0FBdUM7NEJBQ3ZDLE1BQU00RCxTQUFTTCxhQUFhcEMsS0FBSyxHQUFJd0MsSUFBSXhDLEtBQUs7NEJBQzlDLE1BQU0wQyxTQUFTTixhQUFhbkMsTUFBTSxHQUFJdUMsSUFBSXZDLE1BQU07NEJBQ2hELE1BQU0wQyxRQUFRakIsS0FBS0csR0FBRyxDQUFDWSxRQUFRQzs0QkFFL0JGLElBQUlyRCxHQUFHLENBQUM7Z0NBQ040QixNQUFNcUIsYUFBYXJCLElBQUk7Z0NBQ3ZCQyxLQUFLb0IsYUFBYXBCLEdBQUc7Z0NBQ3JCeUIsUUFBUUU7Z0NBQ1JELFFBQVFDO2dDQUNSQyxTQUFTUixhQUFhUSxPQUFPO2dDQUM3QkMsU0FBU1QsYUFBYVMsT0FBTztnQ0FDN0I1RSxJQUFJRCxNQUFNQyxFQUFFOzRCQUNkOzRCQUVBLHFCQUFxQjs0QkFDckJsQixnQkFBZ0I4QixPQUFPLENBQUNpRSxNQUFNLENBQUNWOzRCQUMvQnJGLGdCQUFnQjhCLE9BQU8sQ0FBQ2tFLEdBQUcsQ0FBQ1A7NEJBQzVCekYsZ0JBQWdCOEIsT0FBTyxDQUFDb0MsU0FBUzs0QkFDakNFO3dCQUNGLEVBQUUsT0FBT2pGLE9BQU87NEJBQ2QyQixRQUFRM0IsS0FBSyxDQUFDLHVDQUF1Q0E7d0JBQ3ZEO29CQUNGO29CQUNBLFFBQVEsOERBQThEO2dCQUN4RTtZQUNGO1lBRUE0RCxPQUFPbUIsU0FBUztZQUNoQkU7UUFDRixFQUFFLE9BQU9qRixPQUFPO1lBQ2QyQixRQUFRM0IsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDbEQ7SUFDRixHQUFHO1FBQUNFO1FBQWdCUTtRQUFtQmQ7UUFBVXFGO0tBQTBCO0lBRTNFLE1BQU02QixrQkFBa0I7UUFDdEJ2Ryx1QkFBdUI7UUFDdkIsSUFBSTtZQUNGLDZDQUE2QztZQUM3QzBFO1lBQ0Esb0NBQW9DO1lBQ3BDLE1BQU0sSUFBSWQsUUFBUUMsQ0FBQUEsVUFBV1ksV0FBV1osU0FBUztRQUNuRCxFQUFFLE9BQU9uQyxLQUFLO1lBQ1pOLFFBQVEzQixLQUFLLENBQUMsK0JBQStCaUM7UUFDL0MsU0FBVTtZQUNSMUIsdUJBQXVCO1FBQ3pCO0lBQ0Y7SUFFQSxNQUFNd0cscUJBQXFCO1FBQ3pCdEcsaUJBQWlCO1FBQ2pCLElBQUk7WUFDRix3RUFBd0U7WUFDeEUsNkJBQTZCO1lBQzdCLE1BQU0sSUFBSTBELFFBQVFDLENBQUFBLFVBQVdZLFdBQVdaLFNBQVM7WUFFakQsa0VBQWtFO1lBQ2xFLE1BQU00QyxPQUFPQyxTQUFTQyxhQUFhLENBQUM7WUFDcENGLEtBQUtHLElBQUksR0FBR3ZILENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVXVGLFlBQVksS0FBSTtZQUN0QzZCLEtBQUtJLFFBQVEsR0FBRyxjQUF5QyxPQUEzQnhILENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVStFLElBQUksS0FBSSxVQUFTO1lBQ3pEcUMsS0FBS0ssS0FBSztRQUNaLEVBQUUsT0FBT3BGLEtBQUs7WUFDWk4sUUFBUTNCLEtBQUssQ0FBQyx1QkFBdUJpQztRQUN2QyxTQUFVO1lBQ1J4QixpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLElBQUlYLFNBQVM7UUFDWCxxQkFDRSw4REFBQ3dIO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUM3SSxpSEFBT0E7Z0JBQUM2SSxXQUFVOzs7Ozs7Ozs7OztJQUd6QjtJQUVBLElBQUl2SCxTQUFTLENBQUNKLFVBQVU7UUFDdEIscUJBQ0UsOERBQUMwSDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FDWHZILFNBQVM7Ozs7OztzQ0FFWiw4REFBQ2YseURBQU1BOzRCQUFDd0ksU0FBUyxJQUFNOUgsT0FBTytILElBQUksQ0FBQzs0QkFBWUMsU0FBUTs7OENBQ3JELDhEQUFDaEosa0hBQVNBO29DQUFDNEksV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFPbEQ7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN0SSx5REFBTUE7d0NBQ0x3SSxTQUFTLElBQU05SCxPQUFPK0gsSUFBSSxDQUFDLFdBQTRCLE9BQWpCaEksT0FBT3VCLFNBQVM7d0NBQ3REMEcsU0FBUTt3Q0FDUkMsTUFBSzs7MERBRUwsOERBQUNqSixrSEFBU0E7Z0RBQUM0SSxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7O2tEQUd4Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7OztrREFDZiw4REFBQ0Q7OzBEQUNDLDhEQUFDRTtnREFBR0QsV0FBVTs7b0RBQXNDO29EQUN0QzNILFNBQVMrRSxJQUFJOzs7Ozs7OzBEQUUzQiw4REFBQ2tEO2dEQUFFTixXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUt6Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDdEkseURBQU1BO3dDQUNMd0ksU0FBU1g7d0NBQ1RnQixVQUFVeEg7d0NBQ1ZxSCxTQUFROzs0Q0FFUHJILG9DQUNDLDhEQUFDNUIsaUhBQU9BO2dEQUFDNkksV0FBVTs7Ozs7dURBQ2pCOzRDQUFLOzs7Ozs7O2tEQUdYLDhEQUFDdEkseURBQU1BO3dDQUNMd0ksU0FBU1Y7d0NBQ1RlLFVBQVV0SDt3Q0FDVitHLFdBQVU7OzRDQUVUL0csOEJBQ0MsOERBQUM5QixpSEFBT0E7Z0RBQUM2SSxXQUFVOzs7OztxRUFFbkIsOERBQUMzSSxrSEFBUUE7Z0RBQUMySSxXQUFVOzs7Ozs7NENBQ3BCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTWiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNuSSxxREFBSUE7O2tEQUNILDhEQUFDRSwyREFBVUE7a0RBQ1QsNEVBQUNDLDBEQUFTQTs0Q0FBQ2dJLFdBQVU7c0RBQVU7Ozs7Ozs7Ozs7O2tEQUVqQyw4REFBQ2xJLDREQUFXQTt3Q0FBQ2tJLFdBQVU7a0RBQ3BCM0gsU0FBUzRCLGNBQWMsQ0FBQ3VHLEdBQUcsQ0FBQyxDQUFDakc7Z0RBdUJUQSxvQkFHWkEscUJBRUk1QiwwQkFZTzRCLG1DQUFBQSxxQkFvQ1RBO2lFQTNFVCw4REFBQ3dGO2dEQUFtQkMsV0FBVTs7a0VBQzVCLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUMvSCx1REFBS0E7Z0VBQUNtSSxTQUFTN0YsTUFBTXFFLElBQUksS0FBSyxTQUFTLFlBQVk7O29FQUNqRHJFLE1BQU1xRSxJQUFJLEtBQUssdUJBQ2QsOERBQUN0SCxrSEFBSUE7d0VBQUMwSSxXQUFVOzs7Ozs2RkFFaEIsOERBQUN4SSxrSEFBU0E7d0VBQUN3SSxXQUFVOzs7Ozs7b0VBRXRCekYsTUFBTXFFLElBQUk7Ozs7Ozs7MEVBRWIsOERBQUM2QjtnRUFBS1QsV0FBVTswRUFBdUJ6RixNQUFNNkMsSUFBSTs7Ozs7Ozs7Ozs7O29EQUdsRDdDLE1BQU1xRSxJQUFJLEtBQUssdUJBQ2QsOERBQUNtQjs7MEVBQ0MsOERBQUNuSSx1REFBS0E7Z0VBQUNvSSxXQUFVOzBFQUNkekYsTUFBTW1HLFdBQVcsSUFBSTs7Ozs7OzBFQUV4Qiw4REFBQy9JLHVEQUFLQTtnRUFDSmtELE9BQU9sQyxjQUFjLENBQUM0QixNQUFNQyxFQUFFLENBQUMsSUFBSTtnRUFDbkNtRyxVQUFVLENBQUNDLElBQU1qRyxpQkFBaUJKLE1BQU1DLEVBQUUsRUFBRW9HLEVBQUVDLE1BQU0sQ0FBQ2hHLEtBQUs7Z0VBQzFENkYsYUFBYW5HLE1BQU1tRyxXQUFXO2dFQUM5QkksU0FBUyxHQUFFdkcscUJBQUFBLE1BQU13RyxXQUFXLGNBQWpCeEcseUNBQUFBLG1CQUFtQnVHLFNBQVM7Z0VBQ3ZDZCxXQUFVOzs7Ozs7NERBRVh6RixFQUFBQSxzQkFBQUEsTUFBTXdHLFdBQVcsY0FBakJ4RywwQ0FBQUEsb0JBQW1CdUcsU0FBUyxtQkFDM0IsOERBQUNSO2dFQUFFTixXQUFVOztvRUFDVnJILEVBQUFBLDJCQUFBQSxjQUFjLENBQUM0QixNQUFNQyxFQUFFLENBQUMsY0FBeEI3QiwrQ0FBQUEseUJBQTBCcUksTUFBTSxLQUFJO29FQUFFO29FQUFFekcsTUFBTXdHLFdBQVcsQ0FBQ0QsU0FBUztvRUFBQzs7Ozs7Ozs7Ozs7OzZFQUszRSw4REFBQ2Y7OzBFQUNDLDhEQUFDbkksdURBQUtBO2dFQUFDb0ksV0FBVTswRUFBd0I7Ozs7OzswRUFHekMsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ2lCO3dFQUNDckMsTUFBSzt3RUFDTHNDLFFBQVEzRyxFQUFBQSxzQkFBQUEsTUFBTXdHLFdBQVcsY0FBakJ4RywyQ0FBQUEsb0NBQUFBLG9CQUFtQjRHLGNBQWMsY0FBakM1Ryx3REFBQUEsa0NBQW1DaUcsR0FBRyxDQUFDWSxDQUFBQSxJQUFLLElBQU0sT0FBRkEsSUFBS0MsSUFBSSxDQUFDLFNBQVE7d0VBQzFFVixVQUFVLENBQUNDO2dGQUNJQTs0RUFBYixNQUFNNUYsUUFBTzRGLGtCQUFBQSxFQUFFQyxNQUFNLENBQUNTLEtBQUssY0FBZFYsc0NBQUFBLGVBQWdCLENBQUMsRUFBRTs0RUFDaEMsSUFBSTVGLE1BQU07Z0ZBQ1JELGtCQUFrQlIsTUFBTUMsRUFBRSxFQUFFUTs0RUFDOUI7d0VBQ0Y7d0VBQ0FnRixXQUFVOzs7Ozs7b0VBSVhySCxjQUFjLENBQUM0QixNQUFNQyxFQUFFLENBQUMsa0JBQ3ZCLDhEQUFDdUY7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDakI7Z0ZBQ0N3QyxLQUFLNUksY0FBYyxDQUFDNEIsTUFBTUMsRUFBRSxDQUFDO2dGQUM3QmdILEtBQUk7Z0ZBQ0p4QixXQUFVOzs7Ozs7MEZBRVosOERBQUNEO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ007d0ZBQUVOLFdBQVU7a0dBQXdCOzs7Ozs7a0dBQ3JDLDhEQUFDeUI7d0ZBQ0N2QixTQUFTOzRGQUNQdEgsa0JBQWtCa0MsQ0FBQUE7Z0dBQ2hCLE1BQU00RyxVQUFVO29HQUFFLEdBQUc1RyxJQUFJO2dHQUFDO2dHQUMxQixPQUFPNEcsT0FBTyxDQUFDbkgsTUFBTUMsRUFBRSxDQUFDO2dHQUN4QixPQUFPa0g7NEZBQ1Q7d0ZBQ0Y7d0ZBQ0ExQixXQUFVO2tHQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0VBT056RixFQUFBQSxzQkFBQUEsTUFBTXdHLFdBQVcsY0FBakJ4RywwQ0FBQUEsb0JBQW1Cb0gsV0FBVyxtQkFDN0IsOERBQUNyQjt3RUFBRU4sV0FBVTs7NEVBQXdCOzRFQUN4Qi9CLEtBQUsyRCxLQUFLLENBQUNySCxNQUFNd0csV0FBVyxDQUFDWSxXQUFXLEdBQUcsT0FBTzs0RUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0NBN0VyRXBILE1BQU1DLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBMEYxQiw4REFBQ3VGOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDbkkscURBQUlBOztrREFDSCw4REFBQ0UsMkRBQVVBO2tEQUNULDRFQUFDQywwREFBU0E7NENBQUNnSSxXQUFVO3NEQUFVOzs7Ozs7Ozs7OztrREFFakMsOERBQUNsSSw0REFBV0E7a0RBQ1YsNEVBQUNpSTs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQ0NDLFdBQVU7Z0RBQ1Y2QixPQUFPO29EQUNMQyxhQUFhLEdBQXFCekosT0FBbEJBLFNBQVNrRSxLQUFLLEVBQUMsS0FBbUIsT0FBaEJsRSxTQUFTbUUsTUFBTTtvREFDakR1RixVQUFVO29EQUNWQyxXQUFXO29EQUNYekYsT0FBTztvREFDUEMsUUFBUTtnREFDVjswREFFQzNELGNBQWNSLFNBQVN1RixZQUFZLGlCQUNsQyw4REFBQ21CO29EQUNDd0MsS0FBSzFJLGNBQWNSLFNBQVN1RixZQUFZLElBQUk7b0RBQzVDNEQsS0FBSTtvREFDSnhCLFdBQVU7b0RBQ1Y2QixPQUFPO3dEQUNMdEYsT0FBTzt3REFDUEMsUUFBUTt3REFDUnVGLFVBQVU7d0RBQ1ZDLFdBQVc7b0RBQ2I7Ozs7O3lFQUdGLDhEQUFDakM7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUN4SSxrSEFBU0E7d0RBQUN3SSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBWXZDLDhEQUFDM0Q7Z0JBQ0M0RixLQUFLNUk7Z0JBQ0x3SSxPQUFPO29CQUFFSyxTQUFTO2dCQUFPO2dCQUN6QjNGLE9BQU9sRSxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVrRSxLQUFLLEtBQUk7Z0JBQzFCQyxRQUFRbkUsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVbUUsTUFBTSxLQUFJOzs7Ozs7Ozs7Ozs7QUFJcEM7R0EvakJ3QnRFOztRQUNQakIsc0RBQVNBO1FBQ1RDLHNEQUFTQTs7O0tBRkZnQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3B1YmxpYy9bcHJvamVjdElkXS9jdXN0b21pemUvcGFnZS50c3g/MTlkMSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VDYWxsYmFjayB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlUGFyYW1zLCB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQgeyBMb2FkZXIyLCBBcnJvd0xlZnQsIERvd25sb2FkLCBVcGxvYWQsIFR5cGUsIEltYWdlIGFzIEltYWdlSWNvbiB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IGZhYnJpYyB9IGZyb20gXCJmYWJyaWNcIjtcblxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiO1xuaW1wb3J0IHsgTGFiZWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2xhYmVsXCI7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jYXJkXCI7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIjtcblxuaW1wb3J0IHsgVGVtcGxhdGVSZXNwb25zZSwgRWRpdGFibGVMYXllciB9IGZyb20gXCJAL3R5cGVzL3RlbXBsYXRlXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEN1c3RvbWl6ZVRlbXBsYXRlUGFnZSgpIHtcbiAgY29uc3QgcGFyYW1zID0gdXNlUGFyYW1zKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBbdGVtcGxhdGUsIHNldFRlbXBsYXRlXSA9IHVzZVN0YXRlPFRlbXBsYXRlUmVzcG9uc2UgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtjdXN0b21pemF0aW9ucywgc2V0Q3VzdG9taXphdGlvbnNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgc3RyaW5nPj4oe30pO1xuICBjb25zdCBbcHJldmlld1VybCwgc2V0UHJldmlld1VybF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzR2VuZXJhdGluZ1ByZXZpZXcsIHNldElzR2VuZXJhdGluZ1ByZXZpZXddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNEb3dubG9hZGluZywgc2V0SXNEb3dubG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjYW52YXNJbml0aWFsaXplZCwgc2V0Q2FudmFzSW5pdGlhbGl6ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIENhbnZhcyByZWZzIGZvciBGYWJyaWMuanNcbiAgY29uc3QgY2FudmFzUmVmID0gdXNlUmVmPEhUTUxDYW52YXNFbGVtZW50PihudWxsKTtcbiAgY29uc3QgZmFicmljQ2FudmFzUmVmID0gdXNlUmVmPGZhYnJpYy5DYW52YXMgfCBudWxsPihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGZldGNoVGVtcGxhdGUgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3Byb2plY3RzL3B1YmxpYy8ke3BhcmFtcy5wcm9qZWN0SWR9YCk7XG4gICAgICAgIFxuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDA0KSB7XG4gICAgICAgICAgICBzZXRFcnJvcihcIlRlbXBsYXRlIG5vdCBmb3VuZCBvciBub3QgcHVibGljXCIpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzZXRFcnJvcihcIkZhaWxlZCB0byBsb2FkIHRlbXBsYXRlXCIpO1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnN0IHRlbXBsYXRlRGF0YSA9IGRhdGEuZGF0YTtcbiAgICAgICAgXG4gICAgICAgIC8vIENoZWNrIGlmIHRlbXBsYXRlIGlzIGN1c3RvbWl6YWJsZVxuICAgICAgICBpZiAoIXRlbXBsYXRlRGF0YS5pc0N1c3RvbWl6YWJsZSB8fCAhdGVtcGxhdGVEYXRhLmVkaXRhYmxlTGF5ZXJzKSB7XG4gICAgICAgICAgc2V0RXJyb3IoXCJUaGlzIHRlbXBsYXRlIGlzIG5vdCBjdXN0b21pemFibGVcIik7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gUGFyc2UgZWRpdGFibGUgbGF5ZXJzIHNhZmVseVxuICAgICAgICBsZXQgZWRpdGFibGVMYXllcnM6IEVkaXRhYmxlTGF5ZXJbXSA9IFtdO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGVkaXRhYmxlTGF5ZXJzID0gdGVtcGxhdGVEYXRhLmVkaXRhYmxlTGF5ZXJzID8gSlNPTi5wYXJzZSh0ZW1wbGF0ZURhdGEuZWRpdGFibGVMYXllcnMpIDogW107XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyBlZGl0YWJsZSBsYXllcnM6JywgZXJyb3IpO1xuICAgICAgICAgIGVkaXRhYmxlTGF5ZXJzID0gW107XG4gICAgICAgIH1cblxuICAgICAgICBzZXRUZW1wbGF0ZSh7XG4gICAgICAgICAgLi4udGVtcGxhdGVEYXRhLFxuICAgICAgICAgIGVkaXRhYmxlTGF5ZXJzLFxuICAgICAgICB9KTtcblxuICAgICAgICAvLyBJbml0aWFsaXplIGN1c3RvbWl6YXRpb25zIHdpdGggb3JpZ2luYWwgdmFsdWVzXG4gICAgICAgIGNvbnN0IGluaXRpYWxDdXN0b21pemF0aW9uczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHt9O1xuICAgICAgICBlZGl0YWJsZUxheWVycy5mb3JFYWNoKGxheWVyID0+IHtcbiAgICAgICAgICBpbml0aWFsQ3VzdG9taXphdGlvbnNbbGF5ZXIuaWRdID0gbGF5ZXIub3JpZ2luYWxWYWx1ZSB8fCAnJztcbiAgICAgICAgfSk7XG4gICAgICAgIHNldEN1c3RvbWl6YXRpb25zKGluaXRpYWxDdXN0b21pemF0aW9ucyk7XG4gICAgICAgIFxuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIHNldEVycm9yKFwiRmFpbGVkIHRvIGxvYWQgdGVtcGxhdGVcIik7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaWYgKHBhcmFtcy5wcm9qZWN0SWQpIHtcbiAgICAgIGZldGNoVGVtcGxhdGUoKTtcbiAgICB9XG4gIH0sIFtwYXJhbXMucHJvamVjdElkXSk7XG5cbiAgY29uc3QgaGFuZGxlVGV4dENoYW5nZSA9IChsYXllcklkOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRDdXN0b21pemF0aW9ucyhwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW2xheWVySWRdOiB2YWx1ZSxcbiAgICB9KSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VVcGxvYWQgPSBhc3luYyAobGF5ZXJJZDogc3RyaW5nLCBmaWxlOiBGaWxlKSA9PiB7XG4gICAgLy8gQ3JlYXRlIG9iamVjdCBVUkwgZm9yIGltbWVkaWF0ZSBwcmV2aWV3XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGZpbGUpO1xuXG4gICAgLy8gVXBkYXRlIGN1c3RvbWl6YXRpb25zIGltbWVkaWF0ZWx5XG4gICAgc2V0Q3VzdG9taXphdGlvbnMocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtsYXllcklkXTogaW1hZ2VVcmwsXG4gICAgfSkpO1xuXG4gICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB5b3UnZCB1cGxvYWQgdG8geW91ciBzdG9yYWdlIHNlcnZpY2UgaGVyZVxuICAgIC8vIGNvbnN0IHVwbG9hZGVkVXJsID0gYXdhaXQgdXBsb2FkVG9TdG9yYWdlKGZpbGUpO1xuICAgIC8vIHNldEN1c3RvbWl6YXRpb25zKHByZXYgPT4gKHsgLi4ucHJldiwgW2xheWVySWRdOiB1cGxvYWRlZFVybCB9KSk7XG4gIH07XG5cbiAgLy8gSW5pdGlhbGl6ZSBGYWJyaWMuanMgY2FudmFzIHdpdGggYmV0dGVyIGVycm9yIGhhbmRsaW5nXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCF0ZW1wbGF0ZSB8fCAhY2FudmFzUmVmLmN1cnJlbnQpIHJldHVybjtcblxuICAgIC8vIENsZWFuIHVwIGV4aXN0aW5nIGNhbnZhc1xuICAgIGlmIChmYWJyaWNDYW52YXNSZWYuY3VycmVudCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgZmFicmljQ2FudmFzUmVmLmN1cnJlbnQuZGlzcG9zZSgpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdFcnJvciBkaXNwb3NpbmcgY2FudmFzOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG5cbiAgICBjb25zdCBpbml0Q2FudmFzID0gYXN5bmMgKCkgPT4ge1xuICAgICAgaWYgKCFjYW52YXNSZWYuY3VycmVudCB8fCAhdGVtcGxhdGUpIHJldHVybjtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gRml4IGZhYnJpYy5qcyB0ZXh0QmFzZWxpbmUgaXNzdWUgYmVmb3JlIGNyZWF0aW5nIGNhbnZhc1xuICAgICAgICBmYWJyaWMuT2JqZWN0LnByb3RvdHlwZS5zZXQoe1xuICAgICAgICAgIGNvcm5lckNvbG9yOiBcIiNGRkZcIixcbiAgICAgICAgICBjb3JuZXJTdHlsZTogXCJjaXJjbGVcIixcbiAgICAgICAgICBib3JkZXJDb2xvcjogXCIjM2I4MmY2XCIsXG4gICAgICAgICAgYm9yZGVyU2NhbGVGYWN0b3I6IDEuNSxcbiAgICAgICAgICB0cmFuc3BhcmVudENvcm5lcnM6IGZhbHNlLFxuICAgICAgICAgIGJvcmRlck9wYWNpdHlXaGVuTW92aW5nOiAxLFxuICAgICAgICAgIGNvcm5lclN0cm9rZUNvbG9yOiBcIiMzYjgyZjZcIixcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gRml4IHRleHRCYXNlbGluZSBpc3N1ZVxuICAgICAgICBmYWJyaWMuVGV4dC5wcm90b3R5cGUuc2V0KHtcbiAgICAgICAgICB0ZXh0QmFzZWxpbmU6IFwibWlkZGxlXCIsXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGZhYnJpYy5UZXh0Ym94LnByb3RvdHlwZS5zZXQoe1xuICAgICAgICAgIHRleHRCYXNlbGluZTogXCJtaWRkbGVcIixcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gQ3JlYXRlIGNhbnZhcyB3aXRoIHByb3BlciBlcnJvciBoYW5kbGluZ1xuICAgICAgICBjb25zdCBjYW52YXMgPSBuZXcgZmFicmljLkNhbnZhcyhjYW52YXNSZWYuY3VycmVudCwge1xuICAgICAgICAgIHdpZHRoOiB0ZW1wbGF0ZS53aWR0aCxcbiAgICAgICAgICBoZWlnaHQ6IHRlbXBsYXRlLmhlaWdodCxcbiAgICAgICAgICBzZWxlY3Rpb246IGZhbHNlLFxuICAgICAgICAgIHByZXNlcnZlT2JqZWN0U3RhY2tpbmc6IHRydWUsXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50ID0gY2FudmFzO1xuXG4gICAgICAgIC8vIExvYWQgdGVtcGxhdGUgSlNPTlxuICAgICAgICBpZiAodGVtcGxhdGUuanNvbikge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCB0ZW1wbGF0ZUpzb24gPSBKU09OLnBhcnNlKHRlbXBsYXRlLmpzb24pO1xuXG4gICAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZTx2b2lkPigocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICAgIGNhbnZhcy5sb2FkRnJvbUpTT04odGVtcGxhdGVKc29uLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgIC8vIEZpbmQgYW5kIGhhbmRsZSB0aGUgd29ya3NwYWNlL2NsaXAgb2JqZWN0XG4gICAgICAgICAgICAgICAgICBjb25zdCB3b3Jrc3BhY2UgPSBjYW52YXMuZ2V0T2JqZWN0cygpLmZpbmQoKG9iajogYW55KSA9PiBvYmoubmFtZSA9PT0gXCJjbGlwXCIpO1xuICAgICAgICAgICAgICAgICAgaWYgKHdvcmtzcGFjZSkge1xuICAgICAgICAgICAgICAgICAgICAvLyBTZXQgY2FudmFzIGRpbWVuc2lvbnMgdG8gbWF0Y2ggd29ya3NwYWNlXG4gICAgICAgICAgICAgICAgICAgIGNhbnZhcy5zZXREaW1lbnNpb25zKHtcbiAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogd29ya3NwYWNlLndpZHRoIHx8IHRlbXBsYXRlLndpZHRoLFxuICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogd29ya3NwYWNlLmhlaWdodCB8fCB0ZW1wbGF0ZS5oZWlnaHQsXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgICAgIC8vIENlbnRlciB0aGUgd29ya3NwYWNlIGlmIG5lZWRlZFxuICAgICAgICAgICAgICAgICAgICB3b3Jrc3BhY2Uuc2V0KHtcbiAgICAgICAgICAgICAgICAgICAgICBsZWZ0OiAwLFxuICAgICAgICAgICAgICAgICAgICAgIHRvcDogMCxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcbiAgICAgICAgICAgICAgICAgIHNldENhbnZhc0luaXRpYWxpemVkKHRydWUpO1xuXG4gICAgICAgICAgICAgICAgICAvLyBEZWxheSBwcmV2aWV3IGdlbmVyYXRpb24gdG8gZW5zdXJlIGNhbnZhcyBpcyBmdWxseSByZW5kZXJlZFxuICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlUHJldmlld0Zyb21DYW52YXMoKTtcbiAgICAgICAgICAgICAgICAgIH0sIDEwMCk7XG5cbiAgICAgICAgICAgICAgICAgIHJlc29sdmUoKTtcbiAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgICAgcmVqZWN0KGVycm9yKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0sIChlcnJvcjogYW55KSA9PiB7XG4gICAgICAgICAgICAgICAgcmVqZWN0KGVycm9yKTtcbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9IGNhdGNoIChqc29uRXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBhcnNpbmcgdGVtcGxhdGUgSlNPTjonLCBqc29uRXJyb3IpO1xuICAgICAgICAgICAgc2V0Q2FudmFzSW5pdGlhbGl6ZWQodHJ1ZSk7XG4gICAgICAgICAgICBnZW5lcmF0ZVByZXZpZXdGcm9tQ2FudmFzKCk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldENhbnZhc0luaXRpYWxpemVkKHRydWUpO1xuICAgICAgICAgIGdlbmVyYXRlUHJldmlld0Zyb21DYW52YXMoKTtcbiAgICAgICAgfVxuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gaW5pdGlhbGl6ZSBjYW52YXM6JywgZXJyb3IpO1xuICAgICAgICAvLyBGYWxsYmFjayB0byB0ZW1wbGF0ZSB0aHVtYm5haWxcbiAgICAgICAgaWYgKHRlbXBsYXRlLnRodW1ibmFpbFVybCkge1xuICAgICAgICAgIHNldFByZXZpZXdVcmwodGVtcGxhdGUudGh1bWJuYWlsVXJsKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG5cbiAgICAvLyBEZWxheSBpbml0aWFsaXphdGlvbiB0byBlbnN1cmUgRE9NIGlzIHJlYWR5XG4gICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dChpbml0Q2FudmFzLCAxMDApO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgaWYgKGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50KSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgZmFicmljQ2FudmFzUmVmLmN1cnJlbnQuZGlzcG9zZSgpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUud2FybignRXJyb3IgZGlzcG9zaW5nIGNhbnZhcyBpbiBjbGVhbnVwOicsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgICBmYWJyaWNDYW52YXNSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW3RlbXBsYXRlXSk7XG5cbiAgLy8gR2VuZXJhdGUgcHJldmlldyBmcm9tIGNhbnZhc1xuICBjb25zdCBnZW5lcmF0ZVByZXZpZXdGcm9tQ2FudmFzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghZmFicmljQ2FudmFzUmVmLmN1cnJlbnQgfHwgIXRlbXBsYXRlKSB7XG4gICAgICAvLyBGYWxsYmFjayB0byB0ZW1wbGF0ZSB0aHVtYm5haWxcbiAgICAgIGlmICh0ZW1wbGF0ZT8udGh1bWJuYWlsVXJsKSB7XG4gICAgICAgIHNldFByZXZpZXdVcmwodGVtcGxhdGUudGh1bWJuYWlsVXJsKTtcbiAgICAgIH1cbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgY2FudmFzID0gZmFicmljQ2FudmFzUmVmLmN1cnJlbnQ7XG5cbiAgICAgIC8vIENhbGN1bGF0ZSBhcHByb3ByaWF0ZSBtdWx0aXBsaWVyIGZvciBnb29kIHF1YWxpdHkgYnV0IHJlYXNvbmFibGUgc2l6ZVxuICAgICAgY29uc3QgbWF4UHJldmlld1NpemUgPSA4MDA7IC8vIE1heCB3aWR0aCBvciBoZWlnaHQgZm9yIHByZXZpZXdcbiAgICAgIGNvbnN0IHRlbXBsYXRlTWF4RGltZW5zaW9uID0gTWF0aC5tYXgodGVtcGxhdGUud2lkdGgsIHRlbXBsYXRlLmhlaWdodCk7XG4gICAgICBjb25zdCBtdWx0aXBsaWVyID0gTWF0aC5taW4oMSwgbWF4UHJldmlld1NpemUgLyB0ZW1wbGF0ZU1heERpbWVuc2lvbik7XG5cbiAgICAgIGNvbnN0IGRhdGFVUkwgPSBjYW52YXMudG9EYXRhVVJMKHtcbiAgICAgICAgZm9ybWF0OiAncG5nJyxcbiAgICAgICAgcXVhbGl0eTogMC45LFxuICAgICAgICBtdWx0aXBsaWVyOiBtdWx0aXBsaWVyLFxuICAgICAgfSk7XG4gICAgICBzZXRQcmV2aWV3VXJsKGRhdGFVUkwpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2VuZXJhdGUgcHJldmlldzonLCBlcnJvcik7XG4gICAgICAvLyBGYWxsYmFjayB0byB0ZW1wbGF0ZSB0aHVtYm5haWxcbiAgICAgIGlmICh0ZW1wbGF0ZT8udGh1bWJuYWlsVXJsKSB7XG4gICAgICAgIHNldFByZXZpZXdVcmwodGVtcGxhdGUudGh1bWJuYWlsVXJsKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFt0ZW1wbGF0ZV0pO1xuXG4gIC8vIEFwcGx5IGN1c3RvbWl6YXRpb25zIHRvIGNhbnZhcyBvYmplY3RzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFmYWJyaWNDYW52YXNSZWYuY3VycmVudCB8fCAhY2FudmFzSW5pdGlhbGl6ZWQgfHwgIXRlbXBsYXRlIHx8ICF0ZW1wbGF0ZS5lZGl0YWJsZUxheWVycykgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNhbnZhcyA9IGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50O1xuICAgICAgY29uc3Qgb2JqZWN0cyA9IGNhbnZhcy5nZXRPYmplY3RzKCk7XG5cbiAgICAgIC8vIEFwcGx5IGN1c3RvbWl6YXRpb25zIHRvIGVkaXRhYmxlIG9iamVjdHNcbiAgICAgIHRlbXBsYXRlLmVkaXRhYmxlTGF5ZXJzLmZvckVhY2gobGF5ZXIgPT4ge1xuICAgICAgICBjb25zdCBjdXN0b21WYWx1ZSA9IGN1c3RvbWl6YXRpb25zW2xheWVyLmlkXTtcbiAgICAgICAgaWYgKCFjdXN0b21WYWx1ZSkgcmV0dXJuO1xuXG4gICAgICAgIC8vIEZpbmQgdGhlIGNhbnZhcyBvYmplY3QgYnkgSURcbiAgICAgICAgY29uc3QgY2FudmFzT2JqZWN0ID0gb2JqZWN0cy5maW5kKChvYmo6IGFueSkgPT4gb2JqLmlkID09PSBsYXllci5pZCk7XG4gICAgICAgIGlmICghY2FudmFzT2JqZWN0KSByZXR1cm47XG5cbiAgICAgICAgaWYgKGxheWVyLnR5cGUgPT09ICd0ZXh0JyAmJiBjYW52YXNPYmplY3QudHlwZSA9PT0gJ3RleHRib3gnKSB7XG4gICAgICAgICAgLy8gQXBwbHkgdGV4dCBjdXN0b21pemF0aW9uXG4gICAgICAgICAgKGNhbnZhc09iamVjdCBhcyBmYWJyaWMuVGV4dGJveCkuc2V0KCd0ZXh0JywgY3VzdG9tVmFsdWUpO1xuICAgICAgICB9IGVsc2UgaWYgKGxheWVyLnR5cGUgPT09ICdpbWFnZScgJiYgY3VzdG9tVmFsdWUuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgICAgIC8vIEFwcGx5IGltYWdlIGN1c3RvbWl6YXRpb25cbiAgICAgICAgICBmYWJyaWMuSW1hZ2UuZnJvbVVSTChjdXN0b21WYWx1ZSwgKGltZykgPT4ge1xuICAgICAgICAgICAgaWYgKCFmYWJyaWNDYW52YXNSZWYuY3VycmVudCkgcmV0dXJuO1xuXG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAvLyBTY2FsZSBpbWFnZSB0byBmaXQgdGhlIG9iamVjdCBib3VuZHNcbiAgICAgICAgICAgICAgY29uc3Qgc2NhbGVYID0gY2FudmFzT2JqZWN0LndpZHRoISAvIGltZy53aWR0aCE7XG4gICAgICAgICAgICAgIGNvbnN0IHNjYWxlWSA9IGNhbnZhc09iamVjdC5oZWlnaHQhIC8gaW1nLmhlaWdodCE7XG4gICAgICAgICAgICAgIGNvbnN0IHNjYWxlID0gTWF0aC5taW4oc2NhbGVYLCBzY2FsZVkpO1xuXG4gICAgICAgICAgICAgIGltZy5zZXQoe1xuICAgICAgICAgICAgICAgIGxlZnQ6IGNhbnZhc09iamVjdC5sZWZ0LFxuICAgICAgICAgICAgICAgIHRvcDogY2FudmFzT2JqZWN0LnRvcCxcbiAgICAgICAgICAgICAgICBzY2FsZVg6IHNjYWxlLFxuICAgICAgICAgICAgICAgIHNjYWxlWTogc2NhbGUsXG4gICAgICAgICAgICAgICAgb3JpZ2luWDogY2FudmFzT2JqZWN0Lm9yaWdpblgsXG4gICAgICAgICAgICAgICAgb3JpZ2luWTogY2FudmFzT2JqZWN0Lm9yaWdpblksXG4gICAgICAgICAgICAgICAgaWQ6IGxheWVyLmlkLCAvLyBQcmVzZXJ2ZSB0aGUgSURcbiAgICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgICAgLy8gUmVwbGFjZSB0aGUgb2JqZWN0XG4gICAgICAgICAgICAgIGZhYnJpY0NhbnZhc1JlZi5jdXJyZW50LnJlbW92ZShjYW52YXNPYmplY3QpO1xuICAgICAgICAgICAgICBmYWJyaWNDYW52YXNSZWYuY3VycmVudC5hZGQoaW1nKTtcbiAgICAgICAgICAgICAgZmFicmljQ2FudmFzUmVmLmN1cnJlbnQucmVuZGVyQWxsKCk7XG4gICAgICAgICAgICAgIGdlbmVyYXRlUHJldmlld0Zyb21DYW52YXMoKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFwcGx5aW5nIGltYWdlIGN1c3RvbWl6YXRpb246JywgZXJyb3IpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybjsgLy8gU2tpcCB0aGUgcmVuZGVyQWxsIGJlbG93IHNpbmNlIGl0J3MgaGFuZGxlZCBpbiB0aGUgY2FsbGJhY2tcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcbiAgICAgIGdlbmVyYXRlUHJldmlld0Zyb21DYW52YXMoKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYXBwbHlpbmcgY3VzdG9taXphdGlvbnM6JywgZXJyb3IpO1xuICAgIH1cbiAgfSwgW2N1c3RvbWl6YXRpb25zLCBjYW52YXNJbml0aWFsaXplZCwgdGVtcGxhdGUsIGdlbmVyYXRlUHJldmlld0Zyb21DYW52YXNdKTtcblxuICBjb25zdCBnZW5lcmF0ZVByZXZpZXcgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNHZW5lcmF0aW5nUHJldmlldyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgLy8gR2VuZXJhdGUgcHJldmlldyBmcm9tIGN1cnJlbnQgY2FudmFzIHN0YXRlXG4gICAgICBnZW5lcmF0ZVByZXZpZXdGcm9tQ2FudmFzKCk7XG4gICAgICAvLyBTbWFsbCBkZWxheSB0byBzaG93IGxvYWRpbmcgc3RhdGVcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZW5lcmF0ZSBwcmV2aWV3OicsIGVycik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzR2VuZXJhdGluZ1ByZXZpZXcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBkb3dubG9hZEN1c3RvbWl6ZWQgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNEb3dubG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgLy8gVGhpcyB3b3VsZCBjYWxsIGFuIEFQSSB0byBnZW5lcmF0ZSBhbmQgZG93bmxvYWQgdGhlIGN1c3RvbWl6ZWQgZGVzaWduXG4gICAgICAvLyBGb3Igbm93LCB3ZSdsbCBzaW11bGF0ZSBpdFxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDMwMDApKTtcbiAgICAgIFxuICAgICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB5b3UnZCBnZXQgYSBkb3dubG9hZCBVUkwgZnJvbSB0aGUgQVBJXG4gICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpO1xuICAgICAgbGluay5ocmVmID0gdGVtcGxhdGU/LnRodW1ibmFpbFVybCB8fCAnJztcbiAgICAgIGxpbmsuZG93bmxvYWQgPSBgY3VzdG9taXplZC0ke3RlbXBsYXRlPy5uYW1lIHx8ICdkZXNpZ24nfS5wbmdgO1xuICAgICAgbGluay5jbGljaygpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGRvd25sb2FkOicsIGVycik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzRG93bmxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtOCB3LTggYW5pbWF0ZS1zcGluIHRleHQtcHVycGxlLTYwMFwiIC8+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKGVycm9yIHx8ICF0ZW1wbGF0ZSkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cbiAgICAgICAgICAgICAge2Vycm9yIHx8IFwiVGVtcGxhdGUgbm90IGZvdW5kXCJ9XG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaChcIi9wdWJsaWNcIil9IHZhcmlhbnQ9XCJvdXRsaW5lXCI+XG4gICAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgQmFjayB0byBHYWxsZXJ5XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaChgL3B1YmxpYy8ke3BhcmFtcy5wcm9qZWN0SWR9YCl9IFxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiIFxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgQmFja1xuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTYgdy1weCBiZy1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICBDdXN0b21pemU6IHt0ZW1wbGF0ZS5uYW1lfVxuICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICBNYWtlIHRoaXMgdGVtcGxhdGUgeW91ciBvd25cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17Z2VuZXJhdGVQcmV2aWV3fVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0dlbmVyYXRpbmdQcmV2aWV3fVxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc0dlbmVyYXRpbmdQcmV2aWV3ID8gKFxuICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC00IHctNCBhbmltYXRlLXNwaW4gbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICAgICAgUHJldmlld1xuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2Rvd25sb2FkQ3VzdG9taXplZH1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNEb3dubG9hZGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzRG93bmxvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTQgdy00IGFuaW1hdGUtc3BpbiBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICBEb3dubG9hZFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMyBnYXAtOFwiPlxuICAgICAgICAgIHsvKiBDdXN0b21pemF0aW9uIFBhbmVsICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMVwiPlxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPkN1c3RvbWl6ZSBFbGVtZW50czwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICB7dGVtcGxhdGUuZWRpdGFibGVMYXllcnMubWFwKChsYXllcikgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2xheWVyLmlkfSBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD17bGF5ZXIudHlwZSA9PT0gJ3RleHQnID8gJ2RlZmF1bHQnIDogJ3NlY29uZGFyeSd9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2xheWVyLnR5cGUgPT09ICd0ZXh0JyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cGUgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZUljb24gY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICB7bGF5ZXIudHlwZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc21cIj57bGF5ZXIubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHtsYXllci50eXBlID09PSAndGV4dCcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2xheWVyLnBsYWNlaG9sZGVyIHx8ICdFbnRlciB0ZXh0J31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2N1c3RvbWl6YXRpb25zW2xheWVyLmlkXSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVUZXh0Q2hhbmdlKGxheWVyLmlkLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtsYXllci5wbGFjZWhvbGRlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4TGVuZ3RoPXtsYXllci5jb25zdHJhaW50cz8ubWF4TGVuZ3RofVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bGF5ZXIuY29uc3RyYWludHM/Lm1heExlbmd0aCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2N1c3RvbWl6YXRpb25zW2xheWVyLmlkXT8ubGVuZ3RoIHx8IDB9L3tsYXllci5jb25zdHJhaW50cy5tYXhMZW5ndGh9IGNoYXJhY3RlcnNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBVcGxvYWQgeW91ciBpbWFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMSBzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImZpbGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VwdD17bGF5ZXIuY29uc3RyYWludHM/LmFsbG93ZWRGb3JtYXRzPy5tYXAoZiA9PiBgLiR7Zn1gKS5qb2luKCcsJykgfHwgJ2ltYWdlLyonfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsZSA9IGUudGFyZ2V0LmZpbGVzPy5bMF07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZmlsZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJbWFnZVVwbG9hZChsYXllci5pZCwgZmlsZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgdGV4dC1zbSB0ZXh0LWdyYXktNTAwIGZpbGU6bXItNCBmaWxlOnB5LTIgZmlsZTpweC00IGZpbGU6cm91bmRlZC1mdWxsIGZpbGU6Ym9yZGVyLTAgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1zZW1pYm9sZCBmaWxlOmJnLXB1cnBsZS01MCBmaWxlOnRleHQtcHVycGxlLTcwMCBob3ZlcjpmaWxlOmJnLXB1cnBsZS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBTbWFsbCBwcmV2aWV3IG9mIHVwbG9hZGVkIGltYWdlICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y3VzdG9taXphdGlvbnNbbGF5ZXIuaWRdICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2N1c3RvbWl6YXRpb25zW2xheWVyLmlkXX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiVXBsb2FkZWQgcHJldmlld1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTIgaC0xMiBvYmplY3QtY292ZXIgcm91bmRlZCBib3JkZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMFwiPkltYWdlIHVwbG9hZGVkPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q3VzdG9taXphdGlvbnMocHJldiA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWQgPSB7IC4uLnByZXYgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIHVwZGF0ZWRbbGF5ZXIuaWRdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdXBkYXRlZDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXJlZC01MDAgaG92ZXI6dGV4dC1yZWQtNzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlbW92ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtsYXllci5jb25zdHJhaW50cz8ubWF4RmlsZVNpemUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTWF4IHNpemU6IHtNYXRoLnJvdW5kKGxheWVyLmNvbnN0cmFpbnRzLm1heEZpbGVTaXplIC8gMTAyNCAvIDEwMjQpfU1CXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBQcmV2aWV3IFBhbmVsICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPlByZXZpZXc8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmF5LTEwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBvdmVyZmxvdy1oaWRkZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIlxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgIGFzcGVjdFJhdGlvOiBgJHt0ZW1wbGF0ZS53aWR0aH0vJHt0ZW1wbGF0ZS5oZWlnaHR9YCxcbiAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDogXCIxMDAlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgbWF4SGVpZ2h0OiBcIjUwMHB4XCIsXG4gICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IFwiYXV0b1wiLFxuICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogXCJhdXRvXCJcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3ByZXZpZXdVcmwgfHwgdGVtcGxhdGUudGh1bWJuYWlsVXJsID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17cHJldmlld1VybCB8fCB0ZW1wbGF0ZS50aHVtYm5haWxVcmwgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICBhbHQ9XCJUZW1wbGF0ZSBwcmV2aWV3XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LWZ1bGwgbWF4LWgtZnVsbCBvYmplY3QtY29udGFpblwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogXCJhdXRvXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogXCJhdXRvXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1heFdpZHRoOiBcIjEwMCVcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4SGVpZ2h0OiBcIjEwMCVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS01MCB3LWZ1bGwgaC1mdWxsIG1pbi1oLVsyMDBweF1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZUljb24gY2xhc3NOYW1lPVwiaC0xMiB3LTEyIHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBIaWRkZW4gY2FudmFzIGZvciBGYWJyaWMuanMgcmVuZGVyaW5nICovfVxuICAgICAgPGNhbnZhc1xuICAgICAgICByZWY9e2NhbnZhc1JlZn1cbiAgICAgICAgc3R5bGU9e3sgZGlzcGxheTogJ25vbmUnIH19XG4gICAgICAgIHdpZHRoPXt0ZW1wbGF0ZT8ud2lkdGggfHwgODAwfVxuICAgICAgICBoZWlnaHQ9e3RlbXBsYXRlPy5oZWlnaHQgfHwgNjAwfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUNhbGxiYWNrIiwidXNlUGFyYW1zIiwidXNlUm91dGVyIiwiTG9hZGVyMiIsIkFycm93TGVmdCIsIkRvd25sb2FkIiwiVHlwZSIsIkltYWdlIiwiSW1hZ2VJY29uIiwiZmFicmljIiwiQnV0dG9uIiwiSW5wdXQiLCJMYWJlbCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCYWRnZSIsIkN1c3RvbWl6ZVRlbXBsYXRlUGFnZSIsInBhcmFtcyIsInJvdXRlciIsInRlbXBsYXRlIiwic2V0VGVtcGxhdGUiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJjdXN0b21pemF0aW9ucyIsInNldEN1c3RvbWl6YXRpb25zIiwicHJldmlld1VybCIsInNldFByZXZpZXdVcmwiLCJpc0dlbmVyYXRpbmdQcmV2aWV3Iiwic2V0SXNHZW5lcmF0aW5nUHJldmlldyIsImlzRG93bmxvYWRpbmciLCJzZXRJc0Rvd25sb2FkaW5nIiwiY2FudmFzSW5pdGlhbGl6ZWQiLCJzZXRDYW52YXNJbml0aWFsaXplZCIsImNhbnZhc1JlZiIsImZhYnJpY0NhbnZhc1JlZiIsImZldGNoVGVtcGxhdGUiLCJyZXNwb25zZSIsImZldGNoIiwicHJvamVjdElkIiwib2siLCJzdGF0dXMiLCJkYXRhIiwianNvbiIsInRlbXBsYXRlRGF0YSIsImlzQ3VzdG9taXphYmxlIiwiZWRpdGFibGVMYXllcnMiLCJKU09OIiwicGFyc2UiLCJjb25zb2xlIiwiaW5pdGlhbEN1c3RvbWl6YXRpb25zIiwiZm9yRWFjaCIsImxheWVyIiwiaWQiLCJvcmlnaW5hbFZhbHVlIiwiZXJyIiwiaGFuZGxlVGV4dENoYW5nZSIsImxheWVySWQiLCJ2YWx1ZSIsInByZXYiLCJoYW5kbGVJbWFnZVVwbG9hZCIsImZpbGUiLCJpbWFnZVVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImN1cnJlbnQiLCJkaXNwb3NlIiwid2FybiIsImluaXRDYW52YXMiLCJPYmplY3QiLCJwcm90b3R5cGUiLCJzZXQiLCJjb3JuZXJDb2xvciIsImNvcm5lclN0eWxlIiwiYm9yZGVyQ29sb3IiLCJib3JkZXJTY2FsZUZhY3RvciIsInRyYW5zcGFyZW50Q29ybmVycyIsImJvcmRlck9wYWNpdHlXaGVuTW92aW5nIiwiY29ybmVyU3Ryb2tlQ29sb3IiLCJUZXh0IiwidGV4dEJhc2VsaW5lIiwiVGV4dGJveCIsImNhbnZhcyIsIkNhbnZhcyIsIndpZHRoIiwiaGVpZ2h0Iiwic2VsZWN0aW9uIiwicHJlc2VydmVPYmplY3RTdGFja2luZyIsInRlbXBsYXRlSnNvbiIsIlByb21pc2UiLCJyZXNvbHZlIiwicmVqZWN0IiwibG9hZEZyb21KU09OIiwid29ya3NwYWNlIiwiZ2V0T2JqZWN0cyIsImZpbmQiLCJvYmoiLCJuYW1lIiwic2V0RGltZW5zaW9ucyIsImxlZnQiLCJ0b3AiLCJyZW5kZXJBbGwiLCJzZXRUaW1lb3V0IiwiZ2VuZXJhdGVQcmV2aWV3RnJvbUNhbnZhcyIsImpzb25FcnJvciIsInRodW1ibmFpbFVybCIsInRpbWVvdXRJZCIsImNsZWFyVGltZW91dCIsIm1heFByZXZpZXdTaXplIiwidGVtcGxhdGVNYXhEaW1lbnNpb24iLCJNYXRoIiwibWF4IiwibXVsdGlwbGllciIsIm1pbiIsImRhdGFVUkwiLCJ0b0RhdGFVUkwiLCJmb3JtYXQiLCJxdWFsaXR5Iiwib2JqZWN0cyIsImN1c3RvbVZhbHVlIiwiY2FudmFzT2JqZWN0IiwidHlwZSIsInN0YXJ0c1dpdGgiLCJmcm9tVVJMIiwiaW1nIiwic2NhbGVYIiwic2NhbGVZIiwic2NhbGUiLCJvcmlnaW5YIiwib3JpZ2luWSIsInJlbW92ZSIsImFkZCIsImdlbmVyYXRlUHJldmlldyIsImRvd25sb2FkQ3VzdG9taXplZCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJjbGljayIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwib25DbGljayIsInB1c2giLCJ2YXJpYW50Iiwic2l6ZSIsInAiLCJkaXNhYmxlZCIsIm1hcCIsInNwYW4iLCJwbGFjZWhvbGRlciIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm1heExlbmd0aCIsImNvbnN0cmFpbnRzIiwibGVuZ3RoIiwiaW5wdXQiLCJhY2NlcHQiLCJhbGxvd2VkRm9ybWF0cyIsImYiLCJqb2luIiwiZmlsZXMiLCJzcmMiLCJhbHQiLCJidXR0b24iLCJ1cGRhdGVkIiwibWF4RmlsZVNpemUiLCJyb3VuZCIsInN0eWxlIiwiYXNwZWN0UmF0aW8iLCJtYXhXaWR0aCIsIm1heEhlaWdodCIsInJlZiIsImRpc3BsYXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ })

});