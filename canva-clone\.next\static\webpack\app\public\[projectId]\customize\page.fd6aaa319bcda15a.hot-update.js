"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/public/[projectId]/customize/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomizeTemplatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Image,Loader2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction CustomizeTemplatePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [template, setTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingPreview, setIsGeneratingPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [canvasInitialized, setCanvasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Simplified approach - no canvas refs needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTemplate = async ()=>{\n            try {\n                const response = await fetch(\"/api/projects/public/\".concat(params.projectId));\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        setError(\"Template not found or not public\");\n                    } else {\n                        setError(\"Failed to load template\");\n                    }\n                    return;\n                }\n                const data = await response.json();\n                const templateData = data.data;\n                // Check if template is customizable\n                if (!templateData.isCustomizable || !templateData.editableLayers) {\n                    setError(\"This template is not customizable\");\n                    return;\n                }\n                // Parse editable layers\n                const editableLayers = JSON.parse(templateData.editableLayers);\n                setTemplate({\n                    ...templateData,\n                    editableLayers\n                });\n                // Initialize customizations with original values\n                const initialCustomizations = {};\n                editableLayers.forEach((layer)=>{\n                    initialCustomizations[layer.id] = layer.originalValue || \"\";\n                });\n                setCustomizations(initialCustomizations);\n            } catch (err) {\n                setError(\"Failed to load template\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (params.projectId) {\n            fetchTemplate();\n        }\n    }, [\n        params.projectId\n    ]);\n    const handleTextChange = (layerId, value)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: value\n            }));\n    };\n    const handleImageUpload = async (layerId, file)=>{\n        // Create object URL for immediate preview\n        const imageUrl = URL.createObjectURL(file);\n        // Update customizations immediately\n        setCustomizations((prev)=>({\n                ...prev,\n                [layerId]: imageUrl\n            }));\n    // In a real implementation, you'd upload to your storage service here\n    // const uploadedUrl = await uploadToStorage(file);\n    // setCustomizations(prev => ({ ...prev, [layerId]: uploadedUrl }));\n    };\n    // Initialize preview - use simple approach without Fabric.js canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!template) return;\n        // Simply use the template thumbnail as preview\n        // This avoids all the Fabric.js canvas issues\n        if (template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n            setCanvasInitialized(true);\n        }\n    // For now, we'll skip the complex canvas rendering to avoid errors\n    // In a production app, you might want to use a server-side rendering service\n    // or a simpler canvas approach that doesn't rely on Fabric.js\n    }, [\n        template\n    ]);\n    // Simple preview generation - just use template thumbnail\n    const generatePreviewFromCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // For now, always use the template thumbnail\n        // This avoids all canvas-related errors\n        if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n        }\n    }, [\n        template\n    ]);\n    // Handle customizations - simplified approach to avoid canvas errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // For now, we'll just use the template thumbnail regardless of customizations\n        // This completely avoids all Fabric.js canvas issues\n        // In a production app, you'd want to implement server-side rendering\n        // or use a more reliable canvas approach\n        if (template === null || template === void 0 ? void 0 : template.thumbnailUrl) {\n            setPreviewUrl(template.thumbnailUrl);\n        }\n    }, [\n        customizations,\n        template\n    ]);\n    const generatePreview = async ()=>{\n        setIsGeneratingPreview(true);\n        try {\n            // Generate preview from current canvas state\n            generatePreviewFromCanvas();\n            // Small delay to show loading state\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        } catch (err) {\n            console.error(\"Failed to generate preview:\", err);\n        } finally{\n            setIsGeneratingPreview(false);\n        }\n    };\n    const downloadCustomized = async ()=>{\n        setIsDownloading(true);\n        try {\n            // This would call an API to generate and download the customized design\n            // For now, we'll simulate it\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // In a real implementation, you'd get a download URL from the API\n            const link = document.createElement(\"a\");\n            link.href = (template === null || template === void 0 ? void 0 : template.thumbnailUrl) || \"\";\n            link.download = \"customized-\".concat((template === null || template === void 0 ? void 0 : template.name) || \"design\", \".png\");\n            link.click();\n        } catch (err) {\n            console.error(\"Failed to download:\", err);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !template) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: error || \"Template not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: ()=>router.push(\"/public\"),\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>router.push(\"/public/\".concat(params.projectId)),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: [\n                                                    \"Customize: \",\n                                                    template.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Make this template your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: generatePreview,\n                                        disabled: isGeneratingPreview,\n                                        variant: \"outline\",\n                                        children: [\n                                            isGeneratingPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this) : null,\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: downloadCustomized,\n                                        disabled: isDownloading,\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        children: [\n                                            isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Customize Elements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: template.editableLayers.map((layer)=>{\n                                            var _layer_constraints, _layer_constraints1, _customizations_layer_id, _layer_constraints_allowedFormats, _layer_constraints2, _layer_constraints3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                                children: [\n                                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    layer.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: layer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: layer.placeholder || \"Enter text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: customizations[layer.id] || \"\",\n                                                                onChange: (e)=>handleTextChange(layer.id, e.target.value),\n                                                                placeholder: layer.placeholder,\n                                                                maxLength: (_layer_constraints = layer.constraints) === null || _layer_constraints === void 0 ? void 0 : _layer_constraints.maxLength,\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ((_layer_constraints1 = layer.constraints) === null || _layer_constraints1 === void 0 ? void 0 : _layer_constraints1.maxLength) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    ((_customizations_layer_id = customizations[layer.id]) === null || _customizations_layer_id === void 0 ? void 0 : _customizations_layer_id.length) || 0,\n                                                                    \"/\",\n                                                                    layer.constraints.maxLength,\n                                                                    \" characters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Upload your image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"file\",\n                                                                        accept: ((_layer_constraints2 = layer.constraints) === null || _layer_constraints2 === void 0 ? void 0 : (_layer_constraints_allowedFormats = _layer_constraints2.allowedFormats) === null || _layer_constraints_allowedFormats === void 0 ? void 0 : _layer_constraints_allowedFormats.map((f)=>\".\".concat(f)).join(\",\")) || \"image/*\",\n                                                                        onChange: (e)=>{\n                                                                            var _e_target_files;\n                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                            if (file) {\n                                                                                handleImageUpload(layer.id, file);\n                                                                            }\n                                                                        },\n                                                                        className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    customizations[layer.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: customizations[layer.id],\n                                                                                alt: \"Uploaded preview\",\n                                                                                className: \"w-12 h-12 object-cover rounded border\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 314,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Image uploaded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomizations((prev)=>{\n                                                                                                const updated = {\n                                                                                                    ...prev\n                                                                                                };\n                                                                                                delete updated[layer.id];\n                                                                                                return updated;\n                                                                                            });\n                                                                                        },\n                                                                                        className: \"text-xs text-red-500 hover:text-red-700\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                        lineNumber: 321,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                                lineNumber: 319,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ((_layer_constraints3 = layer.constraints) === null || _layer_constraints3 === void 0 ? void 0 : _layer_constraints3.maxFileSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Max size: \",\n                                                                            Math.round(layer.constraints.maxFileSize / 1024 / 1024),\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, layer.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center bg-gray-100 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-lg overflow-hidden flex items-center justify-center\",\n                                                style: {\n                                                    aspectRatio: \"\".concat(template.width, \"/\").concat(template.height),\n                                                    maxWidth: \"100%\",\n                                                    maxHeight: \"500px\",\n                                                    width: \"auto\",\n                                                    height: \"auto\"\n                                                },\n                                                children: previewUrl || template.thumbnailUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: previewUrl || template.thumbnailUrl || \"\",\n                                                    alt: \"Template preview\",\n                                                    className: \"max-w-full max-h-full object-contain\",\n                                                    style: {\n                                                        width: \"auto\",\n                                                        height: \"auto\",\n                                                        maxWidth: \"100%\",\n                                                        maxHeight: \"100%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center bg-gray-50 w-full h-full min-h-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Image_Loader2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\public\\\\[projectId]\\\\customize\\\\page.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomizeTemplatePage, \"bDAnX+1m+b6zO+JfNpWoxt0v8PE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomizeTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/public/[projectId]/customize/page.tsx\n"));

/***/ })

});