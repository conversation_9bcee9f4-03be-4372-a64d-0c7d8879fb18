"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx":
/*!********************************************************************!*\
  !*** ./src/features/editor/components/template-config-sidebar.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateConfigSidebar: function() { return /* binding */ TemplateConfigSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/projects/api/use-update-template-config */ \"(app-pages-browser)/./src/features/projects/api/use-update-template-config.ts\");\n/* __next_internal_client_entry_do_not_use__ TemplateConfigSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst TemplateConfigSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool, projectId, initialData } = param;\n    _s();\n    const [editableLayers, setEditableLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCustomizable, setIsCustomizable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasLoadedInitialData, setHasLoadedInitialData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateTemplateConfig = (0,_features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig)(projectId || \"\");\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    const canvas = editor === null || editor === void 0 ? void 0 : editor.canvas;\n    const selectedObjects = (editor === null || editor === void 0 ? void 0 : editor.selectedObjects) || [];\n    // Load existing template configuration from database (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData && !hasLoadedInitialData) {\n            setIsCustomizable(initialData.isCustomizable || false);\n            if (initialData.editableLayers) {\n                try {\n                    const parsedLayers = JSON.parse(initialData.editableLayers);\n                    setEditableLayers(parsedLayers);\n                } catch (error) {\n                    console.error(\"Failed to parse editable layers:\", error);\n                }\n            }\n            setHasLoadedInitialData(true);\n        }\n    }, [\n        initialData,\n        hasLoadedInitialData\n    ]);\n    // Apply editable properties to canvas objects when canvas is ready and layers are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (canvas && editableLayers.length > 0 && hasLoadedInitialData) {\n            editableLayers.forEach((layer)=>{\n                const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n                if (canvasObject) {\n                    canvasObject.isEditable = true;\n                    canvasObject.editableType = layer.type;\n                    canvasObject.editableName = layer.name;\n                    canvasObject.editablePlaceholder = layer.placeholder;\n                    canvasObject.editableConstraints = layer.constraints;\n                }\n            });\n            canvas.renderAll();\n        }\n    }, [\n        canvas,\n        editableLayers,\n        hasLoadedInitialData\n    ]);\n    // Load existing editable layers from canvas objects (fallback)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvas || editableLayers.length > 0) return;\n        const layers = [];\n        canvas.getObjects().forEach((obj)=>{\n            const editableObj = obj;\n            if (editableObj.isEditable) {\n                layers.push({\n                    id: editableObj.id || \"\",\n                    type: editableObj.editableType || \"text\",\n                    name: editableObj.editableName || \"Unnamed Layer\",\n                    originalValue: editableObj.type === \"textbox\" ? editableObj.text : \"\",\n                    placeholder: editableObj.editablePlaceholder,\n                    constraints: editableObj.editableConstraints\n                });\n            }\n        });\n        if (layers.length > 0) {\n            setEditableLayers(layers);\n            setIsCustomizable(true);\n        }\n    }, [\n        canvas,\n        editableLayers.length\n    ]);\n    const makeLayerEditable = (type)=>{\n        if (!canvas || selectedObjects.length === 0) return;\n        const selectedObject = selectedObjects[0];\n        // Validate object type\n        if (type === \"text\" && selectedObject.type !== \"textbox\") {\n            alert(\"Please select a text object to make it editable\");\n            return;\n        }\n        if (type === \"image\" && ![\n            \"image\",\n            \"rect\",\n            \"circle\"\n        ].includes(selectedObject.type || \"\")) {\n            alert(\"Please select an image or shape to make it editable\");\n            return;\n        }\n        // Mark object as editable\n        selectedObject.isEditable = true;\n        selectedObject.editableType = type;\n        selectedObject.editableName = \"\".concat(type === \"text\" ? \"Text\" : \"Image\", \" \").concat(editableLayers.length + 1);\n        if (type === \"text\") {\n            selectedObject.editablePlaceholder = \"Enter your text here...\";\n        }\n        // Add to editable layers list\n        const newLayer = {\n            id: selectedObject.id || \"layer_\".concat(Date.now()),\n            type,\n            name: selectedObject.editableName,\n            originalValue: type === \"text\" ? selectedObject.text : \"\",\n            placeholder: selectedObject.editablePlaceholder,\n            constraints: {\n                maxLength: type === \"text\" ? 100 : undefined,\n                allowedFormats: type === \"image\" ? [\n                    \"jpg\",\n                    \"jpeg\",\n                    \"png\",\n                    \"gif\"\n                ] : undefined,\n                maxFileSize: type === \"image\" ? 5 * 1024 * 1024 : undefined\n            }\n        };\n        // Assign ID if not exists\n        if (!selectedObject.id) {\n            selectedObject.id = newLayer.id;\n        }\n        setEditableLayers([\n            ...editableLayers,\n            newLayer\n        ]);\n        // Don't automatically enable the toggle - let user control it manually\n        canvas.renderAll();\n    };\n    const removeEditableLayer = (layerId)=>{\n        // Find and update the canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.isEditable = false;\n            delete canvasObject.editableType;\n            delete canvasObject.editableName;\n            delete canvasObject.editablePlaceholder;\n            delete canvasObject.editableConstraints;\n        }\n        // Remove from layers list\n        const updatedLayers = editableLayers.filter((layer)=>layer.id !== layerId);\n        setEditableLayers(updatedLayers);\n        // Don't automatically disable the toggle - let user control it manually\n        canvas === null || canvas === void 0 ? void 0 : canvas.renderAll();\n    };\n    const updateLayerName = (layerId, name)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                name\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editableName = name;\n        }\n    };\n    const updateLayerPlaceholder = (layerId, placeholder)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                placeholder\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editablePlaceholder = placeholder;\n        }\n    };\n    const saveTemplateConfig = ()=>{\n        if (!projectId) {\n            alert(\"Project ID is required to save template configuration\");\n            return;\n        }\n        updateTemplateConfig.mutate({\n            isCustomizable,\n            editableLayers: JSON.stringify(editableLayers)\n        }, {\n            onSuccess: ()=>{\n                // Close sidebar after successful save\n                setTimeout(()=>{\n                    onClose();\n                }, 500); // Small delay to ensure state is updated\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"template-config\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"Template Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: \"Configure which elements users can customize\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Template Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"customizable\",\n                                                className: \"text-sm\",\n                                                children: \"Make Customizable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                                id: \"customizable\",\n                                                checked: isCustomizable,\n                                                onCheckedChange: setIsCustomizable\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Allow public users to customize this template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: \"Add Editable Elements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"text\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Text Editable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"image\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Image Replaceable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    selectedObjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Select an element on the canvas first\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, undefined),\n                    editableLayers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: [\n                                        \"Editable Elements (\",\n                                        editableLayers.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-3\",\n                                children: editableLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                        variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                        children: [\n                                                            layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            layer.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeEditableLayer(layer.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Display Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.name,\n                                                                onChange: (e)=>updateLayerName(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"e.g., Your Name, Profile Photo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    layer.type === \"text\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Placeholder Text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.placeholder || \"\",\n                                                                onChange: (e)=>updateLayerPlaceholder(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"Enter placeholder text...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, layer.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: saveTemplateConfig,\n                    className: \"w-full\",\n                    disabled: updateTemplateConfig.isPending,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, undefined),\n                        updateTemplateConfig.isPending ? \"Saving...\" : \"Save Template Config\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 376,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateConfigSidebar, \"jMWZ7D+oEsDeK2D8A3TiN5W25SE=\", false, function() {\n    return [\n        _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig\n    ];\n});\n_c = TemplateConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TemplateConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\n"));

/***/ })

});