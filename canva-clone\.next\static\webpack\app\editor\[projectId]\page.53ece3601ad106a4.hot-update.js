"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts":
/*!******************************************************!*\
  !*** ./src/features/editor/hooks/use-auto-resize.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoResize: function() { return /* binding */ useAutoResize; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst useAutoResize = (param)=>{\n    let { canvas, container } = param;\n    const autoZoom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!canvas || !container) return;\n        const width = container.offsetWidth;\n        const height = container.offsetHeight;\n        // Ensure we have valid dimensions\n        if (width <= 0 || height <= 0) {\n            console.warn(\"Container has invalid dimensions for autoZoom\", {\n                width,\n                height\n            });\n            return;\n        }\n        canvas.setWidth(width);\n        canvas.setHeight(height);\n        const center = canvas.getCenter();\n        const zoomRatio = 0.85;\n        const localWorkspace = canvas.getObjects().find((object)=>object.name === \"clip\");\n        if (!localWorkspace) return;\n        // Validate workspace dimensions\n        const workspaceWidth = localWorkspace.width || 0;\n        const workspaceHeight = localWorkspace.height || 0;\n        if (workspaceWidth <= 0 || workspaceHeight <= 0) {\n            console.warn(\"Workspace has invalid dimensions for autoZoom\", {\n                workspaceWidth,\n                workspaceHeight\n            });\n            return;\n        }\n        // @ts-ignore\n        const scale = fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.util.findScaleToFit(localWorkspace, {\n            width: width,\n            height: height\n        });\n        const zoom = zoomRatio * scale;\n        canvas.setViewportTransform(fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.iMatrix.concat());\n        canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoom);\n        const workspaceCenter = localWorkspace.getCenterPoint();\n        const viewportTransform = canvas.viewportTransform;\n        if (canvas.width === undefined || canvas.height === undefined || !viewportTransform) {\n            return;\n        }\n        viewportTransform[4] = canvas.width / 2 - workspaceCenter.x * viewportTransform[0];\n        viewportTransform[5] = canvas.height / 2 - workspaceCenter.y * viewportTransform[3];\n        canvas.setViewportTransform(viewportTransform);\n        localWorkspace.clone((cloned)=>{\n            canvas.clipPath = cloned;\n            canvas.requestRenderAll();\n        });\n    }, [\n        canvas,\n        container\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let resizeObserver = null;\n        if (canvas && container) {\n            resizeObserver = new ResizeObserver(()=>{\n                autoZoom();\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        canvas,\n        container,\n        autoZoom\n    ]);\n    return {\n        autoZoom\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\n"));

/***/ })

});