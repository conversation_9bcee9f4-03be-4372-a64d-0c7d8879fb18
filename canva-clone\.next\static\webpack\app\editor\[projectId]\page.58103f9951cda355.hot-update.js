"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts":
/*!**************************************************************!*\
  !*** ./src/features/editor/hooks/use-thumbnail-generator.ts ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useThumbnailGenerator: function() { return /* binding */ useThumbnailGenerator; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hono__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hono */ \"(app-pages-browser)/./src/lib/hono.ts\");\n\n\n\nconst useThumbnailGenerator = (param)=>{\n    let { editor, projectId, onThumbnailGenerated } = param;\n    const lastThumbnailRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const generateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!editor) return;\n        try {\n            // Generate thumbnail data URL with ultra-high quality (Canva-level)\n            const thumbnailDataUrl = editor.generateThumbnail({\n                width: 600,\n                height: 400,\n                quality: 1.0\n            });\n            // Skip if thumbnail hasn't changed significantly\n            if (lastThumbnailRef.current === thumbnailDataUrl) {\n                return;\n            }\n            lastThumbnailRef.current = thumbnailDataUrl;\n            // For now, let's store the thumbnail as a data URL directly in the database\n            // This is simpler and doesn't require external file uploads\n            // In production, you might want to upload to a CDN\n            // Update project with thumbnail data URL\n            const response = await _lib_hono__WEBPACK_IMPORTED_MODULE_2__.client.api.projects[\":id\"].$patch({\n                param: {\n                    id: projectId\n                },\n                json: {\n                    thumbnailUrl: thumbnailDataUrl\n                }\n            });\n            if (response.ok) {\n                onThumbnailGenerated === null || onThumbnailGenerated === void 0 ? void 0 : onThumbnailGenerated(thumbnailDataUrl);\n            }\n        } catch (error) {\n            console.error(\"Error generating thumbnail:\", error);\n        }\n    }, [\n        editor,\n        projectId,\n        onThumbnailGenerated\n    ]);\n    // Debounced version to avoid too frequent thumbnail generation\n    const debouncedGenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default()(generateThumbnail, 2000), [\n        generateThumbnail\n    ]);\n    const forceRegenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        // Reset the last thumbnail to force regeneration\n        lastThumbnailRef.current = null;\n        await generateThumbnail();\n    }, [\n        generateThumbnail\n    ]);\n    return {\n        generateThumbnail,\n        debouncedGenerateThumbnail,\n        forceRegenerateThumbnail\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\n"));

/***/ })

});