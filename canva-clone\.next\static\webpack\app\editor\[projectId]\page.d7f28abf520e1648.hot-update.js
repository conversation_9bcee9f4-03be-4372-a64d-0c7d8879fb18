"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_utils_thumbnail__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/utils/thumbnail */ \"(app-pages-browser)/./src/features/editor/utils/thumbnail.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        try {\n            const data = JSON.parse(json);\n            // Validate canvas dimensions before loading\n            const canvasWidth = canvas.getWidth();\n            const canvasHeight = canvas.getHeight();\n            if (canvasWidth <= 0 || canvasHeight <= 0) {\n                console.warn(\"Cannot load JSON: Canvas has invalid dimensions\", {\n                    canvasWidth,\n                    canvasHeight\n                });\n                return;\n            }\n            canvas.loadFromJSON(data, ()=>{\n                // Ensure all objects have valid dimensions after loading\n                canvas.getObjects().forEach((obj)=>{\n                    if (obj.width === 0 || obj.height === 0) {\n                        console.warn(\"Object has zero dimensions after loading:\", obj);\n                        // Set minimum dimensions to prevent rendering errors\n                        if (obj.width === 0) obj.set(\"width\", 1);\n                        if (obj.height === 0) obj.set(\"height\", 1);\n                    }\n                });\n                autoZoom();\n            });\n        } catch (error) {\n            console.error(\"Error loading JSON:\", error);\n        }\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        generateThumbnail: (options)=>{\n            return (0,_features_editor_utils_thumbnail__WEBPACK_IMPORTED_MODULE_5__.generateThumbnail)(canvas, options);\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_7__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_8__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_9__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_6__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        // Fix fabric.js textBaseline issue\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Text.prototype.set({\n            textBaseline: \"middle\"\n        });\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox.prototype.set({\n            textBaseline: \"middle\"\n        });\n        // Ensure we have valid dimensions\n        const containerWidth = initialContainer.offsetWidth || 800;\n        const containerHeight = initialContainer.offsetHeight || 600;\n        const workspaceWidth = initialWidth.current || 900;\n        const workspaceHeight = initialHeight.current || 1200;\n        // Validate dimensions\n        if (workspaceWidth <= 0 || workspaceHeight <= 0) {\n            console.error(\"Invalid workspace dimensions:\", {\n                workspaceWidth,\n                workspaceHeight\n            });\n            return;\n        }\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: workspaceWidth,\n            height: workspaceHeight,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(containerWidth);\n        initialCanvas.setHeight(containerHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ })

});