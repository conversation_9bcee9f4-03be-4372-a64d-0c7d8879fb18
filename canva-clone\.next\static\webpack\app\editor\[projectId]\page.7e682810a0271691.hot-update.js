"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx":
/*!********************************************************************!*\
  !*** ./src/features/editor/components/template-config-sidebar.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateConfigSidebar: function() { return /* binding */ TemplateConfigSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/projects/api/use-update-template-config */ \"(app-pages-browser)/./src/features/projects/api/use-update-template-config.ts\");\n/* __next_internal_client_entry_do_not_use__ TemplateConfigSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst TemplateConfigSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool, projectId, initialData } = param;\n    _s();\n    const [editableLayers, setEditableLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCustomizable, setIsCustomizable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasLoadedInitialData, setHasLoadedInitialData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateTemplateConfig = (0,_features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig)(projectId || \"\");\n    // Reset success state after 3 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (updateTemplateConfig.isSuccess) {\n            const timer = setTimeout(()=>{\n                updateTemplateConfig.reset();\n            }, 3000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        updateTemplateConfig.isSuccess,\n        updateTemplateConfig\n    ]);\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    const canvas = editor === null || editor === void 0 ? void 0 : editor.canvas;\n    const selectedObjects = (editor === null || editor === void 0 ? void 0 : editor.selectedObjects) || [];\n    // Load existing template configuration from database (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData && !hasLoadedInitialData) {\n            setIsCustomizable(initialData.isCustomizable || false);\n            if (initialData.editableLayers) {\n                try {\n                    const parsedLayers = JSON.parse(initialData.editableLayers);\n                    setEditableLayers(parsedLayers);\n                } catch (error) {\n                    console.error(\"Failed to parse editable layers:\", error);\n                }\n            }\n            setHasLoadedInitialData(true);\n        }\n    }, [\n        initialData,\n        hasLoadedInitialData\n    ]);\n    // Apply editable properties to canvas objects when canvas is ready and layers are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (canvas && editableLayers.length > 0 && hasLoadedInitialData) {\n            editableLayers.forEach((layer)=>{\n                const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n                if (canvasObject) {\n                    canvasObject.isEditable = true;\n                    canvasObject.editableType = layer.type;\n                    canvasObject.editableName = layer.name;\n                    canvasObject.editablePlaceholder = layer.placeholder;\n                    canvasObject.editableConstraints = layer.constraints;\n                }\n            });\n            canvas.renderAll();\n        }\n    }, [\n        canvas,\n        editableLayers,\n        hasLoadedInitialData\n    ]);\n    // Load existing editable layers from canvas objects (fallback)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvas || editableLayers.length > 0) return;\n        const layers = [];\n        canvas.getObjects().forEach((obj)=>{\n            const editableObj = obj;\n            if (editableObj.isEditable) {\n                layers.push({\n                    id: editableObj.id || \"\",\n                    type: editableObj.editableType || \"text\",\n                    name: editableObj.editableName || \"Unnamed Layer\",\n                    originalValue: editableObj.type === \"textbox\" ? editableObj.text : \"\",\n                    placeholder: editableObj.editablePlaceholder,\n                    constraints: editableObj.editableConstraints\n                });\n            }\n        });\n        if (layers.length > 0) {\n            setEditableLayers(layers);\n            setIsCustomizable(true);\n        }\n    }, [\n        canvas,\n        editableLayers.length\n    ]);\n    const makeLayerEditable = (type)=>{\n        if (!canvas || selectedObjects.length === 0) return;\n        const selectedObject = selectedObjects[0];\n        // Validate object type\n        if (type === \"text\" && selectedObject.type !== \"textbox\") {\n            alert(\"Please select a text object to make it editable\");\n            return;\n        }\n        if (type === \"image\" && ![\n            \"image\",\n            \"rect\",\n            \"circle\"\n        ].includes(selectedObject.type || \"\")) {\n            alert(\"Please select an image or shape to make it editable\");\n            return;\n        }\n        // Mark object as editable\n        selectedObject.isEditable = true;\n        selectedObject.editableType = type;\n        selectedObject.editableName = \"\".concat(type === \"text\" ? \"Text\" : \"Image\", \" \").concat(editableLayers.length + 1);\n        if (type === \"text\") {\n            selectedObject.editablePlaceholder = \"Enter your text here...\";\n        }\n        // Add to editable layers list\n        const newLayer = {\n            id: selectedObject.id || \"layer_\".concat(Date.now()),\n            type,\n            name: selectedObject.editableName,\n            originalValue: type === \"text\" ? selectedObject.text : \"\",\n            placeholder: selectedObject.editablePlaceholder,\n            constraints: {\n                maxLength: type === \"text\" ? 100 : undefined,\n                allowedFormats: type === \"image\" ? [\n                    \"jpg\",\n                    \"jpeg\",\n                    \"png\",\n                    \"gif\"\n                ] : undefined,\n                maxFileSize: type === \"image\" ? 5 * 1024 * 1024 : undefined\n            }\n        };\n        // Assign ID if not exists\n        if (!selectedObject.id) {\n            selectedObject.id = newLayer.id;\n        }\n        setEditableLayers([\n            ...editableLayers,\n            newLayer\n        ]);\n        // Don't automatically enable the toggle - let user control it manually\n        canvas.renderAll();\n    };\n    const removeEditableLayer = (layerId)=>{\n        // Find and update the canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.isEditable = false;\n            delete canvasObject.editableType;\n            delete canvasObject.editableName;\n            delete canvasObject.editablePlaceholder;\n            delete canvasObject.editableConstraints;\n        }\n        // Remove from layers list\n        const updatedLayers = editableLayers.filter((layer)=>layer.id !== layerId);\n        setEditableLayers(updatedLayers);\n        // Don't automatically disable the toggle - let user control it manually\n        canvas === null || canvas === void 0 ? void 0 : canvas.renderAll();\n    };\n    const updateLayerName = (layerId, name)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                name\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editableName = name;\n        }\n    };\n    const updateLayerPlaceholder = (layerId, placeholder)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                placeholder\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editablePlaceholder = placeholder;\n        }\n    };\n    const saveTemplateConfig = ()=>{\n        if (!projectId) {\n            alert(\"Project ID is required to save template configuration\");\n            return;\n        }\n        updateTemplateConfig.mutate({\n            isCustomizable,\n            editableLayers: JSON.stringify(editableLayers)\n        }, {\n            onSuccess: ()=>{\n            // Don't close sidebar automatically - let user see the success state\n            // They can close it manually if needed\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"template-config\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"Template Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: \"Configure which elements users can customize\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Template Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isCustomizable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                            variant: \"default\",\n                                            className: \"ml-2\",\n                                            children: \"Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"customizable\",\n                                                className: \"text-sm\",\n                                                children: \"Make Customizable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                                id: \"customizable\",\n                                                checked: isCustomizable,\n                                                onCheckedChange: setIsCustomizable\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: isCustomizable ? \"Template is customizable with \".concat(editableLayers.length, \" editable element\").concat(editableLayers.length !== 1 ? \"s\" : \"\") : \"Allow public users to customize this template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: \"Add Editable Elements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"text\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Text Editable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"image\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Image Replaceable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    selectedObjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Select an element on the canvas first\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, undefined),\n                    editableLayers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: [\n                                        \"Editable Elements (\",\n                                        editableLayers.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-3\",\n                                children: editableLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                        variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                        children: [\n                                                            layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            layer.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeEditableLayer(layer.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Display Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.name,\n                                                                onChange: (e)=>updateLayerName(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"e.g., Your Name, Profile Photo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    layer.type === \"text\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Placeholder Text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.placeholder || \"\",\n                                                                onChange: (e)=>updateLayerPlaceholder(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"Enter placeholder text...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, layer.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: saveTemplateConfig,\n                    className: \"w-full\",\n                    disabled: updateTemplateConfig.isPending,\n                    variant: updateTemplateConfig.isSuccess ? \"default\" : \"default\",\n                    children: updateTemplateConfig.isPending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 15\n                            }, undefined),\n                            \"Saving...\"\n                        ]\n                    }, void 0, true) : updateTemplateConfig.isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 15\n                            }, undefined),\n                            \"Saved Successfully!\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 15\n                            }, undefined),\n                            \"Save Template Config\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 393,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateConfigSidebar, \"Pj9UBJ5djFNUUoGgltxp9+oeZYc=\", false, function() {\n    return [\n        _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig\n    ];\n});\n_c = TemplateConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TemplateConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\n"));

/***/ })

});