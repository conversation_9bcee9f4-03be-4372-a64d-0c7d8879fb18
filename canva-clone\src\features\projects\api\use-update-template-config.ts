import { InferRequestType, InferResponseType } from "hono";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { client } from "@/lib/hono";

type ResponseType = InferResponseType<typeof client.api.projects[":id"]["template-config"]["$patch"], 200>;
type RequestType = InferRequestType<typeof client.api.projects[":id"]["template-config"]["$patch"]>;

export const useUpdateTemplateConfig = (id: string) => {
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseType, Error, RequestType["json"]>({
    mutationFn: async (json) => {
      const response = await client.api.projects[":id"]["template-config"].$patch({
        param: { id },
        json,
      });

      if (!response.ok) {
        throw new Error("Failed to update template configuration");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Template configuration saved successfully!");
      
      // Invalidate and refetch projects list
      queryClient.invalidateQueries({ queryKey: ["projects"] });
      
      // Update the specific project in cache
      queryClient.invalidateQueries({ queryKey: ["project", { id }] });
    },
    onError: () => {
      toast.error("Failed to save template configuration");
    },
  });

  return mutation;
};
