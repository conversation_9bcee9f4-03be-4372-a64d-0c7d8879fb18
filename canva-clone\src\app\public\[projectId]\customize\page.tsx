"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Loader2, ArrowLeft, Download, Upload, Type, Image as ImageIcon } from "lucide-react";
import { fabric } from "fabric";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { TemplateResponse, EditableLayer } from "@/types/template";

export default function CustomizeTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const [template, setTemplate] = useState<TemplateResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customizations, setCustomizations] = useState<Record<string, string>>({});
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [canvasInitialized, setCanvasInitialized] = useState(false);

  // Canvas refs for preview generation
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);

  useEffect(() => {
    const fetchTemplate = async () => {
      try {
        const response = await fetch(`/api/projects/public/${params.projectId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setError("Template not found or not public");
          } else {
            setError("Failed to load template");
          }
          return;
        }
        
        const data = await response.json();
        const templateData = data.data;
        
        // Check if template is customizable
        if (!templateData.isCustomizable || !templateData.editableLayers) {
          setError("This template is not customizable");
          return;
        }

        // Parse editable layers
        const editableLayers: EditableLayer[] = JSON.parse(templateData.editableLayers);
        
        setTemplate({
          ...templateData,
          editableLayers,
        });

        // Initialize customizations with original values
        const initialCustomizations: Record<string, string> = {};
        editableLayers.forEach(layer => {
          initialCustomizations[layer.id] = layer.originalValue || '';
        });
        setCustomizations(initialCustomizations);
        
      } catch (err) {
        setError("Failed to load template");
      } finally {
        setLoading(false);
      }
    };

    if (params.projectId) {
      fetchTemplate();
    }
  }, [params.projectId]);

  const handleTextChange = (layerId: string, value: string) => {
    setCustomizations(prev => ({
      ...prev,
      [layerId]: value,
    }));
  };

  const handleImageUpload = async (layerId: string, file: File) => {
    // Create object URL for immediate preview
    const imageUrl = URL.createObjectURL(file);

    // Update customizations immediately
    setCustomizations(prev => ({
      ...prev,
      [layerId]: imageUrl,
    }));

    // In a real implementation, you'd upload to your storage service here
    // const uploadedUrl = await uploadToStorage(file);
    // setCustomizations(prev => ({ ...prev, [layerId]: uploadedUrl }));
  };

  // Initialize canvas when template is loaded
  useEffect(() => {
    if (!template || !canvasRef.current) return;

    // Clean up existing canvas safely
    if (fabricCanvasRef.current) {
      try {
        fabricCanvasRef.current.dispose();
      } catch (error) {
        console.warn('Error disposing canvas:', error);
      }
      fabricCanvasRef.current = null;
    }

    // Initialize canvas with proper error handling
    const initCanvas = async () => {
      if (!canvasRef.current || !template) return;

      try {
        // Ensure canvas element is properly mounted
        const canvasElement = canvasRef.current;
        if (!canvasElement.getContext) {
          console.warn('Canvas element not ready');
          return;
        }

        // Test canvas context availability
        const testContext = canvasElement.getContext('2d');
        if (!testContext) {
          console.warn('Canvas 2D context not available');
          return;
        }

        // Create new fabric canvas with proper sizing
        const canvas = new fabric.Canvas(canvasElement, {
          width: template.width,
          height: template.height,
          selection: false,
          preserveObjectStacking: true,
        });

        // Verify canvas was created successfully
        if (!canvas.getContext() || !canvas.getElement()) {
          console.warn('Fabric canvas not properly initialized');
          canvas.dispose();
          return;
        }

        fabricCanvasRef.current = canvas;
        setCanvasInitialized(true);

        // Load template JSON with error handling
        try {
          const templateJson = JSON.parse(template.json);

          // Use a promise-based approach for loadFromJSON
          await new Promise<void>((resolve, reject) => {
            canvas.loadFromJSON(templateJson, () => {
              try {
                if (!fabricCanvasRef.current) {
                  reject(new Error('Canvas disposed during loading'));
                  return;
                }

                // Ensure canvas is properly sized and rendered
                fabricCanvasRef.current.setDimensions({
                  width: template.width,
                  height: template.height
                });
                fabricCanvasRef.current.renderAll();
                generatePreviewFromCanvas();
                resolve();
              } catch (error) {
                reject(error);
              }
            }, (error: any) => {
              reject(error);
            });
          });
        } catch (jsonError) {
          console.error('Failed to load template JSON:', jsonError);
          setCanvasInitialized(false);
          // Fallback to template thumbnail
          if (template.thumbnailUrl) {
            setPreviewUrl(template.thumbnailUrl);
          }
        }
      } catch (error) {
        console.error('Failed to initialize canvas:', error);
        // Fallback to template thumbnail
        if (template.thumbnailUrl) {
          setPreviewUrl(template.thumbnailUrl);
        }
      }
    };

    // Use multiple timeouts to ensure proper initialization
    const timeoutId = setTimeout(() => {
      initCanvas();
    }, 200);

    return () => {
      clearTimeout(timeoutId);
      setCanvasInitialized(false);
      if (fabricCanvasRef.current) {
        try {
          fabricCanvasRef.current.dispose();
        } catch (error) {
          console.warn('Error disposing canvas in cleanup:', error);
        }
        fabricCanvasRef.current = null;
      }
    };
  }, [template]);

  // Generate preview from canvas
  const generatePreviewFromCanvas = useCallback(() => {
    if (!fabricCanvasRef.current) {
      // Fallback to template thumbnail if no canvas
      if (template?.thumbnailUrl) {
        setPreviewUrl(template.thumbnailUrl);
      }
      return;
    }

    try {
      // Check if canvas is properly initialized
      const canvas = fabricCanvasRef.current;
      if (!canvas.getContext || !canvas.getElement()) {
        console.warn('Canvas not properly initialized');
        // Fallback to template thumbnail
        if (template?.thumbnailUrl) {
          setPreviewUrl(template.thumbnailUrl);
        }
        return;
      }

      // Additional check for canvas context
      const context = canvas.getContext();
      if (!context) {
        console.warn('Canvas context not available');
        if (template?.thumbnailUrl) {
          setPreviewUrl(template.thumbnailUrl);
        }
        return;
      }

      const dataURL = canvas.toDataURL({
        format: 'png',
        quality: 0.8,
        multiplier: 1,
      });
      setPreviewUrl(dataURL);
    } catch (error) {
      console.error('Failed to generate preview:', error);
      // Fallback to template thumbnail if preview generation fails
      if (template?.thumbnailUrl) {
        setPreviewUrl(template.thumbnailUrl);
      }
    }
  }, [template]);

  // Apply customizations to canvas
  useEffect(() => {
    if (!fabricCanvasRef.current || !template || !canvasInitialized) {
      // If canvas is not available, just show template thumbnail with a note about customizations
      if (template?.thumbnailUrl && Object.keys(customizations).length > 0) {
        // For now, fallback to original thumbnail
        // In a production app, you might want to show a message about customizations being applied
        setPreviewUrl(template.thumbnailUrl);
      }
      return;
    }

    const canvas = fabricCanvasRef.current;

    // Check if canvas is properly initialized
    if (!canvas.getContext || !canvas.getElement()) {
      console.warn('Canvas not ready for customizations');
      return;
    }

    try {
      // Apply customizations
      Object.entries(customizations).forEach(([layerId, value]) => {
        const object = canvas.getObjects().find((obj: any) => obj.id === layerId);
        if (object) {
          if (object.type === 'textbox') {
            // Apply text customization
            (object as fabric.Textbox).set('text', value);
          } else if (object.type === 'image' || object.type === 'rect' || object.type === 'circle') {
            // Apply image customization
            if (value && value.startsWith('blob:')) {
              fabric.Image.fromURL(value, (img) => {
                if (!fabricCanvasRef.current) return; // Check if canvas still exists

                try {
                  // Scale image to fit the object bounds
                  const scaleX = object.width! / img.width!;
                  const scaleY = object.height! / img.height!;
                  const scale = Math.min(scaleX, scaleY);

                  img.set({
                    left: object.left,
                    top: object.top,
                    scaleX: scale,
                    scaleY: scale,
                    originX: object.originX,
                    originY: object.originY,
                  });

                  // Replace the object with the new image
                  fabricCanvasRef.current.remove(object);
                  fabricCanvasRef.current.add(img);
                  fabricCanvasRef.current.renderAll();
                  generatePreviewFromCanvas();
                } catch (error) {
                  console.error('Error applying image customization:', error);
                }
              });
              return; // Skip the renderAll below since it's handled in the callback
            }
          }
        }
      });

      canvas.renderAll();
      generatePreviewFromCanvas();
    } catch (error) {
      console.error('Error applying customizations:', error);
    }
  }, [customizations, template, generatePreviewFromCanvas]);

  const generatePreview = async () => {
    setIsGeneratingPreview(true);
    try {
      // Generate preview from current canvas state
      generatePreviewFromCanvas();
      // Small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (err) {
      console.error('Failed to generate preview:', err);
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const downloadCustomized = async () => {
    setIsDownloading(true);
    try {
      // This would call an API to generate and download the customized design
      // For now, we'll simulate it
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // In a real implementation, you'd get a download URL from the API
      const link = document.createElement('a');
      link.href = template?.thumbnailUrl || '';
      link.download = `customized-${template?.name || 'design'}.png`;
      link.click();
    } catch (err) {
      console.error('Failed to download:', err);
    } finally {
      setIsDownloading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
      </div>
    );
  }

  if (error || !template) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || "Template not found"}
            </h1>
            <Button onClick={() => router.push("/public")} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Gallery
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                onClick={() => router.push(`/public/${params.projectId}`)} 
                variant="ghost" 
                size="sm"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Customize: {template.name}
                </h1>
                <p className="text-sm text-gray-500">
                  Make this template your own
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={generatePreview}
                disabled={isGeneratingPreview}
                variant="outline"
              >
                {isGeneratingPreview ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                Preview
              </Button>
              <Button
                onClick={downloadCustomized}
                disabled={isDownloading}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isDownloading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Download
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Customization Panel */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Customize Elements</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {template.editableLayers.map((layer) => (
                  <div key={layer.id} className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Badge variant={layer.type === 'text' ? 'default' : 'secondary'}>
                        {layer.type === 'text' ? (
                          <Type className="h-3 w-3 mr-1" />
                        ) : (
                          <ImageIcon className="h-3 w-3 mr-1" />
                        )}
                        {layer.type}
                      </Badge>
                      <span className="font-medium text-sm">{layer.name}</span>
                    </div>

                    {layer.type === 'text' ? (
                      <div>
                        <Label className="text-sm text-gray-600">
                          {layer.placeholder || 'Enter text'}
                        </Label>
                        <Input
                          value={customizations[layer.id] || ''}
                          onChange={(e) => handleTextChange(layer.id, e.target.value)}
                          placeholder={layer.placeholder}
                          maxLength={layer.constraints?.maxLength}
                          className="mt-1"
                        />
                        {layer.constraints?.maxLength && (
                          <p className="text-xs text-gray-500 mt-1">
                            {customizations[layer.id]?.length || 0}/{layer.constraints.maxLength} characters
                          </p>
                        )}
                      </div>
                    ) : (
                      <div>
                        <Label className="text-sm text-gray-600">
                          Upload your image
                        </Label>
                        <div className="mt-1 space-y-3">
                          <input
                            type="file"
                            accept={layer.constraints?.allowedFormats?.map(f => `.${f}`).join(',') || 'image/*'}
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                handleImageUpload(layer.id, file);
                              }
                            }}
                            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
                          />

                          {/* Small preview of uploaded image */}
                          {customizations[layer.id] && (
                            <div className="flex items-center space-x-2">
                              <img
                                src={customizations[layer.id]}
                                alt="Uploaded preview"
                                className="w-12 h-12 object-cover rounded border"
                              />
                              <div className="flex-1">
                                <p className="text-xs text-gray-600">Image uploaded</p>
                                <button
                                  onClick={() => {
                                    setCustomizations(prev => {
                                      const updated = { ...prev };
                                      delete updated[layer.id];
                                      return updated;
                                    });
                                  }}
                                  className="text-xs text-red-500 hover:text-red-700"
                                >
                                  Remove
                                </button>
                              </div>
                            </div>
                          )}

                          {layer.constraints?.maxFileSize && (
                            <p className="text-xs text-gray-500">
                              Max size: {Math.round(layer.constraints.maxFileSize / 1024 / 1024)}MB
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center bg-gray-100 rounded-lg p-4">
                  <div
                    className="bg-white rounded-lg shadow-lg overflow-hidden flex items-center justify-center"
                    style={{
                      aspectRatio: `${template.width}/${template.height}`,
                      maxWidth: "100%",
                      maxHeight: "500px",
                      width: "auto",
                      height: "auto"
                    }}
                  >
                    {previewUrl || template.thumbnailUrl ? (
                      <img
                        src={previewUrl || template.thumbnailUrl || ''}
                        alt="Template preview"
                        className="max-w-full max-h-full object-contain"
                        style={{
                          width: "auto",
                          height: "auto",
                          maxWidth: "100%",
                          maxHeight: "100%"
                        }}
                      />
                    ) : (
                      <div className="flex items-center justify-center bg-gray-50 w-full h-full min-h-[200px]">
                        <ImageIcon className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Hidden canvas for preview generation */}
      <canvas
        ref={canvasRef}
        style={{ display: 'none' }}
        width={template?.width || 800}
        height={template?.height || 600}
      />
    </div>
  );
}
