"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated, activeLayerId: externalActiveLayerId, onLayerActivation } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeLayerId, setActiveLayerId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(externalActiveLayerId || null);\n    // Sync with external active layer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setActiveLayerId(externalActiveLayerId || null);\n    }, [\n        externalActiveLayerId\n    ]);\n    // Initialize editor with no-save callback since this is read-only customization\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{},\n        saveCallback: ()=>{}\n    });\n    // Initialize canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (retryCanvas) {\n                    setIsInitialized(true);\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        } else {\n            setIsInitialized(true);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Lock non-editable objects and setup customization mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up customization mode:\", {\n            editableLayerIds,\n            totalObjects: canvas.getObjects().length,\n            editableLayers: templateData.editableLayers\n        });\n        // Lock all objects except editable ones\n        canvas.getObjects().forEach((obj)=>{\n            console.log(\"Processing object:\", {\n                id: obj.id,\n                type: obj.type,\n                name: obj.name,\n                isEditable: editableLayerIds.includes(obj.id)\n            });\n            if (obj.name === \"clip\") {\n                // Keep workspace as is but make it non-selectable\n                obj.set({\n                    selectable: false,\n                    evented: false\n                });\n                return;\n            }\n            if (editableLayerIds.includes(obj.id)) {\n                console.log(\"Making object editable:\", obj.id, obj.type);\n                // Make editable objects selectable and editable\n                obj.set({\n                    selectable: true,\n                    hasControls: false,\n                    hasBorders: true,\n                    borderColor: \"#3b82f6\",\n                    borderScaleFactor: 2,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true,\n                    editable: obj.type === \"textbox\" ? true : false,\n                    hoverCursor: \"pointer\",\n                    moveCursor: \"pointer\"\n                });\n                // Add visual indicator for editable elements\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: \"rgba(59, 130, 246, 0.1)\"\n                    });\n                }\n            } else {\n                // Lock non-editable objects completely\n                obj.set({\n                    selectable: false,\n                    evented: false,\n                    lockMovementX: true,\n                    lockMovementY: true,\n                    lockRotation: true,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n            }\n        });\n        canvas.renderAll();\n    }, [\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Update visual feedback for editable objects\n    const updateEditableObjectsVisuals = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        editor.canvas.getObjects().forEach((obj)=>{\n            if (editableLayerIds.includes(obj.id)) {\n                const isActive = obj.id === activeLayerId;\n                if (obj.type === \"textbox\") {\n                    obj.set({\n                        backgroundColor: isActive ? \"rgba(59, 130, 246, 0.2)\" // Darker blue when active\n                         : \"rgba(59, 130, 246, 0.1)\",\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2\n                    });\n                } else {\n                    // For image layers\n                    obj.set({\n                        borderColor: isActive ? \"#3b82f6\" : \"#94a3b8\",\n                        borderScaleFactor: isActive ? 3 : 2,\n                        opacity: isActive ? 1 : 0.8\n                    });\n                }\n            }\n        });\n        editor.canvas.renderAll();\n    }, [\n        editor,\n        templateData.editableLayers,\n        activeLayerId\n    ]);\n    // Update visuals when active layer changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateEditableObjectsVisuals();\n    }, [\n        activeLayerId,\n        updateEditableObjectsVisuals\n    ]);\n    // Apply customizations to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas) || !isInitialized) return;\n        const canvas = editor.canvas;\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            if (!customValue || customValue === layer.originalValue) return;\n            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) return;\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                canvasObject.set(\"text\", customValue);\n            } else if (layer.type === \"image\" && customValue.startsWith(\"blob:\")) {\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!canvas) return;\n                    // Scale image to fit the object bounds\n                    const scaleX = canvasObject.width / img.width;\n                    const scaleY = canvasObject.height / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    img.set({\n                        left: canvasObject.left,\n                        top: canvasObject.top,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX,\n                        originY: canvasObject.originY,\n                        id: layer.id,\n                        selectable: true,\n                        hasControls: false,\n                        lockMovementX: true,\n                        lockMovementY: true,\n                        lockRotation: true,\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true\n                    });\n                    canvas.remove(canvasObject);\n                    canvas.add(img);\n                    canvas.renderAll();\n                    generatePreview();\n                });\n                return;\n            }\n        });\n        canvas.renderAll();\n        generatePreview();\n    }, [\n        customizations,\n        editor,\n        isInitialized,\n        templateData.editableLayers\n    ]);\n    // Generate preview from canvas\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            // Create a temporary canvas with just the workspace content\n            const tempCanvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(null, {\n                width: workspace.width,\n                height: workspace.height\n            });\n            // Clone all objects except the workspace itself\n            const objectsToClone = editor.canvas.getObjects().filter((obj)=>obj.name !== \"clip\");\n            objectsToClone.forEach((obj)=>{\n                obj.clone((cloned)=>{\n                    // Adjust position relative to workspace\n                    cloned.set({\n                        left: (cloned.left || 0) - (workspace.left || 0),\n                        top: (cloned.top || 0) - (workspace.top || 0)\n                    });\n                    tempCanvas.add(cloned);\n                });\n            });\n            // Generate preview after a short delay to ensure all objects are added\n            setTimeout(()=>{\n                const dataUrl = tempCanvas.toDataURL({\n                    format: \"png\",\n                    quality: 0.9,\n                    multiplier: 1\n                });\n                onPreviewGenerated(dataUrl);\n                tempCanvas.dispose();\n            }, 100);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Handle text editing\n    const handleTextEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((layerId, value)=>{\n        onCustomizationChange(layerId, value);\n    }, [\n        onCustomizationChange\n    ]);\n    // Handle click events and layer activation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleObjectSelection = (e)=>{\n            const target = e.target;\n            if (!target) {\n                setActiveLayerId(null);\n                return;\n            }\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer) {\n                console.log(\"Layer activated:\", layer);\n                setActiveLayerId(layerId);\n                // Update visual feedback for all editable objects\n                updateEditableObjectsVisuals();\n            } else {\n                setActiveLayerId(null);\n            }\n        };\n        const handleCanvasClick = (e)=>{\n            if (!e.target) {\n                setActiveLayerId(null);\n                updateEditableObjectsVisuals();\n            }\n        };\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (!layer || layer.type !== \"text\") return;\n            const currentText = target.text || \"\";\n            handleTextEdit(layerId, currentText);\n            generatePreview();\n        };\n        const handleDoubleClick = (e)=>{\n            const target = e.target;\n            if (!target || target.type !== \"textbox\") return;\n            const layerId = target.id;\n            const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n            if (layer && layer.type === \"text\") {\n                setActiveLayerId(layerId);\n                // Enter editing mode\n                target.enterEditing();\n                target.selectAll();\n            }\n        };\n        editor.canvas.on(\"selection:created\", handleObjectSelection);\n        editor.canvas.on(\"selection:updated\", handleObjectSelection);\n        editor.canvas.on(\"selection:cleared\", ()=>setActiveLayerId(null));\n        editor.canvas.on(\"mouse:down\", handleCanvasClick);\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        editor.canvas.on(\"mouse:dblclick\", handleDoubleClick);\n        return ()=>{\n            editor.canvas.off(\"selection:created\", handleObjectSelection);\n            editor.canvas.off(\"selection:updated\", handleObjectSelection);\n            editor.canvas.off(\"selection:cleared\", ()=>setActiveLayerId(null));\n            editor.canvas.off(\"mouse:down\", handleCanvasClick);\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n            editor.canvas.off(\"mouse:dblclick\", handleDoubleClick);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        handleTextEdit,\n        generatePreview\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    ref: containerRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 394,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"ReGyCtJ/TVgJB/jkwtT/sRPNUGM=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});