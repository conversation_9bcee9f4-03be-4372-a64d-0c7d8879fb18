"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts":
/*!**************************************************************!*\
  !*** ./src/features/editor/hooks/use-thumbnail-generator.ts ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useThumbnailGenerator: function() { return /* binding */ useThumbnailGenerator; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _lib_hono__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hono */ \"(app-pages-browser)/./src/lib/hono.ts\");\n\n\n\n\nconst useThumbnailGenerator = (param)=>{\n    let { editor, projectId, onThumbnailGenerated } = param;\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const lastThumbnailRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const generateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!editor) {\n            console.warn(\"No editor available for thumbnail generation\");\n            return;\n        }\n        if (!editor.canvas) {\n            console.warn(\"No canvas available for thumbnail generation\");\n            return;\n        }\n        try {\n            console.log(\"Generating thumbnail for project:\", projectId);\n            // Generate thumbnail data URL with smaller size for testing\n            const thumbnailDataUrl = editor.generateThumbnail({\n                width: 300,\n                height: 200,\n                quality: 0.8\n            });\n            if (!thumbnailDataUrl) {\n                console.error(\"Failed to generate thumbnail data URL\");\n                return;\n            }\n            console.log(\"Generated thumbnail data URL length:\", thumbnailDataUrl.length);\n            console.log(\"Thumbnail data URL preview:\", thumbnailDataUrl.substring(0, 100) + \"...\");\n            // Test if data URL is valid\n            const isValidDataUrl = thumbnailDataUrl.startsWith(\"data:image/\");\n            console.log(\"Is valid data URL:\", isValidDataUrl);\n            // Test if we can create an image from it\n            const testImg = new Image();\n            testImg.onload = ()=>console.log(\"Data URL is valid image\");\n            testImg.onerror = (e)=>console.error(\"Data URL is invalid:\", e);\n            testImg.src = thumbnailDataUrl;\n            // Skip if thumbnail hasn't changed significantly\n            if (lastThumbnailRef.current === thumbnailDataUrl) {\n                console.log(\"Thumbnail unchanged, skipping update\");\n                return;\n            }\n            lastThumbnailRef.current = thumbnailDataUrl;\n            console.log(\"Thumbnail generated, updating project...\");\n            // For now, let's store the thumbnail as a data URL directly in the database\n            // This is simpler and doesn't require external file uploads\n            // In production, you might want to upload to a CDN\n            // Update project with thumbnail data URL\n            const response = await _lib_hono__WEBPACK_IMPORTED_MODULE_2__.client.api.projects[\":id\"].$patch({\n                param: {\n                    id: projectId\n                },\n                json: {\n                    thumbnailUrl: thumbnailDataUrl\n                }\n            });\n            if (response.ok) {\n                console.log(\"Thumbnail updated successfully\");\n                const responseData = await response.json();\n                console.log(\"Response data:\", responseData);\n                onThumbnailGenerated === null || onThumbnailGenerated === void 0 ? void 0 : onThumbnailGenerated(thumbnailDataUrl);\n            } else {\n                console.error(\"Failed to update thumbnail:\", response.status, response.statusText);\n                const errorText = await response.text();\n                console.error(\"Error response:\", errorText);\n            }\n        } catch (error) {\n            console.error(\"Error generating thumbnail:\", error);\n        }\n    }, [\n        editor,\n        projectId,\n        onThumbnailGenerated\n    ]);\n    // Debounced version to avoid too frequent thumbnail generation\n    const debouncedGenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default()(generateThumbnail, 2000), [\n        generateThumbnail\n    ]);\n    const forceRegenerateThumbnail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        // Reset the last thumbnail to force regeneration\n        lastThumbnailRef.current = null;\n        await generateThumbnail();\n    }, [\n        generateThumbnail\n    ]);\n    return {\n        generateThumbnail,\n        debouncedGenerateThumbnail,\n        forceRegenerateThumbnail\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\n"));

/***/ })

});