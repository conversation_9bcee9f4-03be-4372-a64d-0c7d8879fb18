"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/editor.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Editor: function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-thumbnail-generator */ \"(app-pages-browser)/./src/features/editor/hooks/use-thumbnail-generator.ts\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/components/navbar */ \"(app-pages-browser)/./src/features/editor/components/navbar.tsx\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/components/sidebar */ \"(app-pages-browser)/./src/features/editor/components/sidebar.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* harmony import */ var _features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/editor/components/shape-sidebar */ \"(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/editor/components/fill-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/fill-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/features/editor/components/stroke-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/editor/components/stroke-width-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-width-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/features/editor/components/opacity-sidebar */ \"(app-pages-browser)/./src/features/editor/components/opacity-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/features/editor/components/text-sidebar */ \"(app-pages-browser)/./src/features/editor/components/text-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/editor/components/font-sidebar */ \"(app-pages-browser)/./src/features/editor/components/font-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/editor/components/image-sidebar */ \"(app-pages-browser)/./src/features/editor/components/image-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/editor/components/filter-sidebar */ \"(app-pages-browser)/./src/features/editor/components/filter-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/editor/components/draw-sidebar */ \"(app-pages-browser)/./src/features/editor/components/draw-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/editor/components/ai-sidebar */ \"(app-pages-browser)/./src/features/editor/components/ai-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/editor/components/template-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/features/editor/components/remove-bg-sidebar */ \"(app-pages-browser)/./src/features/editor/components/remove-bg-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/editor/components/settings-sidebar */ \"(app-pages-browser)/./src/features/editor/components/settings-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/editor/components/template-config-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Editor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Editor = (param)=>{\n    let { initialData, initialActiveTool = \"select\" } = param;\n    _s();\n    const { mutate } = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject)(initialData.id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()((values)=>{\n        mutate(values);\n    }, 3000 // Increased from 500ms to 3 seconds\n    ), [\n        mutate\n    ]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(initialActiveTool);\n    const onClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        if (_features_editor_types__WEBPACK_IMPORTED_MODULE_6__.selectionDependentTools.includes(activeTool)) {\n            setActiveTool(\"select\");\n        }\n    }, [\n        activeTool\n    ]);\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor)({\n        defaultState: initialData.json,\n        defaultWidth: initialData.width,\n        defaultHeight: initialData.height,\n        clearSelectionCallback: onClearSelection,\n        saveCallback: debouncedSave\n    });\n    const handleManualSave = ()=>{\n        if (editor === null || editor === void 0 ? void 0 : editor.canvas) {\n            const workspace = editor.canvas.getObjects().find((object)=>object.name === \"clip\");\n            const height = (workspace === null || workspace === void 0 ? void 0 : workspace.height) || initialData.height;\n            const width = (workspace === null || workspace === void 0 ? void 0 : workspace.width) || initialData.width;\n            const json = JSON.stringify(editor.canvas.toJSON());\n            mutate({\n                json,\n                height,\n                width\n            });\n        }\n    };\n    // Generate thumbnails automatically when the canvas changes\n    const { debouncedGenerateThumbnail, forceRegenerateThumbnail } = (0,_features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator)({\n        editor,\n        projectId: initialData.id\n    });\n    // Trigger thumbnail generation when canvas changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (editor === null || editor === void 0 ? void 0 : editor.canvas) {\n            const handleCanvasChange = ()=>{\n                debouncedGenerateThumbnail();\n            };\n            const canvas = editor.canvas;\n            canvas.on(\"object:added\", handleCanvasChange);\n            canvas.on(\"object:removed\", handleCanvasChange);\n            canvas.on(\"object:modified\", handleCanvasChange);\n            canvas.on(\"path:created\", handleCanvasChange);\n            // Generate initial thumbnail when editor is ready\n            const generateInitialThumbnail = ()=>{\n                setTimeout(()=>{\n                    console.log(\"Generating initial thumbnail...\");\n                    debouncedGenerateThumbnail();\n                }, 1000); // Wait a bit for canvas to be fully ready\n            };\n            generateInitialThumbnail();\n            return ()=>{\n                canvas.off(\"object:added\", handleCanvasChange);\n                canvas.off(\"object:removed\", handleCanvasChange);\n                canvas.off(\"object:modified\", handleCanvasChange);\n                canvas.off(\"path:created\", handleCanvasChange);\n            };\n        }\n    }, [\n        editor,\n        debouncedGenerateThumbnail\n    ]);\n    // Trigger thumbnail generation when editor changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (editor) {\n            debouncedGenerateThumbnail();\n        }\n    }, [\n        editor,\n        debouncedGenerateThumbnail\n    ]);\n    const onChangeActiveTool = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((tool)=>{\n        if (tool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.enableDrawingMode();\n        }\n        if (activeTool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.disableDrawingMode();\n        }\n        if (tool === activeTool) {\n            return setActiveTool(\"select\");\n        }\n        setActiveTool(tool);\n    }, [\n        activeTool,\n        editor\n    ]);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            // Ensure container is available and has dimensions\n            if (!containerRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            // Don't initialize if container has no dimensions\n            if (containerWidth === 0 || containerHeight === 0) {\n                console.warn(\"Container has zero dimensions, delaying canvas initialization\");\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_1__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        // Try to initialize immediately\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            // If initialization failed, retry after a short delay\n            const timeoutId = setTimeout(()=>{\n                const retryCanvas = initializeCanvas();\n                if (!retryCanvas) {\n                    console.error(\"Failed to initialize canvas after retry\");\n                }\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        }\n        return ()=>{\n            canvas.dispose();\n        };\n    }, [\n        init\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_7__.Navbar, {\n                id: initialData.id,\n                editor: editor,\n                activeTool: activeTool,\n                onChangeActiveTool: onChangeActiveTool,\n                onSave: handleManualSave,\n                initialData: {\n                    name: initialData.name,\n                    isCustomizable: initialData.isCustomizable\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute h-[calc(100%-68px)] w-full top-[68px] flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_12__.ShapeSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_13__.FillColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_14__.StrokeColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_15__.StrokeWidthSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_16__.OpacitySidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_17__.TextSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_18__.FontSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_19__.ImageSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_23__.TemplateSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_20__.FilterSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_22__.AiSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_24__.RemoveBgSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_21__.DrawSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_25__.SettingsSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_config_sidebar__WEBPACK_IMPORTED_MODULE_26__.TemplateConfigSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool,\n                        projectId: initialData.id,\n                        initialData: {\n                            isCustomizable: initialData.isCustomizable,\n                            editableLayers: initialData.editableLayers\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-muted flex-1 overflow-auto relative flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_11__.Toolbar, {\n                                editor: editor,\n                                activeTool: activeTool,\n                                onChangeActiveTool: onChangeActiveTool\n                            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                                ref: containerRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                    ref: canvasRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_8__.Footer, {\n                                editor: editor\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Editor, \"4mKcfUiWcH97psEe8aPkKs+t6DM=\", false, function() {\n    return [\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject,\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_9__.useEditor,\n        _features_editor_hooks_use_thumbnail_generator__WEBPACK_IMPORTED_MODULE_5__.useThumbnailGenerator\n    ];\n});\n_c = Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx\n"));

/***/ })

});