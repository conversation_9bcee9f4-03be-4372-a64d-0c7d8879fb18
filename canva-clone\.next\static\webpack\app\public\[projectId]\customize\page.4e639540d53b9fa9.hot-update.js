"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/public/[projectId]/customize/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/customization-editor.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/customization-editor.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomizationEditor: function() { return /* binding */ CustomizationEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomizationEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CustomizationEditor = (param)=>{\n    let { templateData, customizations, onCustomizationChange, onPreviewGenerated, activeLayerId: externalActiveLayerId, onLayerActivation } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize editor first\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor)({\n        defaultState: templateData.json,\n        defaultWidth: templateData.width,\n        defaultHeight: templateData.height,\n        clearSelectionCallback: ()=>{\n            onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n        },\n        saveCallback: ()=>{\n            // Generate preview when canvas changes\n            generatePreview();\n        }\n    });\n    // Generate preview from canvas (defined after editor)\n    const generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        try {\n            const workspace = editor.canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            const dataUrl = editor.canvas.toDataURL({\n                format: \"png\",\n                quality: 0.9,\n                multiplier: 0.5\n            });\n            onPreviewGenerated(dataUrl);\n        } catch (error) {\n            console.error(\"Failed to generate preview:\", error);\n        }\n    }, [\n        editor,\n        onPreviewGenerated\n    ]);\n    // Initialize canvas (same as main editor)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCanvas = ()=>{\n            if (!containerRef.current || !canvasRef.current) return false;\n            const container = containerRef.current;\n            const containerWidth = container.offsetWidth;\n            const containerHeight = container.offsetHeight;\n            if (containerWidth === 0 || containerHeight === 0) {\n                return false;\n            }\n            const canvas = new fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Canvas(canvasRef.current, {\n                controlsAboveOverlay: true,\n                preserveObjectStacking: true\n            });\n            init({\n                initialCanvas: canvas,\n                initialContainer: container\n            });\n            return canvas;\n        };\n        const canvas = initializeCanvas();\n        if (!canvas) {\n            const timeoutId = setTimeout(()=>{\n                initializeCanvas();\n            }, 100);\n            return ()=>clearTimeout(timeoutId);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.dispose();\n            }\n        };\n    }, [\n        init\n    ]);\n    // Handle selection changes to notify parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const handleSelectionCreated = (e)=>{\n            const target = e.target;\n            if (target) {\n                const layerId = target.id;\n                const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n                if (layer) {\n                    onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(layerId);\n                }\n            }\n        };\n        const handleSelectionCleared = ()=>{\n            onLayerActivation === null || onLayerActivation === void 0 ? void 0 : onLayerActivation(null);\n        };\n        const handleTextChanged = (e)=>{\n            const target = e.target;\n            if (target && target.type === \"textbox\") {\n                const layerId = target.id;\n                const layer = templateData.editableLayers.find((l)=>l.id === layerId);\n                if (layer && layer.type === \"text\") {\n                    const currentText = target.text || \"\";\n                    onCustomizationChange(layerId, currentText);\n                }\n            }\n        };\n        editor.canvas.on(\"selection:created\", handleSelectionCreated);\n        editor.canvas.on(\"selection:updated\", handleSelectionCreated);\n        editor.canvas.on(\"selection:cleared\", handleSelectionCleared);\n        editor.canvas.on(\"text:changed\", handleTextChanged);\n        return ()=>{\n            editor.canvas.off(\"selection:created\", handleSelectionCreated);\n            editor.canvas.off(\"selection:updated\", handleSelectionCreated);\n            editor.canvas.off(\"selection:cleared\", handleSelectionCleared);\n            editor.canvas.off(\"text:changed\", handleTextChanged);\n        };\n    }, [\n        editor,\n        templateData.editableLayers,\n        onLayerActivation,\n        onCustomizationChange\n    ]);\n    // Apply customizations from sidebar to canvas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        console.log(\"Applying customizations:\", customizations);\n        templateData.editableLayers.forEach((layer)=>{\n            const customValue = customizations[layer.id];\n            console.log(\"Processing layer \".concat(layer.id, \" (\").concat(layer.type, \"):\"), customValue);\n            const canvasObject = editor.canvas.getObjects().find((obj)=>obj.id === layer.id);\n            if (!canvasObject) {\n                console.log(\"Canvas object not found for layer \".concat(layer.id));\n                return;\n            }\n            if (layer.type === \"text\" && canvasObject.type === \"textbox\") {\n                const textbox = canvasObject;\n                console.log('Current text: \"'.concat(textbox.text, '\", New text: \"').concat(customValue, '\"'));\n                if (customValue && textbox.text !== customValue) {\n                    console.log(\"Updating text for \".concat(layer.id, ' to: \"').concat(customValue, '\"'));\n                    textbox.set(\"text\", customValue);\n                    editor.canvas.renderAll();\n                    // Trigger save to update the canvas state\n                    if (editor.canvas.fire) {\n                        editor.canvas.fire(\"text:changed\", {\n                            target: textbox\n                        });\n                    }\n                }\n            } else if (layer.type === \"image\" && customValue) {\n                console.log(\"Replacing image for \".concat(layer.id, \" with: \").concat(customValue));\n                // Handle image replacement\n                fabric__WEBPACK_IMPORTED_MODULE_2__.fabric.Image.fromURL(customValue, (img)=>{\n                    if (!editor.canvas) return;\n                    console.log(\"Original image object:\", canvasObject);\n                    console.log(\"New image loaded:\", img);\n                    // Get the original object's properties\n                    const originalLeft = canvasObject.left || 0;\n                    const originalTop = canvasObject.top || 0;\n                    const originalWidth = canvasObject.width || 100;\n                    const originalHeight = canvasObject.height || 100;\n                    const originalScaleX = canvasObject.scaleX || 1;\n                    const originalScaleY = canvasObject.scaleY || 1;\n                    // Calculate scale to fit within original bounds\n                    const targetWidth = originalWidth * originalScaleX;\n                    const targetHeight = originalHeight * originalScaleY;\n                    const scaleX = targetWidth / img.width;\n                    const scaleY = targetHeight / img.height;\n                    const scale = Math.min(scaleX, scaleY);\n                    // Set the new image properties\n                    img.set({\n                        left: originalLeft,\n                        top: originalTop,\n                        scaleX: scale,\n                        scaleY: scale,\n                        originX: canvasObject.originX || \"left\",\n                        originY: canvasObject.originY || \"top\",\n                        selectable: true,\n                        hasControls: true,\n                        hasBorders: true\n                    });\n                    // Set the ID using a custom property\n                    img.id = layer.id;\n                    // Remove old image and add new one\n                    const objectIndex = editor.canvas.getObjects().indexOf(canvasObject);\n                    editor.canvas.remove(canvasObject);\n                    editor.canvas.insertAt(img, objectIndex);\n                    editor.canvas.setActiveObject(img);\n                    editor.canvas.renderAll();\n                    console.log(\"Image replaced for \".concat(layer.id), img);\n                }, {\n                    crossOrigin: \"anonymous\"\n                });\n            }\n        });\n    }, [\n        customizations,\n        editor,\n        templateData.editableLayers\n    ]);\n    // Ensure canvas objects have proper IDs when editor loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        const canvas = editor.canvas;\n        const editableLayerIds = templateData.editableLayers.map((layer)=>layer.id);\n        console.log(\"Setting up canvas object IDs for editable layers:\", editableLayerIds);\n        // Wait a bit for the canvas to be fully loaded\n        setTimeout(()=>{\n            const allObjects = canvas.getObjects();\n            console.log(\"All canvas objects after load:\", allObjects.map((obj)=>({\n                    id: obj.id,\n                    type: obj.type,\n                    name: obj.name,\n                    text: obj.type === \"textbox\" ? obj.text : undefined\n                })));\n            // Try to match objects to editable layers by content or position\n            templateData.editableLayers.forEach((layer)=>{\n                let matchedObject = allObjects.find((obj)=>obj.id === layer.id);\n                if (!matchedObject && layer.type === \"text\") {\n                    // Try to find by text content if no ID match\n                    matchedObject = allObjects.find((obj)=>obj.type === \"textbox\" && obj.text === layer.originalValue && !editableLayerIds.includes(obj.id));\n                }\n                if (matchedObject && !matchedObject.id) {\n                    console.log(\"Assigning ID \".concat(layer.id, \" to object:\"), matchedObject);\n                    matchedObject.id = layer.id;\n                }\n            });\n            canvas.renderAll();\n        }, 500);\n    }, [\n        editor,\n        templateData.editableLayers\n    ]);\n    // Function to select object by layer ID (called from parent)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(editor === null || editor === void 0 ? void 0 : editor.canvas)) return;\n        console.log(\"External active layer changed:\", externalActiveLayerId);\n        if (externalActiveLayerId) {\n            const allObjects = editor.canvas.getObjects();\n            console.log(\"All canvas objects:\", allObjects.map((obj)=>({\n                    id: obj.id,\n                    type: obj.type,\n                    name: obj.name\n                })));\n            const targetObject = allObjects.find((obj)=>obj.id === externalActiveLayerId);\n            console.log(\"Found target object:\", targetObject);\n            if (targetObject) {\n                console.log(\"Selecting object in canvas:\", targetObject.id);\n                editor.canvas.setActiveObject(targetObject);\n                editor.canvas.renderAll();\n            } else {\n                console.log(\"Target object not found for ID:\", externalActiveLayerId);\n            }\n        } else {\n            // Clear selection if no layer is active\n            console.log(\"Clearing canvas selection\");\n            editor.canvas.discardActiveObject();\n            editor.canvas.renderAll();\n        }\n    }, [\n        editor,\n        externalActiveLayerId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-muted\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_5__.Toolbar, {\n                editor: editor,\n                activeTool: \"select\",\n                onChangeActiveTool: ()=>{}\n            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                ref: containerRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {\n                editor: editor\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\customization-editor.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomizationEditor, \"xWIKbIYg0XVsBz7xgaDlw9unfJI=\", false, function() {\n    return [\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_3__.useEditor\n    ];\n});\n_c = CustomizationEditor;\nvar _c;\n$RefreshReg$(_c, \"CustomizationEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/customization-editor.tsx\n"));

/***/ })

});