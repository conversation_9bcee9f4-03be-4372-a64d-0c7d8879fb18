"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/navbar.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/navbar.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: function() { return /* binding */ Navbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CiFileOn!=!react-icons/ci */ \"(app-pages-browser)/./node_modules/react-icons/ci/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BsCloudCheck_BsCloudSlash_react_icons_bs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BsCloudCheck,BsCloudSlash!=!react-icons/bs */ \"(app-pages-browser)/./node_modules/react-icons/bs/index.mjs\");\n/* harmony import */ var use_file_picker__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-file-picker */ \"(app-pages-browser)/./node_modules/use-file-picker/dist/index.esm.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer-click.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Loader,MousePointerClick,Redo2,Save,Undo2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _features_auth_components_user_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/auth/components/user-button */ \"(app-pages-browser)/./src/features/auth/components/user-button.tsx\");\n/* harmony import */ var _features_editor_components_logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/logo */ \"(app-pages-browser)/./src/features/editor/components/logo.tsx\");\n/* harmony import */ var _features_editor_components_project_name__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/project-name */ \"(app-pages-browser)/./src/features/editor/components/project-name.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_hint__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/hint */ \"(app-pages-browser)/./src/components/hint.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Navbar = (param)=>{\n    let { id, editor, activeTool, onChangeActiveTool, onSave, initialData } = param;\n    _s();\n    const data = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutationState)({\n        filters: {\n            mutationKey: [\n                \"project\",\n                {\n                    id\n                }\n            ],\n            exact: true\n        },\n        select: (mutation)=>mutation.state.status\n    });\n    const currentStatus = data[data.length - 1];\n    const isError = currentStatus === \"error\";\n    const isPending = currentStatus === \"pending\";\n    const { openFilePicker } = (0,use_file_picker__WEBPACK_IMPORTED_MODULE_1__.useFilePicker)({\n        accept: \".json\",\n        onFilesSuccessfullySelected: (param)=>{\n            let { plainFiles } = param;\n            if (plainFiles && plainFiles.length > 0) {\n                const file = plainFiles[0];\n                const reader = new FileReader();\n                reader.readAsText(file, \"UTF-8\");\n                reader.onload = ()=>{\n                    editor === null || editor === void 0 ? void 0 : editor.loadJson(reader.result);\n                };\n            }\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"w-full flex items-center p-4 h-[68px] gap-x-8 border-b lg:pl-[34px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_logo__WEBPACK_IMPORTED_MODULE_3__.Logo, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex items-center gap-x-1 h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                        modal: false,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    children: [\n                                        \"File\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"size-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                align: \"start\",\n                                className: \"min-w-60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                    onClick: ()=>openFilePicker(),\n                                    className: \"flex items-center gap-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                            className: \"size-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Open\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Open a JSON file\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                        orientation: \"vertical\",\n                        className: \"mx-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_project_name__WEBPACK_IMPORTED_MODULE_4__.ProjectName, {\n                                id: id,\n                                name: initialData.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined),\n                            initialData.isCustomizable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                variant: \"secondary\",\n                                className: \"text-xs\",\n                                children: \"Template\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                        orientation: \"vertical\",\n                        className: \"mx-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Select\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>onChangeActiveTool(\"select\"),\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"select\" && \"bg-gray-100\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Undo\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            disabled: !(editor === null || editor === void 0 ? void 0 : editor.canUndo()),\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.onUndo(),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Redo\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            disabled: !(editor === null || editor === void 0 ? void 0 : editor.canRedo()),\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.onRedo(),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                        label: \"Save\",\n                        side: \"bottom\",\n                        sideOffset: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: onSave,\n                            disabled: !onSave,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                        orientation: \"vertical\",\n                        className: \"mx-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined),\n                    isPending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"size-4 animate-spin text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Saving...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, undefined),\n                    !isPending && isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsCloudCheck_BsCloudSlash_react_icons_bs__WEBPACK_IMPORTED_MODULE_19__.BsCloudSlash, {\n                                className: \"size-[20px] text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Failed to save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined),\n                    !isPending && !isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsCloudCheck_BsCloudSlash_react_icons_bs__WEBPACK_IMPORTED_MODULE_19__.BsCloudCheck, {\n                                className: \"size-[20px] text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Saved\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-auto flex items-center gap-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                                modal: false,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            size: \"sm\",\n                                            variant: \"ghost\",\n                                            children: [\n                                                \"Export\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Loader_MousePointerClick_Redo2_Save_Undo2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"size-4 ml-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"min-w-60\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.saveJson(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"JSON\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Save for later editing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.savePng(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"PNG\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Best for sharing on the web\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.saveJpg(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"JPG\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Best for printing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                className: \"flex items-center gap-x-2\",\n                                                onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.saveSvg(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiFileOn_react_icons_ci__WEBPACK_IMPORTED_MODULE_13__.CiFileOn, {\n                                                        className: \"size-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"SVG\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Best for editing in vector software\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_auth_components_user_button__WEBPACK_IMPORTED_MODULE_2__.UserButton, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\navbar.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"VCYRclSx01n/ShJ89dLY9EBkSlQ=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutationState,\n        use_file_picker__WEBPACK_IMPORTED_MODULE_1__.useFilePicker\n    ];\n});\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/navbar.tsx\n"));

/***/ })

});