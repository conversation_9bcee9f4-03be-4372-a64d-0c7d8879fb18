"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzP2FlNWQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb25cIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/editor/[projectId]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/editor/[projectId]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader_TriangleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader,TriangleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_Loader_TriangleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader,TriangleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _features_projects_api_use_get_project__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/projects/api/use-get-project */ \"(app-pages-browser)/./src/features/projects/api/use-get-project.ts\");\n/* harmony import */ var _features_editor_components_editor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/editor */ \"(app-pages-browser)/./src/features/editor/components/editor.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst EditorProjectIdPage = (param)=>{\n    let { params } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const mode = searchParams.get(\"mode\");\n    const { data, isLoading, isError } = (0,_features_projects_api_use_get_project__WEBPACK_IMPORTED_MODULE_3__.useGetProject)(params.projectId);\n    if (isLoading || !data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_TriangleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"size-6 animate-spin text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col gap-y-5 items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_TriangleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"size-6 text-muted-foreground\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground text-sm\",\n                    children: \"Failed to fetch project\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    asChild: true,\n                    variant: \"secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: \"/\",\n                        children: \"Back to Home\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_editor__WEBPACK_IMPORTED_MODULE_4__.Editor, {\n        initialData: data\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\editor\\\\[projectId]\\\\page.tsx\",\n        lineNumber: 54,\n        columnNumber: 10\n    }, undefined);\n};\n_s(EditorProjectIdPage, \"3RrzVSCSJMK4G/V+SXIvtFqfT5w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _features_projects_api_use_get_project__WEBPACK_IMPORTED_MODULE_3__.useGetProject\n    ];\n});\n_c = EditorProjectIdPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorProjectIdPage);\nvar _c;\n$RefreshReg$(_c, \"EditorProjectIdPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/editor/[projectId]/page.tsx\n"));

/***/ })

});