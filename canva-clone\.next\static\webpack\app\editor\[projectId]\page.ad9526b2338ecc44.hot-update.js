"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/projects/api/use-update-template-config.ts":
/*!*****************************************************************!*\
  !*** ./src/features/projects/api/use-update-template-config.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUpdateTemplateConfig: function() { return /* binding */ useUpdateTemplateConfig; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_hono__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/hono */ \"(app-pages-browser)/./src/lib/hono.ts\");\n\n\n\nconst useUpdateTemplateConfig = (id)=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const mutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: async (json)=>{\n            const response = await _lib_hono__WEBPACK_IMPORTED_MODULE_1__.client.api.projects[\":id\"][\"template-config\"].$patch({\n                param: {\n                    id\n                },\n                json\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update template configuration\");\n            }\n            return response.json();\n        },\n        onSuccess: (data)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success(\"Template configuration saved successfully!\");\n            // Invalidate and refetch projects list\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"projects\"\n                ]\n            });\n            // Update the specific project in cache\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"project\",\n                    {\n                        id\n                    }\n                ]\n            });\n            // Update the project data in cache immediately for better UX\n            queryClient.setQueryData([\n                \"project\",\n                {\n                    id\n                }\n            ], (oldData)=>{\n                if (oldData) {\n                    return {\n                        ...oldData,\n                        isCustomizable: data.data.isCustomizable,\n                        editableLayers: data.data.editableLayers\n                    };\n                }\n                return oldData;\n            });\n        },\n        onError: ()=>{\n            sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Failed to save template configuration\");\n        }\n    });\n    return mutation;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/projects/api/use-update-template-config.ts\n"));

/***/ })

});